import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class CustomDrawerItem extends StatelessWidget {
  final String icon;
  final String title;
  final VoidCallback? onTap;
  final List<Widget>? children;
  final bool useImage; // set true if using Image.asset instead of SVG

  const CustomDrawerItem({
    super.key,
    required this.icon,
    required this.title,
    required this.onTap,
    this.children,
    this.useImage = false,
  });

  @override
  Widget build(BuildContext context) {
    Widget leadingWidget = useImage
        ? Image.asset(
            icon,
            width: 20,
            height: 20,
          )
        : SvgPicture.asset(
            icon,
            width: 20,
            height: 20,
            colorFilter: const ColorFilter.mode(
              Colors.black87,
              BlendMode.srcIn,
            ),
          );

    if (children != null && children!.isNotEmpty) {
      return ExpansionTile(
        leading: leadingWidget,
        title: Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w400,
            fontFamily: 'inter',
            color: Colors.black87,
          ),
        ),
        tilePadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 0),
        childrenPadding: const EdgeInsets.only(left: 40),
        trailing: const Icon(
          Icons.keyboard_arrow_down,
          color: Colors.black87,
          size: 20,
        ),
        children: children!,
      );
    }

    return ListTile(
      minTileHeight: 35,
      minVerticalPadding: 0,
      horizontalTitleGap: 8,
      leading: leadingWidget,
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w400,
          fontFamily: 'inter',
           color: Colors.black87,
        ),
      ),
      onTap: onTap,
      contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 4),
    );
  }
}
