import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

/// A reusable navigation item widget for mobile library screens
/// Used across Books, Solutions, and Objects library screens
class MobileNavItem extends StatefulWidget {
  final String iconPath;
  final String label;
  final VoidCallback onTap;
  final bool isActive;

  const MobileNavItem({
    super.key,
    required this.iconPath,
    required this.label,
    required this.onTap,
    this.isActive = false,
  });

  @override
  State<MobileNavItem> createState() => _MobileNavItemState();
}

class _MobileNavItemState extends State<MobileNavItem> {
  bool isPressed = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => setState(() => isPressed = true),
      onTapUp: (_) => setState(() => isPressed = false),
      onTapCancel: () => setState(() => isPressed = false),
      onTap: widget.onTap,
      child: Container(
        width: MediaQuery.of(context).size.width * 0.2826,
        padding: EdgeInsets.symmetric(horizontal: 0, vertical: 8),
        decoration: BoxDecoration(
          color: widget.isActive
              ? const Color(0xff0058FF)
              : (isPressed ? Colors.white : const Color(0xffEBEBEB)),
          // border: Border.all(
          //   color: isPressed && !widget.isActive
          //       ? const Color(0xff0058FF)
          //       : Colors.transparent,
          //   width: 1.0,
          // ),
          borderRadius: BorderRadius.circular(6),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(
              widget.iconPath,
              width: 10,
              height: 10,
              colorFilter: ColorFilter.mode(
                widget.isActive ? Colors.white : Colors.black,
                BlendMode.srcIn,
              ),
            ),
            const SizedBox(width: 4),
            Text(
              widget.label,
              style: TextStyle(
                fontSize: 12,
                color: widget.isActive ? Colors.white : Colors.black,
                fontFamily: "TiemposText",
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
