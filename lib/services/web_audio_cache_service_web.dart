// Web-specific implementation for blob operations
import 'dart:html' as html;
import 'dart:typed_data';

/// Web-specific implementation for blob operations
class WebBlobOperations {
  /// Creates a blob URL from audio data
  static String createBlobUrl(Uint8List audioData, String mimeType) {
    final blob = html.Blob([audioData], mimeType);
    return html.Url.createObjectUrlFromBlob(blob);
  }

  /// Revokes a blob URL
  static void revokeBlobUrl(String blobUrl) {
    html.Url.revokeObjectUrl(blobUrl);
  }
}
