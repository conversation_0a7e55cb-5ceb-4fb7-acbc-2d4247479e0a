// To parse this JSON data, do
//
//     final chapterModel = chapterModelFromJson(jsonString);

import 'dart:convert';

List<ChapterModel> chapterModelFromJson(String str) => List<ChapterModel>.from(
    json.decode(str).map((x) => ChapterModel.fromJson(x)));

String chapterModelToJson(List<ChapterModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class ChapterModel {
  String? chapterId;
  String? chapterName;
  String? bookId;
  String? bookName;
  int? objectiveCount;

  // UI state fields for dynamic loading
  bool isLoading;
  String? errorMessage;
  bool isExpanded;
  bool isSelected;
  List<dynamic> children;

  ChapterModel({
    this.chapterId,
    this.chapterName,
    this.bookId,
    this.bookName,
    this.objectiveCount,
    this.isLoading = false,
    this.errorMessage,
    this.isExpanded = false,
    this.isSelected = false,
    this.children = const [],
  });

  ChapterModel copyWith({
    String? chapterId,
    String? chapterName,
    String? bookId,
    String? bookName,
    int? objectiveCount,
    bool? isLoading,
    String? errorMessage,
    bool? isExpanded,
    bool? isSelected,
    List<dynamic>? children,
  }) =>
      ChapterModel(
        chapterId: chapterId ?? this.chapterId,
        chapterName: chapterName ?? this.chapterName,
        bookId: bookId ?? this.bookId,
        bookName: bookName ?? this.bookName,
        objectiveCount: objectiveCount ?? this.objectiveCount,
        isLoading: isLoading ?? this.isLoading,
        errorMessage: errorMessage ?? this.errorMessage,
        isExpanded: isExpanded ?? this.isExpanded,
        isSelected: isSelected ?? this.isSelected,
        children: children ?? this.children,
      );

  factory ChapterModel.fromJson(Map<String, dynamic> json) => ChapterModel(
        chapterId: json["chapter_id"],
        chapterName: json["chapter_name"],
        bookId: json["book_id"],
        bookName: json["book_name"],
        objectiveCount: json["objective_count"],
        // UI state fields with default values (not from API)
        isLoading: false,
        errorMessage: null,
        isExpanded: false,
        isSelected: false,
        children: [],
      );

  Map<String, dynamic> toJson() => {
        "chapter_id": chapterId,
        "chapter_name": chapterName,
        "book_id": bookId,
        "book_name": bookName,
        "objective_count": objectiveCount,
        // UI state fields
        "is_loading": isLoading,
        "error_message": errorMessage,
        "is_expanded": isExpanded,
        "is_selected": isSelected,
        "children": children,
      };
}
