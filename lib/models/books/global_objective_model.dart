// To parse this JSON data, do
//
//     final globalObjectiveModel = globalObjectiveModelFromJson(jsonString);

import 'dart:convert';

GlobalObjectiveModel globalObjectiveModelFromJson(String str) =>
    GlobalObjectiveModel.fromJson(json.decode(str));

String globalObjectiveModelToJson(GlobalObjectiveModel data) =>
    json.encode(data.toJson());

class GlobalObjectiveModel {
  List<Objective>? objectives;
  int? totalCount;
  int? filteredCount;

  GlobalObjectiveModel({
    this.objectives,
    this.totalCount,
    this.filteredCount,
  });

  GlobalObjectiveModel copyWith({
    List<Objective>? objectives,
    int? totalCount,
    int? filteredCount,
  }) =>
      GlobalObjectiveModel(
        objectives: objectives ?? this.objectives,
        totalCount: totalCount ?? this.totalCount,
        filteredCount: filteredCount ?? this.filteredCount,
      );

  factory GlobalObjectiveModel.fromJson(Map<String, dynamic> json) =>
      GlobalObjectiveModel(
        objectives: json["objectives"] == null
            ? []
            : List<Objective>.from(
                json["objectives"]!.map((x) => Objective.fromJson(x))),
        totalCount: json["total_count"],
        filteredCount: json["filtered_count"],
      );

  Map<String, dynamic> toJson() => {
        "objectives": objectives == null
            ? []
            : List<dynamic>.from(objectives!.map((x) => x.toJson())),
        "total_count": totalCount,
        "filtered_count": filteredCount,
      };
}

class Objective {
  String? goId;
  String? name;
  String? version;
  String? status;
  String? description;
  DateTime? createdAt;
  DateTime? updatedAt;
  String? tenantId;
  DateTime? lastUsed;
  bool? deletedMark;
  String? versionType;
  dynamic metadata;
  int? autoId;
  String? primaryEntity;
  String? classification;
  String? tenantName;
  String? bookId;
  String? bookName;
  String? chapterId;
  String? chapterName;
  String? createdBy;
  String? updatedBy;
  List<PathwayDefinition>? pathwayDefinitions;
  List<PathwayFrequency>? pathwayFrequency;
  List<LocalObjective>? localObjectives;
  List<ValidationRuleDetail>? validationRuleDetails;

  Objective({
    this.goId,
    this.name,
    this.version,
    this.status,
    this.description,
    this.createdAt,
    this.updatedAt,
    this.tenantId,
    this.lastUsed,
    this.deletedMark,
    this.versionType,
    this.metadata,
    this.autoId,
    this.primaryEntity,
    this.classification,
    this.tenantName,
    this.bookId,
    this.bookName,
    this.chapterId,
    this.chapterName,
    this.createdBy,
    this.updatedBy,
    this.pathwayDefinitions,
    this.pathwayFrequency,
    this.localObjectives,
    this.validationRuleDetails,
  });

  Objective copyWith({
    String? goId,
    String? name,
    String? version,
    String? status,
    String? description,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? tenantId,
    DateTime? lastUsed,
    bool? deletedMark,
    String? versionType,
    dynamic metadata,
    int? autoId,
    String? primaryEntity,
    String? classification,
    String? tenantName,
    String? bookId,
    String? bookName,
    String? chapterId,
    String? chapterName,
    String? createdBy,
    String? updatedBy,
    List<PathwayDefinition>? pathwayDefinitions,
    List<PathwayFrequency>? pathwayFrequency,
    List<LocalObjective>? localObjectives,
    List<ValidationRuleDetail>? validationRuleDetails,
  }) =>
      Objective(
        goId: goId ?? this.goId,
        name: name ?? this.name,
        version: version ?? this.version,
        status: status ?? this.status,
        description: description ?? this.description,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        tenantId: tenantId ?? this.tenantId,
        lastUsed: lastUsed ?? this.lastUsed,
        deletedMark: deletedMark ?? this.deletedMark,
        versionType: versionType ?? this.versionType,
        metadata: metadata ?? this.metadata,
        autoId: autoId ?? this.autoId,
        primaryEntity: primaryEntity ?? this.primaryEntity,
        classification: classification ?? this.classification,
        tenantName: tenantName ?? this.tenantName,
        bookId: bookId ?? this.bookId,
        bookName: bookName ?? this.bookName,
        chapterId: chapterId ?? this.chapterId,
        chapterName: chapterName ?? this.chapterName,
        createdBy: createdBy ?? this.createdBy,
        updatedBy: updatedBy ?? this.updatedBy,
        pathwayDefinitions: pathwayDefinitions ?? this.pathwayDefinitions,
        pathwayFrequency: pathwayFrequency ?? this.pathwayFrequency,
        localObjectives: localObjectives ?? this.localObjectives,
        validationRuleDetails:
            validationRuleDetails ?? this.validationRuleDetails,
      );

  factory Objective.fromJson(Map<String, dynamic> json) => Objective(
        goId: json["go_id"],
        name: json["name"],
        version: json["version"],
        status: json["status"],
        description: json["description"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        tenantId: json["tenant_id"],
        lastUsed: json["last_used"] == null
            ? null
            : DateTime.parse(json["last_used"]),
        deletedMark: json["deleted_mark"],
        versionType: json["version_type"],
        metadata: json["metadata"],
        autoId: json["auto_id"],
        primaryEntity: json["primary_entity"],
        classification: json["classification"],
        tenantName: json["tenant_name"],
        bookId: json["book_id"],
        bookName: json["book_name"],
        chapterId: json["chapter_id"],
        chapterName: json["chapter_name"],
        createdBy: json["created_by"],
        updatedBy: json["updated_by"],
        pathwayDefinitions: json["pathway_definitions"] == null
            ? []
            : List<PathwayDefinition>.from(json["pathway_definitions"]!
                .map((x) => PathwayDefinition.fromJson(x))),
        pathwayFrequency: json["pathway_frequency"] == null
            ? []
            : List<PathwayFrequency>.from(json["pathway_frequency"]!
                .map((x) => PathwayFrequency.fromJson(x))),
        localObjectives: json["local_objectives"] == null
            ? []
            : List<LocalObjective>.from(json["local_objectives"]!
                .map((x) => LocalObjective.fromJson(x))),
        validationRuleDetails: json["validation_rule_details"] == null
            ? []
            : List<ValidationRuleDetail>.from(json["validation_rule_details"]!
                .map((x) => ValidationRuleDetail.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "go_id": goId,
        "name": name,
        "version": version,
        "status": status,
        "description": description,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "tenant_id": tenantId,
        "last_used": lastUsed?.toIso8601String(),
        "deleted_mark": deletedMark,
        "version_type": versionType,
        "metadata": metadata,
        "auto_id": autoId,
        "primary_entity": primaryEntity,
        "classification": classification,
        "tenant_name": tenantName,
        "book_id": bookId,
        "book_name": bookName,
        "chapter_id": chapterId,
        "chapter_name": chapterName,
        "created_by": createdBy,
        "updated_by": updatedBy,
        "pathway_definitions": pathwayDefinitions == null
            ? []
            : List<dynamic>.from(pathwayDefinitions!.map((x) => x.toJson())),
        "pathway_frequency": pathwayFrequency == null
            ? []
            : List<dynamic>.from(pathwayFrequency!.map((x) => x.toJson())),
        "local_objectives": localObjectives == null
            ? []
            : List<dynamic>.from(localObjectives!.map((x) => x.toJson())),
        "validation_rule_details": validationRuleDetails == null
            ? []
            : List<dynamic>.from(validationRuleDetails!.map((x) => x.toJson())),
      };
}

class LocalObjective {
  int? id;
  String? loId;
  String? goId;
  int? loNumber;
  String? loName;
  String? actorType;
  DateTime? createdAt;
  String? createdBy;
  DateTime? updatedAt;
  String? updatedBy;

  LocalObjective({
    this.id,
    this.loId,
    this.goId,
    this.loNumber,
    this.loName,
    this.actorType,
    this.createdAt,
    this.createdBy,
    this.updatedAt,
    this.updatedBy,
  });

  LocalObjective copyWith({
    int? id,
    String? loId,
    String? goId,
    int? loNumber,
    String? loName,
    String? actorType,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
  }) =>
      LocalObjective(
        id: id ?? this.id,
        loId: loId ?? this.loId,
        goId: goId ?? this.goId,
        loNumber: loNumber ?? this.loNumber,
        loName: loName ?? this.loName,
        actorType: actorType ?? this.actorType,
        createdAt: createdAt ?? this.createdAt,
        createdBy: createdBy ?? this.createdBy,
        updatedAt: updatedAt ?? this.updatedAt,
        updatedBy: updatedBy ?? this.updatedBy,
      );

  factory LocalObjective.fromJson(Map<String, dynamic> json) => LocalObjective(
        id: json["id"],
        loId: json["lo_id"],
        goId: json["go_id"],
        loNumber: json["lo_number"],
        loName: json["lo_name"],
        actorType: json["actor_type"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        createdBy: json["created_by"],
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        updatedBy: json["updated_by"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "lo_id": loId,
        "go_id": goId,
        "lo_number": loNumber,
        "lo_name": loName,
        "actor_type": actorType,
        "created_at": createdAt?.toIso8601String(),
        "created_by": createdBy,
        "updated_at": updatedAt?.toIso8601String(),
        "updated_by": updatedBy,
      };
}

class PathwayDefinition {
  int? id;
  String? pathwayDefinitionsId;
  String? goId;
  int? pathwayNumber;
  String? pathwayName;
  String? steps;
  DateTime? createdAt;
  String? createdBy;
  DateTime? updatedAt;
  String? updatedBy;

  PathwayDefinition({
    this.id,
    this.pathwayDefinitionsId,
    this.goId,
    this.pathwayNumber,
    this.pathwayName,
    this.steps,
    this.createdAt,
    this.createdBy,
    this.updatedAt,
    this.updatedBy,
  });

  PathwayDefinition copyWith({
    int? id,
    String? pathwayDefinitionsId,
    String? goId,
    int? pathwayNumber,
    String? pathwayName,
    String? steps,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
  }) =>
      PathwayDefinition(
        id: id ?? this.id,
        pathwayDefinitionsId: pathwayDefinitionsId ?? this.pathwayDefinitionsId,
        goId: goId ?? this.goId,
        pathwayNumber: pathwayNumber ?? this.pathwayNumber,
        pathwayName: pathwayName ?? this.pathwayName,
        steps: steps ?? this.steps,
        createdAt: createdAt ?? this.createdAt,
        createdBy: createdBy ?? this.createdBy,
        updatedAt: updatedAt ?? this.updatedAt,
        updatedBy: updatedBy ?? this.updatedBy,
      );

  factory PathwayDefinition.fromJson(Map<String, dynamic> json) =>
      PathwayDefinition(
        id: json["id"],
        pathwayDefinitionsId: json["pathway_definitions_id"],
        goId: json["go_id"],
        pathwayNumber: json["pathway_number"],
        pathwayName: json["pathway_name"],
        steps: json["steps"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        createdBy: json["created_by"],
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        updatedBy: json["updated_by"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "pathway_definitions_id": pathwayDefinitionsId,
        "go_id": goId,
        "pathway_number": pathwayNumber,
        "pathway_name": pathwayName,
        "steps": steps,
        "created_at": createdAt?.toIso8601String(),
        "created_by": createdBy,
        "updated_at": updatedAt?.toIso8601String(),
        "updated_by": updatedBy,
      };
}

class PathwayFrequency {
  int? id;
  String? performanceDiscoveryId;
  String? goId;
  String? pathwayName;
  int? frequency;
  int? percentage;
  String? averageDuration;
  int? successRate;
  DateTime? createdAt;
  dynamic createdBy;
  DateTime? updatedAt;
  dynamic updatedBy;

  PathwayFrequency({
    this.id,
    this.performanceDiscoveryId,
    this.goId,
    this.pathwayName,
    this.frequency,
    this.percentage,
    this.averageDuration,
    this.successRate,
    this.createdAt,
    this.createdBy,
    this.updatedAt,
    this.updatedBy,
  });

  PathwayFrequency copyWith({
    int? id,
    String? performanceDiscoveryId,
    String? goId,
    String? pathwayName,
    int? frequency,
    int? percentage,
    String? averageDuration,
    int? successRate,
    DateTime? createdAt,
    dynamic createdBy,
    DateTime? updatedAt,
    dynamic updatedBy,
  }) =>
      PathwayFrequency(
        id: id ?? this.id,
        performanceDiscoveryId:
            performanceDiscoveryId ?? this.performanceDiscoveryId,
        goId: goId ?? this.goId,
        pathwayName: pathwayName ?? this.pathwayName,
        frequency: frequency ?? this.frequency,
        percentage: percentage ?? this.percentage,
        averageDuration: averageDuration ?? this.averageDuration,
        successRate: successRate ?? this.successRate,
        createdAt: createdAt ?? this.createdAt,
        createdBy: createdBy ?? this.createdBy,
        updatedAt: updatedAt ?? this.updatedAt,
        updatedBy: updatedBy ?? this.updatedBy,
      );

  factory PathwayFrequency.fromJson(Map<String, dynamic> json) =>
      PathwayFrequency(
        id: json["id"],
        performanceDiscoveryId: json["performance_discovery_id"],
        goId: json["go_id"],
        pathwayName: json["pathway_name"],
        frequency: json["frequency"],
        percentage: json["percentage"],
        averageDuration: json["average_duration"],
        successRate: json["success_rate"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        createdBy: json["created_by"],
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        updatedBy: json["updated_by"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "performance_discovery_id": performanceDiscoveryId,
        "go_id": goId,
        "pathway_name": pathwayName,
        "frequency": frequency,
        "percentage": percentage,
        "average_duration": averageDuration,
        "success_rate": successRate,
        "created_at": createdAt?.toIso8601String(),
        "created_by": createdBy,
        "updated_at": updatedAt?.toIso8601String(),
        "updated_by": updatedBy,
      };
}

class ValidationRuleDetail {
  int? id;
  String? ruleId;
  String? goValidationRuleId;
  String? goId;
  String? ruleName;
  String? ruleInputs;
  String? ruleOperation;
  String? ruleDescription;
  String? ruleOutput;
  String? ruleError;
  String? ruleValidation;
  DateTime? createdAt;
  String? createdBy;
  DateTime? updatedAt;
  String? updatedBy;

  ValidationRuleDetail({
    this.id,
    this.ruleId,
    this.goValidationRuleId,
    this.goId,
    this.ruleName,
    this.ruleInputs,
    this.ruleOperation,
    this.ruleDescription,
    this.ruleOutput,
    this.ruleError,
    this.ruleValidation,
    this.createdAt,
    this.createdBy,
    this.updatedAt,
    this.updatedBy,
  });

  ValidationRuleDetail copyWith({
    int? id,
    String? ruleId,
    String? goValidationRuleId,
    String? goId,
    String? ruleName,
    String? ruleInputs,
    String? ruleOperation,
    String? ruleDescription,
    String? ruleOutput,
    String? ruleError,
    String? ruleValidation,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
  }) =>
      ValidationRuleDetail(
        id: id ?? this.id,
        ruleId: ruleId ?? this.ruleId,
        goValidationRuleId: goValidationRuleId ?? this.goValidationRuleId,
        goId: goId ?? this.goId,
        ruleName: ruleName ?? this.ruleName,
        ruleInputs: ruleInputs ?? this.ruleInputs,
        ruleOperation: ruleOperation ?? this.ruleOperation,
        ruleDescription: ruleDescription ?? this.ruleDescription,
        ruleOutput: ruleOutput ?? this.ruleOutput,
        ruleError: ruleError ?? this.ruleError,
        ruleValidation: ruleValidation ?? this.ruleValidation,
        createdAt: createdAt ?? this.createdAt,
        createdBy: createdBy ?? this.createdBy,
        updatedAt: updatedAt ?? this.updatedAt,
        updatedBy: updatedBy ?? this.updatedBy,
      );

  factory ValidationRuleDetail.fromJson(Map<String, dynamic> json) =>
      ValidationRuleDetail(
        id: json["id"],
        ruleId: json["rule_id"],
        goValidationRuleId: json["go_validation_rule_id"],
        goId: json["go_id"],
        ruleName: json["rule_name"],
        ruleInputs: json["rule_inputs"],
        ruleOperation: json["rule_operation"],
        ruleDescription: json["rule_description"],
        ruleOutput: json["rule_output"],
        ruleError: json["rule_error"],
        ruleValidation: json["rule_validation"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        createdBy: json["created_by"],
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        updatedBy: json["updated_by"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "rule_id": ruleId,
        "go_validation_rule_id": goValidationRuleId,
        "go_id": goId,
        "rule_name": ruleName,
        "rule_inputs": ruleInputs,
        "rule_operation": ruleOperation,
        "rule_description": ruleDescription,
        "rule_output": ruleOutput,
        "rule_error": ruleError,
        "rule_validation": ruleValidation,
        "created_at": createdAt?.toIso8601String(),
        "created_by": createdBy,
        "updated_at": updatedAt?.toIso8601String(),
        "updated_by": updatedBy,
      };
}
