import '../base_model.dart';

/// Model for local objective execution API response
class LocalObjectiveExecutionModel extends BaseModel {
  final String? status;
  final String? message;
  final String? nextLoId;
  final String? loExecutionTable;
  final ExecutionOutput? output;

  LocalObjectiveExecutionModel({
    this.status,
    this.message,
    this.nextLoId,
    this.loExecutionTable,
    this.output,
  });

  factory LocalObjectiveExecutionModel.fromJson(Map<String, dynamic> json) {
    return LocalObjectiveExecutionModel(
      status: json['status'],
      message: json['message'],
      nextLoId: json['next_lo_id'],
      loExecutionTable: json['lo_execution_table'],
      output: json['output'] != null
          ? ExecutionOutput.fromJson(json['output'])
          : null,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'next_lo_id': nextLoId,
      'lo_execution_table': loExecutionTable,
      'output': output?.toJson(),
    };
  }

  @override
  LocalObjectiveExecutionModel copyWith({
    String? status,
    String? message,
    String? nextLoId,
    String? loExecutionTable,
    ExecutionOutput? output,
  }) {
    return LocalObjectiveExecutionModel(
      status: status ?? this.status,
      message: message ?? this.message,
      nextLoId: nextLoId ?? this.nextLoId,
      loExecutionTable: loExecutionTable ?? this.loExecutionTable,
      output: output ?? this.output,
    );
  }
}

/// Model for execution output
class ExecutionOutput extends BaseModel {
  final String? status;
  final String? table;
  final int? rowsAffected;

  ExecutionOutput({
    this.status,
    this.table,
    this.rowsAffected,
  });

  factory ExecutionOutput.fromJson(Map<String, dynamic> json) {
    return ExecutionOutput(
      status: json['status'],
      table: json['table'],
      rowsAffected: json['rows_affected'],
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'table': table,
      'rows_affected': rowsAffected,
    };
  }

  @override
  ExecutionOutput copyWith({
    String? status,
    String? table,
    int? rowsAffected,
  }) {
    return ExecutionOutput(
      status: status ?? this.status,
      table: table ?? this.table,
      rowsAffected: rowsAffected ?? this.rowsAffected,
    );
  }
}

/// Model for error response
class LocalObjectiveExecutionError extends BaseModel {
  final ErrorDetail? detail;

  LocalObjectiveExecutionError({
    this.detail,
  });

  factory LocalObjectiveExecutionError.fromJson(Map<String, dynamic> json) {
    return LocalObjectiveExecutionError(
      detail:
          json['detail'] != null ? ErrorDetail.fromJson(json['detail']) : null,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'detail': detail?.toJson(),
    };
  }

  @override
  LocalObjectiveExecutionError copyWith({
    ErrorDetail? detail,
  }) {
    return LocalObjectiveExecutionError(
      detail: detail ?? this.detail,
    );
  }
}

/// Model for error detail
class ErrorDetail extends BaseModel {
  final String? error;
  final String? message;
  final ErrorDetails? details;

  ErrorDetail({
    this.error,
    this.message,
    this.details,
  });

  factory ErrorDetail.fromJson(Map<String, dynamic> json) {
    return ErrorDetail(
      error: json['error'],
      message: json['message'],
      details: json['details'] != null
          ? ErrorDetails.fromJson(json['details'])
          : null,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'error': error,
      'message': message,
      'details': details?.toJson(),
    };
  }

  @override
  ErrorDetail copyWith({
    String? error,
    String? message,
    ErrorDetails? details,
  }) {
    return ErrorDetail(
      error: error ?? this.error,
      message: message ?? this.message,
      details: details ?? this.details,
    );
  }
}

/// Model for error details
class ErrorDetails extends BaseModel {
  final List<String>? missingFields;
  final Map<String, dynamic>? invalidFields;

  ErrorDetails({
    this.missingFields,
    this.invalidFields,
  });

  factory ErrorDetails.fromJson(Map<String, dynamic> json) {
    return ErrorDetails(
      missingFields: json['missing_fields'] != null
          ? List<String>.from(json['missing_fields'])
          : null,
      invalidFields: json['invalid_fields'] != null
          ? Map<String, dynamic>.from(json['invalid_fields'])
          : null,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'missing_fields': missingFields,
      'invalid_fields': invalidFields,
    };
  }

  @override
  ErrorDetails copyWith({
    List<String>? missingFields,
    Map<String, dynamic>? invalidFields,
  }) {
    return ErrorDetails(
      missingFields: missingFields ?? this.missingFields,
      invalidFields: invalidFields ?? this.invalidFields,
    );
  }
}
