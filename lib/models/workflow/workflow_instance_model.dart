// To parse this JSON data, do
//
//     final workflowInstanceModel = workflowInstanceModelFromJson(jsonString);

import 'dart:convert';

WorkflowInstanceModel workflowInstanceModelFromJson(String str) =>
    WorkflowInstanceModel.fromJson(json.decode(str));

String workflowInstanceModelToJson(WorkflowInstanceModel data) =>
    json.encode(data.toJson());

class WorkflowInstanceModel {
  String? instanceId;
  String? goId;
  String? tenantId;
  String? status;
  String? startedBy;
  String? startedAt;
  String? completedAt;
  String? currentLoId;
  Map<String, dynamic>? instanceData;
  bool? isTest;
  String? version;

  // UI state fields for loading and error handling
  bool isLoading;
  String? errorMessage;

  WorkflowInstanceModel({
    this.instanceId,
    this.goId,
    this.tenantId,
    this.status,
    this.startedBy,
    this.startedAt,
    this.completedAt,
    this.currentLoId,
    this.instanceData,
    this.isTest,
    this.version,
    this.isLoading = false,
    this.errorMessage,
  });

  WorkflowInstanceModel copyWith({
    String? instanceId,
    String? goId,
    String? tenantId,
    String? status,
    String? startedBy,
    String? startedAt,
    String? completedAt,
    String? currentLoId,
    Map<String, dynamic>? instanceData,
    bool? isTest,
    String? version,
    bool? isLoading,
    String? errorMessage,
  }) =>
      WorkflowInstanceModel(
        instanceId: instanceId ?? this.instanceId,
        goId: goId ?? this.goId,
        tenantId: tenantId ?? this.tenantId,
        status: status ?? this.status,
        startedBy: startedBy ?? this.startedBy,
        startedAt: startedAt ?? this.startedAt,
        completedAt: completedAt ?? this.completedAt,
        currentLoId: currentLoId ?? this.currentLoId,
        instanceData: instanceData ?? this.instanceData,
        isTest: isTest ?? this.isTest,
        version: version ?? this.version,
        isLoading: isLoading ?? this.isLoading,
        errorMessage: errorMessage ?? this.errorMessage,
      );

  factory WorkflowInstanceModel.fromJson(Map<String, dynamic> json) =>
      WorkflowInstanceModel(
        instanceId: json["instance_id"],
        goId: json["go_id"],
        tenantId: json["tenant_id"],
        status: json["status"],
        startedBy: json["started_by"],
        startedAt: json["started_at"],
        completedAt: json["completed_at"],
        currentLoId: json["current_lo_id"],
        instanceData: json["instance_data"] != null
            ? Map<String, dynamic>.from(json["instance_data"])
            : null,
        isTest: json["is_test"],
        version: json["version"],
        // UI state fields with default values (not from API)
        isLoading: false,
        errorMessage: null,
      );

  Map<String, dynamic> toJson() => {
        "instance_id": instanceId,
        "go_id": goId,
        "tenant_id": tenantId,
        "status": status,
        "started_by": startedBy,
        "started_at": startedAt,
        "completed_at": completedAt,
        "current_lo_id": currentLoId,
        "instance_data": instanceData,
        "is_test": isTest,
        "version": version,
        // UI state fields
        "is_loading": isLoading,
        "error_message": errorMessage,
      };
}
