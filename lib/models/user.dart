import 'base_model.dart';
import 'auth/login_model.dart';

class User implements BaseModel {
  final String id;
  final String email;
  final String name;
  final String? firstName;     
  final String? lastName;      
  final String? username;
  final String? role;
  final String? profilePicture;
  final String? organization;
  final String? mobileNumber;
  final String? accessToken;
  final String? refreshToken;
  final List<String>? roles;
  final List<String>? orgUnits;
  final String? tenantId;
  final bool? disabled;
  final String? status;

  User({
    required this.id,
    required this.email,
    required this.name,
    this.firstName,              
    this.lastName,
    this.username,
    this.role,
    this.profilePicture,
    this.organization,
    this.mobileNumber,
    this.accessToken,
    this.refreshToken,
    this.roles,
    this.orgUnits,
    this.tenantId,
    this.disabled,
    this.status,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    // Handle roles list
    List<String>? roles;
    if (json['roles'] != null) {
      roles = List<String>.from(json['roles']);
    }

    // Handle org units list
    List<String>? orgUnits;
    if (json['org_units'] != null) {
      orgUnits = List<String>.from(json['org_units']);
    }

    // Combine first and last name for the name field if they exist separately
    String fullName = '';
    final firstName = json['first_name'];
    final lastName = json['last_name'];
    
    if (firstName != null && lastName != null && firstName.toString().isNotEmpty && lastName.toString().isNotEmpty) {
      fullName = '$firstName $lastName';
    } else if (firstName != null && firstName.toString().isNotEmpty) {
      fullName = firstName.toString();
    } else if (lastName != null && lastName.toString().isNotEmpty) {
      fullName = lastName.toString();
    } else {
      fullName = json['name'] ?? json['nickname'] ?? '';
    }

    return User(
      id: json['id'] ?? json['user_id'] ?? json['sub'] ?? '',
      email: json['email'] ?? '',
      name: fullName,
      firstName: json['first_name'],     
      lastName: json['last_name'],
      username: json['username'],
      role: json['roleValue'] ?? json['role'],
      profilePicture:
          json['profile_picture'] ?? json['profilePic'] ?? json['picture'],
      organization: json['OrgName'] ?? json['organization'],
      mobileNumber: json['mobileNo'] ?? json['mobile'],
      accessToken: json['access_token'],
      refreshToken: json['refresh_token'],
      roles: roles,
      orgUnits: orgUnits,
      tenantId: json['tenant_id'],
      disabled: json['disabled'],
      status: json['status'],
    );
  }

  factory User.fromLoginResponse(Map<String, dynamic> response) {
    // Extract token data
    final tokenData = response['token_data'] as Map<String, dynamic>;
    final accessToken = tokenData['access_token'] as String;
    final refreshToken = tokenData['refresh_token'] as String;

    // Extract user info
    final userInfo = response['user_info'] as Map<String, dynamic>;

    return User(
      id: userInfo['user_id'].toString(), // Convert to String if needed
      email: userInfo['email'] as String,
      name: userInfo['full_name'] as String,
      firstName: userInfo['first_name'] as String?,
      lastName: userInfo['last_name'] as String?,
      username: userInfo['username'] as String,
      profilePicture: userInfo['profilePic'] as String,
      mobileNumber: userInfo['phone_number'] as String,
      organization: userInfo['company'] as String,
      accessToken: accessToken,
      refreshToken: refreshToken,
    );
  }

  factory User.fromAuth0Registration(Map<String, dynamic> response) {
    // Handle roles list
    List<String>? roles;
    if (response['roles'] != null) {
      roles = List<String>.from(response['roles']);
    }

    // Handle org units list
    List<String>? orgUnits;
    if (response['org_units'] != null) {
      orgUnits = List<String>.from(response['org_units']);
    }

    // Combine first and last name properly
    String fullName = '';
    final firstName = response['first_name'];
    final lastName = response['last_name'];
    
    if (firstName != null && lastName != null && firstName.toString().isNotEmpty && lastName.toString().isNotEmpty) {
      fullName = '$firstName $lastName';
    } else if (firstName != null && firstName.toString().isNotEmpty) {
      fullName = firstName.toString();
    } else if (lastName != null && lastName.toString().isNotEmpty) {
      fullName = lastName.toString();
    }

    return User(
      id: response['user_id'] ?? '',
      email: response['email'] ?? '',
      name: fullName.trim(),
      firstName: response['first_name'],
      lastName: response['last_name'],
      username: response['username'] ?? '',
      role: roles?.isNotEmpty == true ? roles!.first : '',
      profilePicture: response['picture'],
      organization: orgUnits?.isNotEmpty == true ? orgUnits!.first : '',
      mobileNumber: '',
      roles: roles,
      orgUnits: orgUnits,
      tenantId: response['tenant_id'],
      disabled: response['disabled'],
      status: response['status'],
    );
  }

  factory User.fromUserDetails(UserDetails userDetails) {
    // Combine first and last name properly
    String fullName = '';
    final firstName = userDetails.firstName;
    final lastName = userDetails.lastName;
    
    if (firstName != null && lastName != null && firstName.isNotEmpty && lastName.isNotEmpty) {
      fullName = '$firstName $lastName';
    } else if (firstName != null && firstName.isNotEmpty) {
      fullName = firstName;
    } else if (lastName != null && lastName.isNotEmpty) {
      fullName = lastName;
    }

    return User(
      id: userDetails.userId ?? '',
      email: userDetails.email ?? '',
      name: fullName.trim(),
      firstName: userDetails.firstName,
      lastName: userDetails.lastName,
      username: userDetails.username,
      roles: userDetails.roles,
      tenantId: userDetails.tenantId,
      disabled: userDetails.disabled,
      status: userDetails.status,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': id,
      'email': email,
      'name': name,
      'first_name': firstName,
      'last_name': lastName,
      'username': username,
      'roleValue': role,
      'profilePic': profilePicture,
      'OrgName': organization,
      'mobileNo': mobileNumber,
      'access_token': accessToken,
      'refresh_token': refreshToken,
      'roles': roles,
      'org_units': orgUnits,
      'tenant_id': tenantId,
      'disabled': disabled,
      'status': status,
    };
  }

  @override
  User copyWith({
    String? id,
    String? email,
    String? name,
    String? firstName,
    String? lastName,
    String? username,
    String? role,
    String? profilePicture,
    String? organization,
    String? mobileNumber,
    String? accessToken,
    String? refreshToken,
    List<String>? roles,
    List<String>? orgUnits,
    String? tenantId,
    bool? disabled,
    String? status,
  }) {
    return User(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      username: username ?? this.username,
      role: role ?? this.role,
      profilePicture: profilePicture ?? this.profilePicture,
      organization: organization ?? this.organization,
      mobileNumber: mobileNumber ?? this.mobileNumber,
      accessToken: accessToken ?? this.accessToken,
      refreshToken: refreshToken ?? this.refreshToken,
      roles: roles ?? this.roles,
      orgUnits: orgUnits ?? this.orgUnits,
      tenantId: tenantId ?? this.tenantId,
      disabled: disabled ?? this.disabled,
      status: status ?? this.status,
    );
  }

  // Utility method to check if the user is logged in
  bool get isLoggedIn => accessToken != null && accessToken!.isNotEmpty;

  // Utility method to get the user's display name
  String get displayName => name.isNotEmpty ? name : username ?? email;

  // Utility method to get the user's initials
  String get initials {
    if (name.isNotEmpty) {
      final parts = name.split(' ');
      if (parts.length > 1) {
        return '${parts[0][0]}${parts[1][0]}'.toUpperCase();
      }
      return name[0].toUpperCase();
    }
    return email[0].toUpperCase();
  }

  // Utility method to get formatted full name with proper capitalization
  String get formattedFullName {
    print('DEBUG formattedFullName called');
    print('DEBUG firstName: "$firstName"');
    print('DEBUG lastName: "$lastName"');
    print('DEBUG name: "$name"');
    
    // If we have separate first and last names, use them and capitalize properly
    if (firstName != null && lastName != null && firstName!.isNotEmpty && lastName!.isNotEmpty) {
      final first = _capitalizeFirstLetter(firstName!.trim());
      // Use _capitalizeWords for lastName to handle multi-word last names like "reddy etikala"
      final last = _capitalizeWords(lastName!.trim());
      final result = '$first $last';
      print('DEBUG using firstName + lastName: "$result"');
      return result;
    } 
    
    // If we only have firstName, use it
    if (firstName != null && firstName!.isNotEmpty) {
      final result = _capitalizeFirstLetter(firstName!.trim());
      print('DEBUG using firstName only: "$result"');
      return result;
    } 
    
    // If we have a name field, handle it intelligently
    if (name.isNotEmpty) {
      final trimmedName = name.trim();
      final nameParts = trimmedName.split(' ');
      
      if (nameParts.length >= 2) {
        // Multiple words - treat as full name and capitalize each word
        final result = _capitalizeWords(trimmedName);
        print('DEBUG using name field (multi-word): "$result"');
        return result;
      } else {
        // Single word - capitalize and return
        final result = _capitalizeFirstLetter(trimmedName);
        print('DEBUG using name field (single word): "$result"');
        return result;
      }
    }
    
    print('DEBUG fallback to User Name');
    return 'User Name';
  }

  // Helper method to capitalize first letter of a word
  String _capitalizeFirstLetter(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1).toLowerCase();
  }

  // Helper method to capitalize each word in a string
  String _capitalizeWords(String text) {
    return text.split(' ')
        .map((word) => word.isEmpty ? '' : _capitalizeFirstLetter(word))
        .where((word) => word.isNotEmpty)
        .join(' ');
  }
}
