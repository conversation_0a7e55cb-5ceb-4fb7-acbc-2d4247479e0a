// To parse this JSON data, do
//
//     final userProfileModel = userProfileModelFromJson(jsonString);

import 'dart:convert';

UserProfileModel userProfileModelFromJson(String str) =>
    UserProfileModel.fromJson(json.decode(str));

String userProfileModelToJson(UserProfileModel data) =>
    json.encode(data.toJson());

class UserProfileModel {
  String? userId;
  String? username;
  String? email;
  String? firstName;
  String? lastName;
  String? status;
  List<String>? roles;
  String? tenantId;
  bool? disabled;
  DateTime? createdAt;
  dynamic department;
  List<dynamic>? teamMemberships;
  OrganizationalHierarchy? organizationalHierarchy;
  List<Permission>? entityPermissions;
  List<Permission>? attributePermissions;
  List<Permission>? globalObjectivePermissions;
  List<Permission>? localObjectivePermissions;
  List<Permission>? bookPermissions;
  List<Permission>? chapterPermissions;
  List<RoleKpi>? roleKpis;
  List<dynamic>? teamColleagues;
  List<dynamic>? departmentColleagues;

  UserProfileModel({
    this.userId,
    this.username,
    this.email,
    this.firstName,
    this.lastName,
    this.status,
    this.roles,
    this.tenantId,
    this.disabled,
    this.createdAt,
    this.department,
    this.teamMemberships,
    this.organizationalHierarchy,
    this.entityPermissions,
    this.attributePermissions,
    this.globalObjectivePermissions,
    this.localObjectivePermissions,
    this.bookPermissions,
    this.chapterPermissions,
    this.roleKpis,
    this.teamColleagues,
    this.departmentColleagues,
  });

  UserProfileModel copyWith({
    String? userId,
    String? username,
    String? email,
    String? firstName,
    String? lastName,
    String? status,
    List<String>? roles,
    String? tenantId,
    bool? disabled,
    DateTime? createdAt,
    dynamic department,
    List<dynamic>? teamMemberships,
    OrganizationalHierarchy? organizationalHierarchy,
    List<Permission>? entityPermissions,
    List<Permission>? attributePermissions,
    List<Permission>? globalObjectivePermissions,
    List<Permission>? localObjectivePermissions,
    List<Permission>? bookPermissions,
    List<Permission>? chapterPermissions,
    List<RoleKpi>? roleKpis,
    List<dynamic>? teamColleagues,
    List<dynamic>? departmentColleagues,
  }) =>
      UserProfileModel(
        userId: userId ?? this.userId,
        username: username ?? this.username,
        email: email ?? this.email,
        firstName: firstName ?? this.firstName,
        lastName: lastName ?? this.lastName,
        status: status ?? this.status,
        roles: roles ?? this.roles,
        tenantId: tenantId ?? this.tenantId,
        disabled: disabled ?? this.disabled,
        createdAt: createdAt ?? this.createdAt,
        department: department ?? this.department,
        teamMemberships: teamMemberships ?? this.teamMemberships,
        organizationalHierarchy:
            organizationalHierarchy ?? this.organizationalHierarchy,
        entityPermissions: entityPermissions ?? this.entityPermissions,
        attributePermissions: attributePermissions ?? this.attributePermissions,
        globalObjectivePermissions:
            globalObjectivePermissions ?? this.globalObjectivePermissions,
        localObjectivePermissions:
            localObjectivePermissions ?? this.localObjectivePermissions,
        bookPermissions: bookPermissions ?? this.bookPermissions,
        chapterPermissions: chapterPermissions ?? this.chapterPermissions,
        roleKpis: roleKpis ?? this.roleKpis,
        teamColleagues: teamColleagues ?? this.teamColleagues,
        departmentColleagues: departmentColleagues ?? this.departmentColleagues,
      );

  factory UserProfileModel.fromJson(Map<String, dynamic> json) =>
      UserProfileModel(
        userId: json["user_id"],
        username: json["username"],
        email: json["email"],
        firstName: json["first_name"],
        lastName: json["last_name"],
        status: json["status"],
        roles: json["roles"] == null
            ? []
            : List<String>.from(json["roles"]!.map((x) => x)),
        tenantId: json["tenant_id"],
        disabled: json["disabled"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        department: json["department"],
        teamMemberships: json["team_memberships"] == null
            ? []
            : List<dynamic>.from(json["team_memberships"]!.map((x) => x)),
        organizationalHierarchy: json["organizational_hierarchy"] == null
            ? null
            : OrganizationalHierarchy.fromJson(
                json["organizational_hierarchy"]),
        entityPermissions: json["entity_permissions"] == null
            ? []
            : List<Permission>.from(
                json["entity_permissions"]!.map((x) => Permission.fromJson(x))),
        attributePermissions: json["attribute_permissions"] == null
            ? []
            : List<Permission>.from(json["attribute_permissions"]!
                .map((x) => Permission.fromJson(x))),
        globalObjectivePermissions: json["global_objective_permissions"] == null
            ? []
            : List<Permission>.from(json["global_objective_permissions"]!
                .map((x) => Permission.fromJson(x))),
        localObjectivePermissions: json["local_objective_permissions"] == null
            ? []
            : List<Permission>.from(json["local_objective_permissions"]!
                .map((x) => Permission.fromJson(x))),
        bookPermissions: json["book_permissions"] == null
            ? []
            : List<Permission>.from(
                json["book_permissions"]!.map((x) => Permission.fromJson(x))),
        chapterPermissions: json["chapter_permissions"] == null
            ? []
            : List<Permission>.from(json["chapter_permissions"]!
                .map((x) => Permission.fromJson(x))),
        roleKpis: json["role_kpis"] == null
            ? []
            : List<RoleKpi>.from(
                json["role_kpis"]!.map((x) => RoleKpi.fromJson(x))),
        teamColleagues: json["team_colleagues"] == null
            ? []
            : List<dynamic>.from(json["team_colleagues"]!.map((x) => x)),
        departmentColleagues: json["department_colleagues"] == null
            ? []
            : List<dynamic>.from(json["department_colleagues"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "user_id": userId,
        "username": username,
        "email": email,
        "first_name": firstName,
        "last_name": lastName,
        "status": status,
        "roles": roles == null ? [] : List<dynamic>.from(roles!.map((x) => x)),
        "tenant_id": tenantId,
        "disabled": disabled,
        "created_at": createdAt?.toIso8601String(),
        "department": department,
        "team_memberships": teamMemberships == null
            ? []
            : List<dynamic>.from(teamMemberships!.map((x) => x)),
        "organizational_hierarchy": organizationalHierarchy?.toJson(),
        "entity_permissions": entityPermissions == null
            ? []
            : List<dynamic>.from(entityPermissions!.map((x) => x.toJson())),
        "attribute_permissions": attributePermissions == null
            ? []
            : List<dynamic>.from(attributePermissions!.map((x) => x.toJson())),
        "global_objective_permissions": globalObjectivePermissions == null
            ? []
            : List<dynamic>.from(
                globalObjectivePermissions!.map((x) => x.toJson())),
        "local_objective_permissions": localObjectivePermissions == null
            ? []
            : List<dynamic>.from(
                localObjectivePermissions!.map((x) => x.toJson())),
        "book_permissions": bookPermissions == null
            ? []
            : List<dynamic>.from(bookPermissions!.map((x) => x.toJson())),
        "chapter_permissions": chapterPermissions == null
            ? []
            : List<dynamic>.from(chapterPermissions!.map((x) => x.toJson())),
        "role_kpis": roleKpis == null
            ? []
            : List<dynamic>.from(roleKpis!.map((x) => x.toJson())),
        "team_colleagues": teamColleagues == null
            ? []
            : List<dynamic>.from(teamColleagues!.map((x) => x)),
        "department_colleagues": departmentColleagues == null
            ? []
            : List<dynamic>.from(departmentColleagues!.map((x) => x)),
      };
}

class Permission {
  String? permissionId;
  String? permissionName;
  String? permissionType;
  String? resourceIdentifier;
  List<String>? availableActions;
  List<String>? grantedActions;
  String? description;

  Permission({
    this.permissionId,
    this.permissionName,
    this.permissionType,
    this.resourceIdentifier,
    this.availableActions,
    this.grantedActions,
    this.description,
  });

  Permission copyWith({
    String? permissionId,
    String? permissionName,
    String? permissionType,
    String? resourceIdentifier,
    List<String>? availableActions,
    List<String>? grantedActions,
    String? description,
  }) =>
      Permission(
        permissionId: permissionId ?? this.permissionId,
        permissionName: permissionName ?? this.permissionName,
        permissionType: permissionType ?? this.permissionType,
        resourceIdentifier: resourceIdentifier ?? this.resourceIdentifier,
        availableActions: availableActions ?? this.availableActions,
        grantedActions: grantedActions ?? this.grantedActions,
        description: description ?? this.description,
      );

  factory Permission.fromJson(Map<String, dynamic> json) => Permission(
        permissionId: json["permission_id"],
        permissionName: json["permission_name"],
        permissionType: json["permission_type"],
        resourceIdentifier: json["resource_identifier"],
        availableActions: json["available_actions"] == null
            ? []
            : List<String>.from(json["available_actions"]!.map((x) => x)),
        grantedActions: json["granted_actions"] == null
            ? []
            : List<String>.from(json["granted_actions"]!.map((x) => x)),
        description: json["description"],
      );

  Map<String, dynamic> toJson() => {
        "permission_id": permissionId,
        "permission_name": permissionName,
        "permission_type": permissionType,
        "resource_identifier": resourceIdentifier,
        "available_actions": availableActions == null
            ? []
            : List<dynamic>.from(availableActions!.map((x) => x)),
        "granted_actions": grantedActions == null
            ? []
            : List<dynamic>.from(grantedActions!.map((x) => x)),
        "description": description,
      };
}

class OrganizationalHierarchy {
  String? userId;
  dynamic reportsToUserId;
  dynamic organizationalLevel;
  List<dynamic>? directReports;
  List<String>? hierarchyPath;

  OrganizationalHierarchy({
    this.userId,
    this.reportsToUserId,
    this.organizationalLevel,
    this.directReports,
    this.hierarchyPath,
  });

  OrganizationalHierarchy copyWith({
    String? userId,
    dynamic reportsToUserId,
    dynamic organizationalLevel,
    List<dynamic>? directReports,
    List<String>? hierarchyPath,
  }) =>
      OrganizationalHierarchy(
        userId: userId ?? this.userId,
        reportsToUserId: reportsToUserId ?? this.reportsToUserId,
        organizationalLevel: organizationalLevel ?? this.organizationalLevel,
        directReports: directReports ?? this.directReports,
        hierarchyPath: hierarchyPath ?? this.hierarchyPath,
      );

  factory OrganizationalHierarchy.fromJson(Map<String, dynamic> json) =>
      OrganizationalHierarchy(
        userId: json["user_id"],
        reportsToUserId: json["reports_to_user_id"],
        organizationalLevel: json["organizational_level"],
        directReports: json["direct_reports"] == null
            ? []
            : List<dynamic>.from(json["direct_reports"]!.map((x) => x)),
        hierarchyPath: json["hierarchy_path"] == null
            ? []
            : List<String>.from(json["hierarchy_path"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "user_id": userId,
        "reports_to_user_id": reportsToUserId,
        "organizational_level": organizationalLevel,
        "direct_reports": directReports == null
            ? []
            : List<dynamic>.from(directReports!.map((x) => x)),
        "hierarchy_path": hierarchyPath == null
            ? []
            : List<dynamic>.from(hierarchyPath!.map((x) => x)),
      };
}

class RoleKpi {
  String? kpiId;
  String? roleId;
  String? name;
  String? description;
  String? formula;
  String? target;
  String? measurementFrequency;
  DateTime? createdAt;

  RoleKpi({
    this.kpiId,
    this.roleId,
    this.name,
    this.description,
    this.formula,
    this.target,
    this.measurementFrequency,
    this.createdAt,
  });

  RoleKpi copyWith({
    String? kpiId,
    String? roleId,
    String? name,
    String? description,
    String? formula,
    String? target,
    String? measurementFrequency,
    DateTime? createdAt,
  }) =>
      RoleKpi(
        kpiId: kpiId ?? this.kpiId,
        roleId: roleId ?? this.roleId,
        name: name ?? this.name,
        description: description ?? this.description,
        formula: formula ?? this.formula,
        target: target ?? this.target,
        measurementFrequency: measurementFrequency ?? this.measurementFrequency,
        createdAt: createdAt ?? this.createdAt,
      );

  factory RoleKpi.fromJson(Map<String, dynamic> json) => RoleKpi(
        kpiId: json["kpi_id"],
        roleId: json["role_id"],
        name: json["name"],
        description: json["description"],
        formula: json["formula"],
        target: json["target"],
        measurementFrequency: json["measurement_frequency"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
      );

  Map<String, dynamic> toJson() => {
        "kpi_id": kpiId,
        "role_id": roleId,
        "name": name,
        "description": description,
        "formula": formula,
        "target": target,
        "measurement_frequency": measurementFrequency,
        "created_at": createdAt?.toIso8601String(),
      };
}
