// Global action cards data that can be shared across different screens
class GlobalActionCardsData {
  static List<Map<String, dynamic>> actionCardsData = [
    {
      'text': 'Procurement Management',
      'icon': 'shopping_cart',
      'image': "assets/images/my_business/solutions/solution_apply.png",
      'action': 'procurement_management',
      'isSelected': false
    },
    {
      'text': 'My Modules and My team',
      'icon': 'people',
      'image': "assets/images/my_business/solutions/solution_employee_details.png",
      'action': 'modules_team',
      'isSelected': false
    },
    {
      'text': 'Org Guides and Business Modules',
      'icon': 'business',
      'image': "assets/images/my_business/solutions/solution_my_module.png",
      'action': 'organisation_guides',
      'isSelected': false
    },
    {
      'text': 'Leave management',
      'icon': 'event_note',
      'image': "assets/images/my_business/solutions/solution_my_attendance.png",
      'action': 'leave_management',
      'isSelected': false
    },
    {
      'text': 'Apply Leave',
      'icon': 'event_note',
      'image': "assets/images/my_business/solutions/solution_organisation_details.png",
      'action': 'apply_leave',
      'isSelected': false
    },
    {
      'text': 'Leave approval',
      'icon': 'event_note',
      'image': "assets/images/my_business/solutions/solution_product_details.png",
      'action': 'leave_approval',
      'isSelected': false
    },
    {
      'text': 'Performance Review',
      'icon': 'assessment',
      'image': "assets/images/my_business/solutions/solution_payment_refund.png",
      'action': 'performance_review',
      'isSelected': false
    },
    {
      'text': 'Training & Development',
      'icon': 'school',
      'image': "assets/images/my_business/solutions/solution_my_orders.png",
      'action': 'training_development',
      'isSelected': false
    },
  ];

  static void toggleFavorite(int index) {
    if (index >= 0 && index < actionCardsData.length) {
      actionCardsData[index]['isSelected'] = !actionCardsData[index]['isSelected'];
    }
  }

  static List<Map<String, dynamic>> getFavorites() {
    return actionCardsData.where((item) => item['isSelected'] == true).toList();
  }
}
