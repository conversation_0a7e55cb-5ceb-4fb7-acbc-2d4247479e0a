import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart' as f1;
//import 'package:pie_chart/pie_chart.dart' as pc;
import 'package:flutter_svg/flutter_svg.dart';

class WebMyTransactionsWidgets extends StatefulWidget {
  const WebMyTransactionsWidgets({super.key});

  @override
  State<WebMyTransactionsWidgets> createState() =>
      _WebMyTransactionsWidgetsState();
}

class _WebMyTransactionsWidgetsState extends State<WebMyTransactionsWidgets> {
  bool _showSearchBox = false;

  void _toggleSearchBox() {
    setState(() {
      _showSearchBox = !_showSearchBox;
    });
  }

  @override
  Widget build(BuildContext context) {
    List<String> taskPriorityTwo = [
      "Task priority in next 2 Days",
      "User Registration",
      "Submit Form",
      "Approve Application",
      "Payment Gateway Page"
    ];

    List<String> taskPrioritySeven = [
      "Task priority in next 7 Days",
      "User Profile",
      "Forgot Password",
      "Payment Gateway",
      "Approve Application"
    ];

    return Scaffold(
      backgroundColor: const Color(0xFFEDEDED),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // ✅ First section with left padding
            Padding(
              padding: const EdgeInsets.only(left: 42),
              child: SizedBox(
                width: 1326,
                height: 273,
                //  margin: const EdgeInsets.only(left: 2),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Title and Search Row
                    Padding(
                      padding: const EdgeInsets.only(top: 10),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const SizedBox(
                            width: 121,
                            height: 20,
                            child: Text(
                              "My Transaction",
                              textAlign: TextAlign.left,
                              style: TextStyle(
                                fontSize: 16,
                                height: 1.25,
                                fontWeight: FontWeight.bold,
                                fontFamily: 'Inter',
                                letterSpacing: 0,
                                color: Color(0xFF606060),
                              ),
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.only(right: 42),
                            child: SizedBox(
                              width: 324,
                              height: 36,
                              child: Stack(
                                alignment: Alignment.centerRight,
                                children: [
                                  AnimatedOpacity(
                                    duration: const Duration(milliseconds: 250),
                                    opacity: _showSearchBox ? 1.0 : 0.0,
                                    child: Visibility(
                                      visible: _showSearchBox,
                                      child: TextField(
                                        autofocus: true,
                                        decoration: InputDecoration(
                                          hintText: "Search Notification",
                                          hintStyle: const TextStyle(
                                            fontSize: 14,
                                            height: 22 / 14,
                                            fontWeight: FontWeight.normal,
                                            color: Color(0xFF999999),
                                          ),
                                          filled: true,
                                          fillColor: Colors.white,
                                          contentPadding:
                                              const EdgeInsets.symmetric(
                                                  horizontal: 10),
                                          suffixIcon: InkWell(
                                            onTap: _toggleSearchBox,
                                            child: const Padding(
                                              padding:
                                                  EdgeInsets.only(right: 3.0),
                                              child: Icon(
                                                Icons.search,
                                                size: 20,
                                                color: Color(0xFF7E7D7D),
                                              ),
                                            ),
                                          ),
                                          suffixIconConstraints:
                                              const BoxConstraints(
                                            minHeight: 24,
                                            minWidth: 24,
                                          ),
                                          border: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                              color: Color(0xFFCCCCCC),
                                              width: 1,
                                            ),
                                          ),
                                          enabledBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                              color: Color(0xFFCCCCCC),
                                              width: 1,
                                            ),
                                          ),
                                          focusedBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                              color: Color(0xFFCCCCCC),
                                              width: 1,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                  Positioned(
                                    right: 3,
                                    child: InkWell(
                                      onTap: _toggleSearchBox,
                                      child: const Icon(
                                        Icons.search,
                                        size: 20,
                                        color: Color(0xFF7E7D7D),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Spacing between search bar and boxes
                    const SizedBox(height: 13),

                    // Row of boxes with horizontal scroll
                    SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: Row(
                        children: [
                          _buildBox(taskPriorityTwo),
                          const SizedBox(width: 16),
                          _buildBox(taskPrioritySeven),
                          const SizedBox(width: 16),
                          _buildBoxPieChart(
                              "Status of all Solutions", "Total 100"),
                          const SizedBox(width: 16),
                          _buildBoxLineChart("Work-in-progress Tasks Timeline"),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            //   const SizedBox(height: 3),

            // ✅ Second section — full width, not affected by first section padding
            const TabChartSection(),
          ],
        ),
      ),
    );
  }
}

Widget _buildBox(List taskPriority) {
  return SizedBox(
    width: 298,
    height: 185,
    child: Container(
      //padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white, // background: #FFFFFF
        borderRadius: BorderRadius.circular(12), // border-radius: 12px
        boxShadow: [
          BoxShadow(
            color: const Color(0x1A000000), // #0000001A
            offset: const Offset(0, 2), // 0px 2px
            blurRadius: 0, // no blur
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header split
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Padding(
                padding: const EdgeInsets.only(top: 18, left: 16),
                child: SizedBox(
                  width: 146,
                  height: 17,
                  child: Text(
                    taskPriority[0]
                        .split(" ")
                        .sublist(0, taskPriority[0].split(" ").length - 2)
                        .join(" "),
                    style: const TextStyle(
                      fontWeight: FontWeight.w600, // normal normal 600
                      fontSize: 14,
                      height: 16 / 14, // line height = 16px
                      fontFamily: 'Inter',
                      color: Color(0xFF606060),
                    ),
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(top: 16, right: 16),
                child: SizedBox(
                  width: 59,
                  height: 21,
                  child: Builder(
                    builder: (context) {
                      final isTwoDays = taskPriority[0].contains("2 Days");

                      // This call is safe if no setState is being invoked
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        // Set state here only if needed.
                      });

                      return Text(
                        taskPriority[0]
                            .split(" ")
                            .sublist(taskPriority[0].split(" ").length - 2)
                            .join(" "),
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                          height: 21 / 18,
                          fontFamily: 'Inter',
                          color: isTwoDays
                              ? const Color(0xFFF47B74)
                              : const Color(0xFFF5BA76),
                          textBaseline: TextBaseline.alphabetic,
                          letterSpacing: 0,
                        ),
                      );
                    },
                  ),
                ),
              ),
            ],
          ),

          // const Divider(),

          // Dynamic items
          ...taskPriority.sublist(1).map((item) {
            return Padding(
              padding: const EdgeInsets.only(left: 22, top: 6, bottom: 2),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Container(
                        width: 16,
                        height: 16,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: taskPriority[0].contains("2 Days")
                              ? const Color(0xFFF47B74)
                              : const Color(0xFFFECA8E),
                        ),
                        child: Center(
                          child: Transform.rotate(
                            angle: -0.785398,
                            child: const Icon(
                              Icons.arrow_forward,
                              size: 12,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        item,
                        style: const TextStyle(
                          fontSize: 12,
                          color: Color(0xFF606060),
                          fontFamily: 'Inter',
                          height: 1.25,
                        ),
                      ),
                    ],
                  ),
                  const Padding(
                    padding: EdgeInsets.only(right: 34),
                    child: Text(
                      ">",
                      style: TextStyle(
                        color: Color(0xFFEDEDED),
                        fontSize:
                            12, // This keeps it aligned and visually balanced
                      ),
                    ),
                  ),
                ],
              ),
            );
          }).toList(),

          const SizedBox(height: 3),

          // Pagination
          Center(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildLeftCircle(Icons.arrow_back),
                const SizedBox(width: 8),
                const Text("...", style: TextStyle(fontSize: 16)),
                const SizedBox(width: 8),
                _buildRightCircle(Icons.arrow_forward),
              ],
            ),
          ),
        ],
      ),
    ),
  );
}

Widget _buildLeftCircle(IconData iconData) {
  return Opacity(
    opacity: 0.6, // Fully visible
    child:
        _buildCircleArrow(iconData, top: 157, left: 109, right: 169, bottom: 8),
  );
}

Widget _buildRightCircle(IconData iconData) {
  return Opacity(
    opacity: 1.0, // 60% opacity
    child:
        _buildCircleArrow(iconData, top: 157, left: 169, right: 109, bottom: 8),
  );
}

// Reusable helper for circular arrow button
Widget _buildCircleArrow(IconData iconData,
    {double? top, double? left, double? right, double? bottom}) {
  return Positioned(
    top: top,
    left: left,
    right: right,
    bottom: bottom,
    child: Container(
      width: 20,
      height: 20,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(color: Colors.black, width: 1),
      ),
      child: Center(
        child: Icon(
          iconData,
          size: 16,
          color: Colors.black,
        ),
      ),
    ),
  );
}

Widget _buildBoxPieChart(String heading, String content) {
  final double completed = 56;
  final double pending = 44;

  final Color completedColor = const Color.fromRGBO(146, 222, 163, 1);
  final Color pendingColor = const Color.fromRGBO(253, 238, 224, 1);

  return SizedBox(
    width: 210,
    height: 188,
    child: Container(
      // padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white, // background
        borderRadius: BorderRadius.circular(12), // border-radius: 12px
        boxShadow: [
          BoxShadow(
            color: const Color(0x1A000000), // #0000001A (10% black)
            offset: const Offset(0, 2), // 0px 2px
            blurRadius: 0,
          ),
        ],
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 16, left: 16),
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                heading,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                  height: 15 / 12, // Line height = 15px
                  fontFamily: 'Inter',
                  letterSpacing: 0.0,
                  color: Color(0xFF606060),
                ),
              ),
            ),
          ),
          //const SizedBox(height: 4),
          Expanded(
            child: Stack(
              alignment: Alignment.center,
              children: [
                f1.PieChart(
                  f1.PieChartData(
                    sectionsSpace: 0,
                    centerSpaceRadius: 30,
                    sections: [
                      f1.PieChartSectionData(
                        value: completed,
                        title: completed.toInt().toString(),
                        color: completedColor,
                        radius: 18,
                        titleStyle: const TextStyle(
                          fontSize: 9,
                          //fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                        titlePositionPercentageOffset: 0.5,
                      ),
                      f1.PieChartSectionData(
                        value: pending,
                        title: pending.toInt().toString(),
                        color: pendingColor,
                        radius: 18,
                        titleStyle: const TextStyle(
                          fontSize: 9,
                          //  fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                        titlePositionPercentageOffset: 0.5,
                      ),
                    ],
                  ),
                ),
                Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'Total',
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 10,
                        height: 1,
                        fontFamily: 'Inter',
                        letterSpacing: 0,
                        color: Color.fromRGBO(170, 169, 169, 1),
                      ),
                    ),
                    Text(
                      content.replaceAll(RegExp(r'^[^\d]*'), ''),
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                        height: 17 / 14,
                        fontFamily: 'Inter',
                        letterSpacing: 0,
                        color: Color.fromRGBO(96, 96, 96, 1),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 4),
          Padding(
            padding:
                EdgeInsets.only(bottom: 13), // Simulate `bottom: 13` spacing
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildLegend(color: pendingColor, label: "Pending"),
                const SizedBox(width: 12),
                _buildLegend(color: completedColor, label: "Completed"),
              ],
            ),
          ),
        ],
      ),
    ),
  );
}

Widget _buildBoxLineChart(String heading) {
  return SizedBox(
    width: 420,
    height: 188,
    child: Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: const [
          BoxShadow(
            color: Color(0x1A000000),
            offset: Offset(0, 2),
            blurRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title and filter row unchanged
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SizedBox(
                width: 195,
                height: 15,
                child: Text(
                  heading,
                  textAlign: TextAlign.left,
                  style: const TextStyle(
                    fontSize: 12,
                    height: 15 / 12,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'Inter',
                    letterSpacing: 0,
                    color: Color(0xFF606060),
                  ),
                ),
              ),
              const Row(
                children: [
                  Padding(
                    padding: EdgeInsets.only(top: 16, left: 16),
                    child: Row(
                      children: [
                        Text("5D | ",
                            style: TextStyle(
                              fontSize: 10,
                              height: 1.6,
                              fontWeight: FontWeight.w600,
                              fontFamily: 'Inter',
                              letterSpacing: 0,
                              color: Colors.black,
                            )),
                        Text("1M | ",
                            style: TextStyle(
                              fontSize: 10,
                              height: 1.6,
                              fontWeight: FontWeight.w600,
                              fontFamily: 'Inter',
                              letterSpacing: 0,
                              color: Colors.black,
                            )),
                        Text("3M | ",
                            style: TextStyle(
                              fontSize: 10,
                              height: 1.6,
                              fontWeight: FontWeight.w600,
                              fontFamily: 'Inter',
                              letterSpacing: 0,
                              color: Colors.black,
                            )),
                        Text("6M | ",
                            style: TextStyle(
                              fontSize: 10,
                              height: 1.6,
                              fontWeight: FontWeight.w600,
                              fontFamily: 'Inter',
                              letterSpacing: 0,
                              color: Colors.black,
                            )),
                        Text("1Y",
                            style: TextStyle(
                              fontSize: 10,
                              height: 1.6,
                              fontWeight: FontWeight.w600,
                              fontFamily: 'Inter',
                              letterSpacing: 0,
                              color: Colors.black,
                              decoration: TextDecoration.underline,
                            )),
                      ],
                    ),
                  ),
                ],
              )
            ],
          ),
          // const SizedBox(height: 2),

          // Chart area + "Hrs" label below
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  flex: 11, // Increased flex for chart to take more space
                  child: Padding(
                    padding: const EdgeInsets.only(
                        left: 4,
                        right: 4,
                        top: 0,
                        bottom: 0), // added bottom padding to push chart down
                    child: f1.LineChart(
                      f1.LineChartData(
                        minX: 0,
                        maxX: 12,
                        minY: 0,
                        maxY: 180,
                        gridData: f1.FlGridData(
                          show: true,
                          drawVerticalLine: true,
                          verticalInterval: 1,
                          drawHorizontalLine: true,
                          horizontalInterval: 20,
                        ),
                        borderData: f1.FlBorderData(show: true),
                        titlesData: f1.FlTitlesData(
                          topTitles: f1.AxisTitles(
                            sideTitles: f1.SideTitles(showTitles: false),
                          ),
                          rightTitles: f1.AxisTitles(
                            sideTitles: f1.SideTitles(showTitles: false),
                          ),
                          bottomTitles: f1.AxisTitles(
                            sideTitles: f1.SideTitles(
                              showTitles: true,
                              reservedSize: 14,
                              interval: 0.5,
                              getTitlesWidget: (value, meta) {
                                const months = [
                                  'Ap',
                                  'Ma',
                                  'Jn',
                                  'Ju',
                                  'Au',
                                  'Se',
                                  'Oc',
                                  'No',
                                  'De',
                                  'Ja',
                                  'Fe',
                                  'Ma',
                                ];
                                if (value % 1 == 0.5) {
                                  int index = value.floor();
                                  if (index >= 0 && index < months.length) {
                                    return Padding(
                                      padding: const EdgeInsets.only(
                                          left:
                                              8), // shifted right to center month label
                                      child: Text(
                                        months[index],
                                        style: const TextStyle(fontSize: 10),
                                      ),
                                    );
                                  }
                                }
                                return const SizedBox.shrink();
                              },
                            ),
                          ),
                          leftTitles: f1.AxisTitles(
                            sideTitles: f1.SideTitles(
                              showTitles: true,
                              reservedSize: 18,
                              interval: 45,
                              getTitlesWidget: (value, meta) {
                                return Text(
                                  '${value.toInt()}',
                                  style: const TextStyle(fontSize: 10),
                                );
                              },
                            ),
                          ),
                        ),
                        lineBarsData: [
                          f1.LineChartBarData(
                            isCurved: true,
                            curveSmoothness: 0.6,
                            spots: const [
                              f1.FlSpot(0, 45),
                              f1.FlSpot(2, 55),
                              f1.FlSpot(4, 39),
                              f1.FlSpot(6, 60),
                              f1.FlSpot(8, 170),
                              f1.FlSpot(11, 45),
                              f1.FlSpot(12, 3),
                            ],
                            barWidth: 3,
                            color: const Color(0xFF66BA04),
                            dotData: f1.FlDotData(show: true),
                          ),
                          f1.LineChartBarData(
                            isCurved: true,
                            curveSmoothness: 0.6,
                            spots: const [
                              f1.FlSpot(0, 90),
                              f1.FlSpot(2, 97),
                              f1.FlSpot(4, 120),
                              f1.FlSpot(6, 50),
                              f1.FlSpot(9, 140),
                              f1.FlSpot(11, 10),
                              f1.FlSpot(12, 170),
                            ],
                            barWidth: 3,
                            color: const Color(0xFF00008B),
                            dotData: f1.FlDotData(show: true),
                          ),
                          f1.LineChartBarData(
                            isCurved: true,
                            curveSmoothness: 0.6,
                            spots: const [
                              f1.FlSpot(0, 20),
                              f1.FlSpot(2, 70),
                              f1.FlSpot(5, 140),
                              f1.FlSpot(7, 100),
                              f1.FlSpot(9, 110),
                              f1.FlSpot(11, 133),
                              f1.FlSpot(12, 160),
                            ],
                            barWidth: 3,
                            color: const Color(0xFFD862FC),
                            dotData: f1.FlDotData(show: true),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

                // "Hrs" label at bottom-left corner with minimal padding
                Expanded(
                  flex: 0,
                  child: Align(
                    alignment: Alignment.bottomLeft,
                    child: Text(
                      'Hrs',
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'Inter',
                        color: Colors.black,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    ),
  );
}

Widget _buildLegend({required Color color, required String label}) {
  return Row(
    children: [
      Container(
        width: 12,
        height: 12,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: color,
        ),
      ),
      const SizedBox(width: 4),
      Text(
        label,
        style: const TextStyle(fontSize: 12, color: Colors.black),
      ),
    ],
  );
}

class TabChartSection extends StatefulWidget {
  const TabChartSection({super.key});

  @override
  State<TabChartSection> createState() => _TabChartSectionState();
}

class _TabChartSectionState extends State<TabChartSection> {
  String selectedTab = 'All';

  @override
  Widget build(BuildContext context) {
    return Container(
      color: const Color.fromRGBO(245, 245, 245, 1),
      child: Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 1300),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 8),

              /// 👇 Left padding added here for both tab section and chart section
              Padding(
                padding: const EdgeInsets.only(
                    left: 42,
                    right:
                        19.5), // Added right padding to align Divider properly
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    bool isSmallScreen = constraints.maxWidth < 500;

                    Widget tabSection = isSmallScreen
                        ? Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Wrap(
                                spacing: 10,
                                runSpacing: 10,
                                children: [
                                  _buildTab("All"),
                                  _buildTab("Pending"),
                                  _buildTab("Completed"),
                                ],
                              ),
                              const SizedBox(height: 10),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Row(
                                    children: [
                                      Container(
                                        width: 14,
                                        height: 14,
                                        child: const Icon(
                                          Icons.filter_alt_outlined,
                                          size: 12,
                                          color: Colors.grey,
                                        ),
                                      ),
                                      const SizedBox(width: 5),
                                      const Text(
                                        "Filter",
                                        style: TextStyle(
                                          fontFamily: 'Inter',
                                          fontSize: 12,
                                          height: 2.0,
                                          fontWeight: FontWeight.normal,
                                          color: Color(0xFF000000),
                                        ),
                                      ),
                                    ],
                                  ),
                                  Row(
                                    children: const [
                                      Text("<",
                                          style: TextStyle(
                                              color: Color(0xFFD0D0D0),
                                              fontSize: 12)),
                                      SizedBox(width: 4),
                                      Text("1 | 3",
                                          style: TextStyle(
                                              fontSize: 10,
                                              fontWeight: FontWeight.w500,
                                              color: Color(0xFF242424))),
                                      SizedBox(width: 4),
                                      Text(">",
                                          style: TextStyle(
                                              color: Colors.black,
                                              fontSize: 12)),
                                    ],
                                  ),
                                ],
                              ),
                            ],
                          )
                        : Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                children: [
                                  _buildTab("All"),
                                  const SizedBox(width: 12),
                                  _buildTab("Pending"),
                                  const SizedBox(width: 12),
                                  _buildTab("Completed"),
                                ],
                              ),
                              Row(
                                children: [
                                  Container(
                                    width: 14,
                                    height: 14,
                                    child: const Icon(
                                      Icons.filter_alt_outlined,
                                      size: 12,
                                      color: Colors.grey,
                                    ),
                                  ),
                                  const SizedBox(width: 5),
                                  const Text(
                                    "Filter",
                                    style: TextStyle(
                                      fontFamily: 'Inter',
                                      fontSize: 12,
                                      height: 2.0,
                                      fontWeight: FontWeight.normal,
                                      color: Color(0xFF000000),
                                    ),
                                  ),
                                  const SizedBox(width: 20),
                                  const Text("<",
                                      style: TextStyle(
                                          color: Color(0xFFD0D0D0),
                                          fontSize: 12)),
                                  const SizedBox(width: 4),
                                  const Text("1 | 3",
                                      style: TextStyle(
                                          fontSize: 10,
                                          fontWeight: FontWeight.w500,
                                          color: Color(0xFF242424))),
                                  const SizedBox(width: 4),
                                  const Text(">",
                                      style: TextStyle(
                                          color: Colors.black, fontSize: 12)),
                                ],
                              ),
                            ],
                          );

                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        tabSection,
                        //          const SizedBox(height: 12),
                        const Divider(
                          color: Color(0xFFEDEDED), // Light grey line
                          thickness: 1.0,
                          height: 0.5,
                        ),
                      ],
                    );
                  },
                ),
              ),

              const SizedBox(height: 16),

              /// 👇 Padding added to chart section too
              Padding(
                padding: const EdgeInsets.only(left: 42),
                child: _buildChartContent(),
              ),
              const SizedBox(height: 40),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTab(String label) {
    final bool isSelected = selectedTab == label;
    return InkWell(
      onTap: () => setState(() => selectedTab = label),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              label,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontFamily: 'Inter',
                fontSize: 14,
                height: 1.714,
                fontWeight: FontWeight.w600,
                color: isSelected ? Colors.black : Colors.grey,
                letterSpacing: 0.0,
                decoration: TextDecoration.none,
              ),
            ),
            // Always reserve space for the underline
            Container(
              //  margin: const EdgeInsets.only(top: 2),
              height: 0.5,
              width: 30, // Adjust underline length here
              color: isSelected ? Colors.black : Colors.transparent,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChartContent() {
    final ScrollController _scrollController = ScrollController();

    return Scrollbar(
      thumbVisibility: true,
      controller: _scrollController,
      child: SingleChildScrollView(
        controller: _scrollController,
        child: LayoutBuilder(
          builder: (context, constraints) {
            double screenWidth = constraints.maxWidth;
            int crossAxisCount = screenWidth ~/ 300;
            double blockWidth =
                (screenWidth - ((crossAxisCount - 1) * 10)) / crossAxisCount;

            return Wrap(
              spacing: 19.5, // Horizontal gap between boxes
              runSpacing: 21, // Vertical gap between rows
              children: List.generate(12, (index) {
                return _workflowBlock(); // Already has fixed width = 292 and height = 133
              }),
            );
          },
        ),
      ),
    );
  }
}

Widget _workflowBlock() {
  return SizedBox(
    width: 292,
    height: 133,
    child: Container(
      // Removed margin to avoid inflating size
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFFFFFFFF),
        boxShadow: const [
          BoxShadow(
            color: Color(0x1A000000), // #0000001A
            offset: Offset(0, 2),
            blurRadius: 0,
          ),
        ],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          /// Title Row
          Row(
            children: [
              Container(
                width: 18,
                height: 18,
                padding: const EdgeInsets.all(2),
                child: SvgPicture.asset(
                  'assets/images/usermanagement.svg',
                  color: Colors.grey,
                ),
              ),
              const SizedBox(width: 8),
              const Expanded(
                child: Text(
                  "User Management Workflow",
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontSize: 14,
                    height: 1.21, // 17px line-height / 14px font-size ≈ 1.21
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF606060),
                    letterSpacing: 0.0,
                  ),
                  textAlign: TextAlign.left,
                ),
              ),
            ],
          ),
          const Spacer(),
          Padding(
            padding:
                const EdgeInsets.only(bottom: 3), // Moves entire row up by 3px
            child: Align(
              alignment: Alignment.centerRight,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SvgPicture.asset(
                    'assets/images/assignedtome.svg',
                    width: 8,
                    height: 8,
                    color: Colors.black54,
                  ),
                  const SizedBox(width: 4),
                  const Padding(
                    padding: EdgeInsets.only(right: 0.5),
                    child: Text(
                      "Assigned to me",
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontSize: 10,
                        height: 1.2,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF606060),
                        letterSpacing: 0.0,
                      ),
                      textAlign: TextAlign.left,
                    ),
                  ),
                ],
              ),
            ),
          ),

          const Divider(
            thickness: 1,
            height: 0, // Ensures height is 0 as per CSS
            color: Color(0xFFE4E4E4),
          ),

          Padding(
            padding: const EdgeInsets.only(top: 5),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: const [
                    Text(
                      "Updated on:",
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontSize: 10,
                        height: 1.2,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFFB3B3B3),
                        letterSpacing: 0.0,
                      ),
                      textAlign: TextAlign.left,
                    ),
                    SizedBox(height: 2),
                    Text(
                      "13/05/2025",
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontSize: 11,
                        height: 1.27,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF606060),
                        letterSpacing: 0.0,
                      ),
                      textAlign: TextAlign.left,
                    ),
                  ],
                ),
                Row(
                  children: [
                    Container(
                      width: 40,
                      height: 20,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        border: Border.all(color: Color(0xFFE4E4E4), width: 1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      alignment: Alignment.center,
                      child: const Text(
                        "1Lo",
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue,
                        ),
                      ),
                    ),
                    const SizedBox(width: 6),
                    Container(
                      width: 64,
                      height: 20,
                      decoration: BoxDecoration(
                        color: Color(0xFFFEF7E2),
                        borderRadius: BorderRadius.circular(13),
                      ),
                      alignment: Alignment.center,
                      child: const Text(
                        "Pending",
                        style: TextStyle(
                          fontSize: 10,
                          color: Color.fromRGBO(206, 143, 0, 1),
                          // fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    ),
  );
}
