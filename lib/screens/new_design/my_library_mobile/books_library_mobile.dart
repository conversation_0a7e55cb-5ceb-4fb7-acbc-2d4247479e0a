import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/l10n/app_localizations.dart';
import 'package:nsl/screens/new_design/my_library_mobile/solutions_library_mobile.dart';
import 'package:nsl/screens/new_design/my_library_mobile/objects_library_mobile.dart';
import 'package:nsl/screens/new_design/my_library_mobile/create_book_mobile.dart';
import 'package:nsl/widgets/common/nsl_knowledge_loader.dart';
import 'package:nsl/widgets/mobile/custom_drawer.dart';
import 'package:nsl/widgets/mobile/mobile_nav_item.dart';

class BookMobile {
  final String title;
  final String subtitle;
  final String imageUrl;
  final bool isDraft;
  final double imageWidth;
  final double imageHeight;

  BookMobile({
    required this.title,
    required this.subtitle,
    required this.imageUrl,
    this.isDraft = false,
    this.imageWidth = 136.0,
    this.imageHeight = 200.0,
  });

  factory BookMobile.fromJson(Map<String, dynamic> json) {
    return BookMobile(
      title: json['title'] as String,
      subtitle: json['subtitle'] as String,
      imageUrl: json['imageUrl'] as String,
      isDraft: json['isDraft'] as bool? ?? false,
      imageWidth: (json['imageWidth'] as num?)?.toDouble() ?? 136.0,
      imageHeight: (json['imageHeight'] as num?)?.toDouble() ?? 200.0,
    );
  }
}

class BooksLibraryMobile extends StatefulWidget {
  const BooksLibraryMobile({super.key, this.showNavigationBar = true});

  final bool showNavigationBar;

  @override
  State<BooksLibraryMobile> createState() => _BooksLibraryMobileState();
}

class _BooksLibraryMobileState extends State<BooksLibraryMobile> {
  late List<BookMobile> books;
  bool isLoading = true;
  int selectedTabIndex = 0; // 0: Books, 1: Solutions, 2: Objects

  // JSON string containing book data
  static const String booksJsonString = '''
{
  "books": [
    {
      "title": "Ecommerce Ecommerce Ecommerce ",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book_01.png",
      "isDraft": false
    },
    {
      "title": "Fashion & Apparel Fashion",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-02.png",
      "isDraft": false
    },
    {
      "title": "Financial Advisory Financial Advisory Financial Advisory ",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-03.png",
      "isDraft": false
    },
    {
      "title": "Home Rentals Home Rentals Home Rentals ",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": true
    },
    {
      "title": "Online Grocery Online Grocery Online Grocery ",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-05.png",
      "isDraft": false
    },
    {
      "title": "Courier & Logistics Couri & Logistics Courier & Logistics ",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-06.png",
      "isDraft": false
    },
    {
      "title": "Automotive Automotive Automotive ",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-07.png",
      "isDraft": true
    },
    {
      "title": "Fitness & Wellness Fit & Wellness ",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-08.png",
      "isDraft": false
    },
    {
      "title": "Real Estate Real Estate",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-09.png",
      "isDraft": false
    },
    {
      "title": "Restaurant & Cafe Restaurant & Cafe",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    }
  ]
}
''';

  @override
  void initState() {
    super.initState();
    _loadBooks();
  }

  void _loadBooks() {
    try {
      // Parse the JSON string
      final data = json.decode(booksJsonString);

      // Convert to BookMobile objects
      final List<BookMobile> loadedBooks = (data['books'] as List)
          .map((bookJson) => BookMobile.fromJson(bookJson))
          .toList();

      setState(() {
        books = loadedBooks;
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        books = [];
        isLoading = false;
      });
      // Log error in a production-safe way
      debugPrint('Error loading books: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor:
          widget.showNavigationBar ? Color(0xfff6f6f6) : Colors.transparent,
      drawer: widget.showNavigationBar ? const CustomDrawer() : null,
      appBar: widget.showNavigationBar ? _buildAppBar() : null,
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.showNavigationBar) _buildTopNavigation(),
          if (widget.showNavigationBar) _buildSearchAndCreateSection(),
          _buildBooksGrid(),
        ],
      ),
      floatingActionButton: widget.showNavigationBar
          ? SizedBox(
              width: 46,
              height: 46,
              child: FloatingActionButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const CreateBookMobile(),
                    ),
                  );
                },
                backgroundColor: const Color(0xff0058FF),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(30),
                ),
                child: const Icon(Icons.add),
              ),
            )
          : null,
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Color(0xfff6f6f6),
      surfaceTintColor: Colors.transparent,
      foregroundColor: Colors.black,
      elevation: 0,
      automaticallyImplyLeading: false,
      titleSpacing: 0,
      title: Row(
        children: [
          // Hamburger menu icon
          Builder(
            builder: (context) => IconButton(
              icon: const Icon(Icons.menu, color: Colors.black, size: 24),
              onPressed: () => Scaffold.of(context).openDrawer(),
              padding: const EdgeInsets.symmetric(horizontal: 16),
            ),
          ),
          // Expanded widget to center the title
          Expanded(
            child: Text(
              AppLocalizations.of(context).translate('library.pageTitle'),
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                fontFamily: 'TiemposText',
                color: Colors.black,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          // Invisible spacer to balance the layout (same width as menu icon)
          const SizedBox(width: 56), // IconButton default width
        ],
      ),
    );
  }

  Widget _buildTopNavigation() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          MobileNavItem(
            iconPath: 'assets/images/books-icon.svg',
            label: AppLocalizations.of(context).translate('library.books'),
            isActive: selectedTabIndex == 0,
            onTap: () {
              // Already on books screen, no navigation needed
            },
          ),
          MobileNavItem(
            iconPath: 'assets/images/square-box-uncheck.svg',
            label: AppLocalizations.of(context).translate('library.solutions'),
            isActive: selectedTabIndex == 1,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SolutionsLibraryMobile(),
                ),
              );
            },
          ),
          MobileNavItem(
            iconPath: 'assets/images/cube-box.svg',
            label: AppLocalizations.of(context).translate('library.objects'),
            isActive: selectedTabIndex == 2,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const ObjectsLibraryMobile(),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndCreateSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          // Search bar
          Container(
            height: 40,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Row(
              children: [
                // Search text field
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(left: 16.0),
                    child: TextField(
                      decoration: InputDecoration(
                        enabledBorder: InputBorder.none,
                        focusedBorder: InputBorder.none,
                        hintText: 'Search',
                        border: InputBorder.none,
                        hintStyle:
                            TextStyle(fontSize: 14, color: Colors.grey[500]),
                        isDense: true,
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                  ),
                ),
                // Search and filter icons
                _MobileSvgButton(
                  iconPath: 'assets/images/search.svg',
                  onPressed: () {},
                  size: 20,
                ),
                Container(
                  height: 24,
                  width: 1,
                  color: Colors.grey.shade200,
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                ),
                _MobileSvgButton(
                  iconPath: 'assets/images/filter-icon.svg',
                  onPressed: () {},
                  size: 24,
                ),
                const SizedBox(width: 8),
              ],
            ),
          ),
          const SizedBox(height: 12),
          // Create button
          // SizedBox(
          //   width: double.infinity,
          //   height: 44,
          //   child: ElevatedButton(
          //     onPressed: () {},
          //     style: ElevatedButton.styleFrom(
          //       backgroundColor: const Color(0xff0058FF),
          //       foregroundColor: Colors.white,
          //       elevation: 0,
          //       shape: RoundedRectangleBorder(
          //         borderRadius: BorderRadius.circular(6),
          //       ),
          //     ),
          //     child: Text(
          //       AppLocalizations.of(context)
          //           .translate('library.createButtonText'),
          //       style: const TextStyle(
          //         fontSize: 16,
          //         fontWeight: FontWeight.w500,
          //         fontFamily: 'TiemposText',
          //       ),
          //     ),
          //   ),
          // ),
        ],
      ),
    );
  }

  Widget _buildBooksGrid() {
    return Expanded(
        child: NSLKnowledgeLoaderWrapper(
      isLoading: isLoading,
      child: SingleChildScrollView(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Book grid (loading handled by wrapper)
              books.isEmpty
                  ? const Center(child: Text('No books found'))
                  : _buildMobileBookGrid(),
              const SizedBox(height: 20),
              // Pagination
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _MobilePaginationButton(
                    icon: const Icon(Icons.chevron_left, size: 20),
                    onPressed: () {
                      // Handle previous page
                    },
                  ),
                  const SizedBox(width: 8),
                  _MobilePaginationButton(
                    icon: const Icon(Icons.chevron_right, size: 20),
                    onPressed: () {
                      // Handle next page
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    ));
  }

  Widget _buildMobileBookGrid() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Determine number of books per row based on screen width
        int booksPerRow = MediaQuery.of(context).size.width >= 375 ? 3 : 2;

        // Calculate rows needed
        int rowCount = (books.length / booksPerRow).ceil();

        // Calculate the width available for each book
        double availableWidth = constraints.maxWidth;
        double spacing = 16.0;
        double bookWidth =
            (availableWidth - (spacing * (booksPerRow - 1))) / booksPerRow;

        return Column(
          children: List.generate(rowCount, (rowIndex) {
            // Calculate start and end indices for this row
            int startIndex = rowIndex * booksPerRow;
            int endIndex = (startIndex + booksPerRow <= books.length)
                ? startIndex + booksPerRow
                : books.length;

            // Create a list of books for this row
            List<BookMobile> rowBooks = books.sublist(startIndex, endIndex);

            return Padding(
              padding: const EdgeInsets.only(bottom: 24.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: rowBooks.asMap().entries.map((entry) {
                  BookMobile book = entry.value;

                  return SizedBox(
                    width: bookWidth,
                    child: _MobileBookCard(
                      onTap: () {
                        // Handle book tap
                      },
                      child: _buildMobileBookCard(
                        title: book.title,
                        subtitle: book.subtitle,
                        imageUrl: book.imageUrl,
                        isDraft: book.isDraft,
                        imageWidth: bookWidth,
                        imageHeight:
                            (bookWidth * 200) / 136, // Maintain aspect ratio
                        index: books.indexOf(book),
                      ),
                    ),
                  );
                }).toList(),
              ),
            );
          }),
        );
      },
    );
  }

  Widget _buildMobileBookCard({
    required String title,
    required String subtitle,
    required String imageUrl,
    bool isDraft = false,
    double imageWidth = 136.0,
    double imageHeight = 200.0,
    int index = 0,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Book cover
        Stack(
          children: [
            Container(
              width: imageWidth,
              height: imageHeight,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: const BorderRadius.only(
                  topRight: Radius.circular(24),
                  topLeft: Radius.circular(12),
                  bottomLeft: Radius.circular(12),
                  bottomRight: Radius.circular(12),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(26),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
                image: DecorationImage(
                  image: AssetImage(imageUrl),
                  fit: BoxFit.cover,
                ),
              ),
            ),
            if (isDraft)
              Positioned(
                top: 8,
                right: 14,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.amber,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Text(
                    'Draft',
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                      fontFamily: "TiemposText",
                    ),
                  ),
                ),
              ),
          ],
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: imageWidth,
          child: Text(
            title,
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 14,
              color: Colors.black,
              fontFamily: "TiemposText",
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        const SizedBox(height: 2),
        SizedBox(
          width: imageWidth,
          child: Text(
            subtitle,
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 14,
              color: Colors.black,
              fontFamily: "TiemposText",
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}

// Mobile SVG Button Widget
class _MobileSvgButton extends StatefulWidget {
  final String iconPath;
  final VoidCallback onPressed;
  final double size;

  const _MobileSvgButton({
    required this.iconPath,
    required this.onPressed,
    this.size = 18,
  });

  @override
  State<_MobileSvgButton> createState() => _MobileSvgButtonState();
}

class _MobileSvgButtonState extends State<_MobileSvgButton> {
  bool isPressed = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => setState(() => isPressed = true),
      onTapUp: (_) => setState(() => isPressed = false),
      onTapCancel: () => setState(() => isPressed = false),
      onTap: widget.onPressed,
      child: Container(
        height: 32,
        width: 32,
        decoration: BoxDecoration(
          border: Border.all(
            color: isPressed ? const Color(0xff0058FF) : Colors.transparent,
            width: 1.0,
          ),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Center(
          child: SvgPicture.asset(
            widget.iconPath,
            width: widget.size,
            height: widget.size,
          ),
        ),
      ),
    );
  }
}

// Mobile Pagination Button Widget
class _MobilePaginationButton extends StatefulWidget {
  final Icon icon;
  final VoidCallback onPressed;

  const _MobilePaginationButton({
    required this.icon,
    required this.onPressed,
  });

  @override
  State<_MobilePaginationButton> createState() =>
      _MobilePaginationButtonState();
}

class _MobilePaginationButtonState extends State<_MobilePaginationButton> {
  bool isPressed = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => setState(() => isPressed = true),
      onTapUp: (_) => setState(() => isPressed = false),
      onTapCancel: () => setState(() => isPressed = false),
      onTap: widget.onPressed,
      child: Container(
        width: 32,
        height: 32,
        decoration: BoxDecoration(
          border: Border.all(
            color: isPressed ? const Color(0xff0058FF) : Colors.grey.shade300,
            width: 1.0,
          ),
          borderRadius: isPressed ? BorderRadius.zero : null,
          color: Colors.white,
        ),
        child: Center(
          child: Icon(
            widget.icon.icon,
            color: isPressed ? const Color(0xff0058FF) : Colors.black,
            size: widget.icon.size,
          ),
        ),
      ),
    );
  }
}

// Mobile Book Card Widget
class _MobileBookCard extends StatefulWidget {
  final Widget child;
  final VoidCallback onTap;

  const _MobileBookCard({
    required this.child,
    required this.onTap,
  });

  @override
  State<_MobileBookCard> createState() => _MobileBookCardState();
}

class _MobileBookCardState extends State<_MobileBookCard> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      child: widget.child,
    );
  }
}
