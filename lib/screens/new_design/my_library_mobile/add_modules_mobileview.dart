import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class AddModulesMobileView extends StatefulWidget {
  const AddModulesMobileView({super.key});

  @override
  State<AddModulesMobileView> createState() => _AddModulesMobileViewState();
}

class _AddModulesMobileViewState extends State<AddModulesMobileView> {
  final TextEditingController _searchController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black, size: 16),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Ecommerce (B2C)',
          style: TextStyle(
            color: Colors.black,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
   actions: [
        Container(
          margin: EdgeInsets.only(right: 16),
          child: Icon<PERSON><PERSON>on(
            icon: SvgPicture.asset(
              '../../assets/images/expand-arrow-left-new.svg', 
              width: 20,
              height: 20,
              color: Colors.black, 
            ),
            onPressed: () {},
          ),
        ),
      ],
      ),
      body: Column(
        children: [
          // Add Modules Section
          Container(
            margin: const EdgeInsets.fromLTRB(16, 16, 16, 8),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: const Color(0xFFE5E7EB), width: 1),
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Row(
                children: [
                  Container(
                     width: 16,
                      height: 16,
                      child: SvgPicture.asset(
                        'assets/images/book_nsl.svg',
                        width: 16,
                        height: 16,
                        color: Colors.grey[600],
                      ),
                    // decoration: BoxDecoration(
                    //   border: Border.all(color: Colors.grey[400]!, width: 1),
                    //   borderRadius: BorderRadius.circular(2),
                    // ),
                  ),
                  const SizedBox(width: 8),
                  const Text(
                    'Add Modules',
                    style: TextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.w500,
                      color: Colors.black87,
                    ),
                  ),
                  const Spacer(),
                  Icon(
                    Icons.arrow_forward,
                    color: Colors.blue,
                    size: 16,
                  ),
                ],
              ),
            ),
          ),
          // Stats Section
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                Expanded(
                  child: Container(
                    margin: const EdgeInsets.only(right: 4),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: const Color(0xFFE5E7EB), width: 1),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                      child: _buildStatItem('Agent', '3'),
                    ),
                  ),
                ),
                Expanded(
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 4),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: const Color(0xFFE5E7EB), width: 1),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                      child: _buildStatItem('Objects', '12'),
                    ),
                  ),
                ),
                Expanded(
                  child: Container(
                    margin: const EdgeInsets.only(left: 4),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: const Color(0xFFE5E7EB), width: 1),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                      child: _buildStatItem('Solutions', '15'),
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),

          // Create Solution Section
          Container(
            margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: const Color(0xFFE5E7EB), width: 1),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Create a solution of a Product management',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Last Message 16 hours ago',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Container(
                        width: 16,
                        height: 16,
                        child: SvgPicture.asset(
                          'assets/images/folder.svg',
                          width: 16,
                          height: 16,
                          color: Colors.grey[600],
                        ),
                      // decoration: BoxDecoration(
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'V00012',
                        style: TextStyle(
                          fontSize: 11,
                          color: Colors.grey[600],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const Spacer(),
                      Text(
                        '22/04/2025',
                        style: TextStyle(
                          fontSize: 11,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          // Spacer
          Expanded(
            child: Container(
              color: const Color(0xFFF5F5F5),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    IconData iconData;
    switch (label) {
      case 'Agent':
        iconData = Icons.group_outlined;
        break;
      case 'Objects':
        iconData = Icons.language_outlined;
        break;
      case 'Solutions':
        iconData = Icons.account_tree_outlined;
        break;
      default:
        iconData = Icons.circle_outlined;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Icon(
              iconData,
              size: 14,
              color: Colors.black87,
            ),
            const SizedBox(width: 4),
            Text(
              value,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.black,
              ),
            ),
          ],
        ),
        const SizedBox(height: 2),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: Colors.black,
                fontWeight: FontWeight.w400,
              ),
            ),
            const SizedBox(width: 4),
            Icon(
              Icons.arrow_forward,
              color: Colors.blue,
              size: 14,
            ),
          ],
        ),
      ],
    );
  }


  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
