// import 'package:flutter/material.dart';
// import 'package:provider/provider.dart';
// import 'package:flutter_colorpicker/flutter_colorpicker.dart';
// import 'dart:convert';
// import 'dart:math';
// import 'package:flutter/services.dart';
// import '../models/line_selection.dart';
// import '../models/solution.dart';
// import '../models/mapping.dart';
// import '../providers/solution_provider.dart';
// import '../providers/mapping_provider.dart';
// import '../widgets/code_viewer.dart';
// import '../services/export_service.dart';
// import '../services/highlight_service.dart';
// import 'edit_mapping_screen.dart';
// import 'edit_solution_screen.dart';
// import 'preview_screen.dart';
// import 'package:flutter/rendering.dart';

// // Class to store line content and index together

// class SolutionDetailScreen extends StatefulWidget {
//   final String solutionId;

//   const SolutionDetailScreen({super.key, required this.solutionId});

//   @override
//   _SolutionDetailScreenState createState() => _SolutionDetailScreenState();
// }

// class _SolutionDetailScreenState extends State<SolutionDetailScreen> {
//   bool _isEditMode = false;
//   bool _wordSelectionMode = false; // Toggle between word and line selection
//   List<Mapping> _selectedMappings = [];
//   List<String> _selectedNlpLines = [];
//   List<LineSelection> _selectedJavaLines = [];
//   List<LineSelection> _selectedSqlLines = [];
//   String _selectedColor = "#3498DB"; // Default blue color
//   Map<String, List<int>> _selectedLineIndices =
//       {}; // Map of selected lines to their indices
//   Solution? _solution;
//   bool _isMultiSelectMode = false; // For multi-line selection
//   Set<int> _selectedLineNumbers = {}; // For tracking selected line numbers
//   int? _rangeStartLine; // Track the start of range selection
//   bool _isRangeSelectionMode = false; // Track if we're in range selection mode

//   @override
//   Widget build(BuildContext context) {
//     return Consumer2<SolutionProvider, MappingProvider>(
//       builder: (context, solutionProvider, mappingProvider, child) {
//         final solution = solutionProvider.getSolutionById(widget.solutionId);
//         final allMappings = mappingProvider.getMappingsForSolution(
//           widget.solutionId,
//         );

//         if (solution == null) {
//           return Scaffold(
//             appBar: AppBar(title: Text('Solution Not Found')),
//             body: Center(child: Text('The requested solution was not found.')),
//           );
//         }

//         return Scaffold(
//           appBar: AppBar(
//             title: Text(solution.title),
//             actions: [
//               // Edit Solution button
//               IconButton(
//                 icon: Icon(Icons.edit_document),
//                 tooltip: 'Edit Solution',
//                 onPressed: () => _navigateToEditSolution(context, solution.id),
//               ),
//               // Preview button
//               IconButton(
//                 icon: Icon(Icons.visibility),
//                 tooltip: 'Preview',
//                 onPressed: () => _openPreview(context, solution, allMappings),
//               ),
//               // Export button
//               IconButton(
//                 icon: Icon(Icons.download),
//                 tooltip: 'Export JSON',
//                 onPressed:
//                     () => _exportSolution(context, solution, allMappings),
//               ),
//               // Clear All Mappings button (only in edit mode)
//               if (_isEditMode)
//                 IconButton(
//                   icon: Icon(Icons.delete_sweep),
//                   tooltip: 'Clear All Mappings',
//                   onPressed:
//                       () =>
//                           _showClearAllMappingsDialog(context, mappingProvider),
//                 ),
//               // Toggle edit mode
//               IconButton(
//                 icon: Icon(_isEditMode ? Icons.done : Icons.edit),
//                 tooltip: _isEditMode ? 'Done Editing' : 'Edit Mappings',
//                 onPressed: () {
//                   setState(() {
//                     _isEditMode = !_isEditMode;
//                     // Clear selections when toggling edit mode
//                     if (!_isEditMode) {
//                       _clearSelections();
//                     }
//                   });
//                 },
//               ),
//               // Toggle phrase selection mode (only in edit mode)
//               if (_isEditMode)
//                 IconButton(
//                   icon: Icon(
//                     _wordSelectionMode ? Icons.text_fields : Icons.subject,
//                   ),
//                   tooltip:
//                       _wordSelectionMode
//                           ? 'Switch to Line Selection'
//                           : 'Switch to Phrase Selection',
//                   onPressed: () {
//                     setState(() {
//                       _wordSelectionMode = !_wordSelectionMode;
//                       // Clear selections when toggling selection mode
//                       _clearSelections();
//                     });
//                   },
//                 ),
//               // Toggle multi-select mode (only in edit mode)
//               if (_isEditMode)
//                 IconButton(
//                   icon: Icon(
//                     _isMultiSelectMode
//                         ? Icons.select_all
//                         : Icons.check_box_outline_blank,
//                   ),
//                   tooltip:
//                       _isMultiSelectMode
//                           ? 'Disable Multi-Select'
//                           : 'Enable Multi-Select',
//                   onPressed: () {
//                     setState(() {
//                       _isMultiSelectMode = !_isMultiSelectMode;
//                       if (!_isMultiSelectMode) {
//                         _selectedLineNumbers.clear();
//                       }
//                     });
//                   },
//                 ),
//               // Add new mapping (only in edit mode)
//               if (_isEditMode)
//                 IconButton(
//                   icon: Icon(Icons.add),
//                   onPressed:
//                       _canCreateMapping()
//                           ? () => _navigateToAddMapping(context, solution)
//                           : null,
//                 ),
//             ],
//           ),
//           body: Column(
//             children: [
//               Expanded(
//                 child: Row(
//                   children: [
//                     // Left panel (NLP)
//                     Expanded(
//                       flex: 1,
//                       child: Column(
//                         crossAxisAlignment: CrossAxisAlignment.start,
//                         children: [
//                           Padding(
//                             padding: EdgeInsets.all(8.0),
//                             child: Text(
//                               'Natural Language Prescriptive',
//                               style: TextStyle(fontWeight: FontWeight.bold),
//                             ),
//                           ),
//                           Expanded(
//                             child: CodeViewer(
//                               code: solution.nlp,
//                               language: 'plaintext',
//                               highlightedMappings: _selectedMappings,
//                               onLineSelected: (text, lineIndex) {
//                                 if (_isEditMode) {
//                                   setState(() {
//                                     // Store the line index
//                                     if (!_selectedLineIndices.containsKey(
//                                       text,
//                                     )) {
//                                       _selectedLineIndices[text] = [];
//                                     }
//                                     if (!_selectedLineIndices[text]!.contains(
//                                       lineIndex,
//                                     )) {
//                                       _selectedLineIndices[text]!.add(
//                                         lineIndex,
//                                       );
//                                     }

//                                     // For NLP, we only allow one selection at a time
//                                     if (_wordSelectionMode) {
//                                       // In word selection mode, we want to select the word with its position
//                                       // The text will be in the format "word:position"
//                                       _selectedNlpLines = [
//                                         text,
//                                       ]; // Replace the list with the selected word
//                                     } else {
//                                       // In line selection mode, we select the entire line
//                                       _selectedNlpLines = [
//                                         text,
//                                       ]; // Replace the list with a single line
//                                     }
//                                     _updateSelectedMappings(mappingProvider);
//                                   });
//                                 } else {
//                                   // For non-edit mode, we need to handle the word position information
//                                   // Extract just the word part if it contains position information
//                                   String wordToUse = text;
//                                   if (text.contains(":")) {
//                                     wordToUse = text.split(":")[0];
//                                   }
//                                   // Store the line index
//                                   if (!_selectedLineIndices.containsKey(
//                                     wordToUse,
//                                   )) {
//                                     _selectedLineIndices[wordToUse] = [];
//                                   }
//                                   if (!_selectedLineIndices[wordToUse]!
//                                       .contains(lineIndex)) {
//                                     _selectedLineIndices[wordToUse]!.add(
//                                       lineIndex,
//                                     );
//                                   }
//                                   _handleNlpLineSelection(
//                                     wordToUse,
//                                     mappingProvider,
//                                   );
//                                 }
//                               },
//                               selectedColor:
//                                   _isEditMode ? _selectedColor : null,
//                               selectedLines:
//                                   _isEditMode ? _selectedNlpLines : [],
//                               selectedLineIndices: _selectedLineIndices,
//                               allowWordSelection:
//                                   _isEditMode && _wordSelectionMode,
//                               isInWordSelectionMode: _wordSelectionMode,
//                               isMultiSelectMode: _isMultiSelectMode,
//                               selectedLineNumbers: _selectedLineNumbers,
//                             ),
//                           ),
//                         ],
//                       ),
//                     ),
//                     // Middle panel (Java)
//                     Expanded(
//                       flex: 1,
//                       child: Column(
//                         crossAxisAlignment: CrossAxisAlignment.start,
//                         children: [
//                           Padding(
//                             padding: EdgeInsets.all(8.0),
//                             child: Text(
//                               'Java Code',
//                               style: TextStyle(fontWeight: FontWeight.bold),
//                             ),
//                           ),
//                           Expanded(
//                             child: CodeViewer(
//                               code: solution.java.code,
//                               language: 'java',
//                               highlightedMappings: _selectedMappings,
//                               onLineSelected: (text, lineIndex) {
//                                 if (_isEditMode) {
//                                   setState(() {
//                                     // Store the line index
//                                     if (!_selectedLineIndices.containsKey(
//                                       text,
//                                     )) {
//                                       _selectedLineIndices[text] = [];
//                                     }
//                                     if (!_selectedLineIndices[text]!.contains(
//                                       lineIndex,
//                                     )) {
//                                       _selectedLineIndices[text]!.add(
//                                         lineIndex,
//                                       );
//                                     }

//                                     // Create a LineSelection object for this line
//                                     final lineSelection = LineSelection(
//                                       text,
//                                       lineIndex,
//                                     );

//                                     // Check if this line is already selected
//                                     if (_selectedJavaLines.any(
//                                       (item) =>
//                                           item.content == text &&
//                                           item.lineIndex == lineIndex,
//                                     )) {
//                                       // If already selected, remove it
//                                       _selectedJavaLines.removeWhere(
//                                         (item) =>
//                                             item.content == text &&
//                                             item.lineIndex == lineIndex,
//                                       );
//                                     } else {
//                                       // If not selected, add it
//                                       _selectedJavaLines.add(lineSelection);
//                                     }
//                                     _updateSelectedMappings(mappingProvider);
//                                   });
//                                 } else {
//                                   // Store the line index
//                                   if (!_selectedLineIndices.containsKey(text)) {
//                                     _selectedLineIndices[text] = [];
//                                   }
//                                   if (!_selectedLineIndices[text]!.contains(
//                                     lineIndex,
//                                   )) {
//                                     _selectedLineIndices[text]!.add(lineIndex);
//                                   }
//                                   _handleJavaLineSelection(
//                                     text,
//                                     mappingProvider,
//                                   );
//                                 }
//                               },
//                               selectedColor:
//                                   _isEditMode ? _selectedColor : null,
//                               selectedLines:
//                                   _isEditMode ? _selectedJavaLines : [],
//                               selectedLineIndices: _selectedLineIndices,
//                               allowWordSelection:
//                                   _isEditMode && _wordSelectionMode,
//                               isInWordSelectionMode: _wordSelectionMode,
//                               isMultiSelectMode: _isMultiSelectMode,
//                               selectedLineNumbers: _selectedLineNumbers,
//                             ),
//                           ),
//                         ],
//                       ),
//                     ),
//                     // Right panel (SQL)
//                     Expanded(
//                       flex: 1,
//                       child: Column(
//                         crossAxisAlignment: CrossAxisAlignment.start,
//                         children: [
//                           Padding(
//                             padding: EdgeInsets.all(8.0),
//                             child: Text(
//                               'SQL Code',
//                               style: TextStyle(fontWeight: FontWeight.bold),
//                             ),
//                           ),
//                           Expanded(
//                             child: CodeViewer(
//                               code: solution.sql.code,
//                               language: 'sql',
//                               highlightedMappings: _selectedMappings,
//                               onLineSelected: (text, lineIndex) {
//                                 if (_isEditMode) {
//                                   setState(() {
//                                     // Store the line index
//                                     if (!_selectedLineIndices.containsKey(
//                                       text,
//                                     )) {
//                                       _selectedLineIndices[text] = [];
//                                     }
//                                     if (!_selectedLineIndices[text]!.contains(
//                                       lineIndex,
//                                     )) {
//                                       _selectedLineIndices[text]!.add(
//                                         lineIndex,
//                                       );
//                                     }

//                                     // Create a LineSelection object for this line
//                                     final lineSelection = LineSelection(
//                                       text,
//                                       lineIndex,
//                                     );

//                                     // Check if this line is already selected
//                                     if (_selectedSqlLines.any(
//                                       (item) =>
//                                           item.content == text &&
//                                           item.lineIndex == lineIndex,
//                                     )) {
//                                       // If already selected, remove it
//                                       _selectedSqlLines.removeWhere(
//                                         (item) =>
//                                             item.content == text &&
//                                             item.lineIndex == lineIndex,
//                                       );
//                                     } else {
//                                       // If not selected, add it
//                                       _selectedSqlLines.add(lineSelection);
//                                     }
//                                     _updateSelectedMappings(mappingProvider);
//                                   });
//                                 } else {
//                                   // Store the line index
//                                   if (!_selectedLineIndices.containsKey(text)) {
//                                     _selectedLineIndices[text] = [];
//                                   }
//                                   if (!_selectedLineIndices[text]!.contains(
//                                     lineIndex,
//                                   )) {
//                                     _selectedLineIndices[text]!.add(lineIndex);
//                                   }
//                                   _handleSqlLineSelection(
//                                     text,
//                                     mappingProvider,
//                                   );
//                                 }
//                               },
//                               selectedColor:
//                                   _isEditMode ? _selectedColor : null,
//                               selectedLines:
//                                   _isEditMode ? _selectedSqlLines : [],
//                               selectedLineIndices: _selectedLineIndices,
//                               allowWordSelection:
//                                   _isEditMode && _wordSelectionMode,
//                               isInWordSelectionMode: _wordSelectionMode,
//                               isMultiSelectMode: _isMultiSelectMode,
//                               selectedLineNumbers: _selectedLineNumbers,
//                             ),
//                           ),
//                         ],
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//             ],
//           ),
//           // Status bar showing current selections in edit mode
//           bottomNavigationBar: _isEditMode ? _buildSelectionStatusBar() : null,
//         );
//       },
//     );
//   }

//   Widget _buildSelectionStatusBar() {
//     return Container(
//       padding: EdgeInsets.all(8.0),
//       color: Colors.grey.shade200,
//       child: Column(
//         mainAxisSize: MainAxisSize.min,
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Row(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             children: [
//               Text(
//                 'Current Selection:',
//                 style: TextStyle(fontWeight: FontWeight.bold),
//               ),
//               Row(
//                 children: [
//                   Text('Selection Mode: '),
//                   Text(
//                     _wordSelectionMode ? 'Phrase Selection' : 'Line Selection',
//                     style: TextStyle(fontWeight: FontWeight.bold),
//                   ),
//                   SizedBox(width: 8),
//                   ElevatedButton.icon(
//                     icon: Icon(
//                       _wordSelectionMode ? Icons.subject : Icons.text_fields,
//                     ),
//                     label: Text(
//                       _wordSelectionMode
//                           ? 'Switch to Line'
//                           : 'Switch to Phrase',
//                     ),
//                     onPressed: () {
//                       setState(() {
//                         _wordSelectionMode = !_wordSelectionMode;
//                         _clearSelections();
//                       });
//                     },
//                   ),
//                 ],
//               ),
//             ],
//           ),
//           SizedBox(height: 4),

//           // Selected lines info
//           Row(
//             children: [
//               Expanded(
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     // NLP selection info
//                     Row(
//                       children: [
//                         Text('NLP: '),
//                         Container(
//                           padding: EdgeInsets.symmetric(
//                             horizontal: 8,
//                             vertical: 2,
//                           ),
//                           decoration: BoxDecoration(
//                             color: HighlightService.getColorFromHex(
//                               _selectedColor,
//                             ).withOpacity(0.3),
//                             borderRadius: BorderRadius.circular(4),
//                           ),
//                           child: Text(
//                             _selectedNlpLines.isEmpty
//                                 ? 'None'
//                                 : _wordSelectionMode
//                                 ? '"${_selectedNlpLines.first.contains(":") ? _selectedNlpLines.first.split(":")[0] : _selectedNlpLines.first}"'
//                                 : '${_selectedNlpLines.length} line(s)',
//                             style: TextStyle(fontWeight: FontWeight.bold),
//                           ),
//                         ),
//                         if (_selectedNlpLines.isNotEmpty &&
//                             _selectedMappings.isNotEmpty)
//                           Padding(
//                             padding: EdgeInsets.only(left: 8),
//                             child: Text(
//                               '(Mapped)',
//                               style: TextStyle(
//                                 color: Colors.green,
//                                 fontStyle: FontStyle.italic,
//                               ),
//                             ),
//                           ),
//                       ],
//                     ),
//                     SizedBox(height: 8),

//                     // Java selection info
//                     Row(
//                       children: [
//                         Text('Java: '),
//                         Container(
//                           padding: EdgeInsets.symmetric(
//                             horizontal: 8,
//                             vertical: 2,
//                           ),
//                           decoration: BoxDecoration(
//                             color: HighlightService.getColorFromHex(
//                               _selectedColor,
//                             ).withOpacity(0.3),
//                             borderRadius: BorderRadius.circular(4),
//                           ),
//                           child: Text(
//                             _selectedJavaLines.isEmpty
//                                 ? 'None'
//                                 : '${_selectedJavaLines.length} line(s)',
//                             style: TextStyle(fontWeight: FontWeight.bold),
//                           ),
//                         ),
//                         if (_selectedJavaLines.isNotEmpty &&
//                             _selectedMappings.isNotEmpty)
//                           Padding(
//                             padding: EdgeInsets.only(left: 8),
//                             child: Text(
//                               '(Mapped)',
//                               style: TextStyle(
//                                 color: Colors.green,
//                                 fontStyle: FontStyle.italic,
//                               ),
//                             ),
//                           ),
//                       ],
//                     ),
//                     SizedBox(height: 8),

//                     // SQL selection info
//                     Row(
//                       children: [
//                         Text('SQL: '),
//                         Container(
//                           padding: EdgeInsets.symmetric(
//                             horizontal: 8,
//                             vertical: 2,
//                           ),
//                           decoration: BoxDecoration(
//                             color: HighlightService.getColorFromHex(
//                               _selectedColor,
//                             ).withOpacity(0.3),
//                             borderRadius: BorderRadius.circular(4),
//                           ),
//                           child: Text(
//                             _selectedSqlLines.isEmpty
//                                 ? 'None'
//                                 : '${_selectedSqlLines.length} line(s)',
//                             style: TextStyle(fontWeight: FontWeight.bold),
//                           ),
//                         ),
//                         if (_selectedSqlLines.isNotEmpty &&
//                             _selectedMappings.isNotEmpty)
//                           Padding(
//                             padding: EdgeInsets.only(left: 8),
//                             child: Text(
//                               '(Mapped)',
//                               style: TextStyle(
//                                 color: Colors.green,
//                                 fontStyle: FontStyle.italic,
//                               ),
//                             ),
//                           ),
//                       ],
//                     ),
//                   ],
//                 ),
//               ),

//               // Color selection
//               Column(
//                 crossAxisAlignment: CrossAxisAlignment.end,
//                 children: [
//                   Text(
//                     'Mapping Color:',
//                     style: TextStyle(fontWeight: FontWeight.bold),
//                   ),
//                   SizedBox(height: 4),
//                   Row(
//                     children: [
//                       // Current color preview
//                       Container(
//                         width: 40,
//                         height: 40,
//                         decoration: BoxDecoration(
//                           color: HighlightService.getColorFromHex(
//                             _selectedColor,
//                           ),
//                           shape: BoxShape.circle,
//                           border: Border.all(color: Colors.grey),
//                           boxShadow: [
//                             BoxShadow(color: Colors.black26, blurRadius: 3),
//                           ],
//                         ),
//                       ),
//                       SizedBox(width: 8),
//                       // Color picker button
//                       ElevatedButton.icon(
//                         icon: Icon(Icons.color_lens),
//                         label: Text('Change Color'),
//                         onPressed: () => _openColorPicker(context),
//                       ),
//                     ],
//                   ),
//                 ],
//               ),
//             ],
//           ),

//           SizedBox(height: 16),

//           // Action buttons
//           Row(
//             mainAxisAlignment: MainAxisAlignment.end,
//             children: [
//               ElevatedButton(onPressed: _clearSelections, child: Text('Clear')),
//               SizedBox(width: 8),
//               ElevatedButton(
//                 onPressed:
//                     _canCreateMapping()
//                         ? () => _navigateToAddMapping(
//                           context,
//                           Provider.of<SolutionProvider>(
//                             context,
//                             listen: false,
//                           ).getSolutionById(widget.solutionId)!,
//                         )
//                         : null,
//                 child: Text('Create Mapping'),
//               ),
//             ],
//           ),
//         ],
//       ),
//     );
//   }

//   // Open color picker dialog
//   void _openColorPicker(BuildContext context) {
//     Color pickerColor = HighlightService.getColorFromHex(_selectedColor);

//     showDialog(
//       context: context,
//       builder: (BuildContext context) {
//         return AlertDialog(
//           title: Text('Pick a color for the mapping'),
//           content: SingleChildScrollView(
//             child: ColorPicker(
//               pickerColor: pickerColor,
//               onColorChanged: (Color color) {
//                 pickerColor = color;
//               },
//               pickerAreaHeightPercent: 0.8,
//               enableAlpha: false,
//               displayThumbColor: true,
//               showLabel: true,
//               paletteType: PaletteType.hsv,
//             ),
//           ),
//           actions: <Widget>[
//             TextButton(
//               child: Text('Cancel'),
//               onPressed: () {
//                 Navigator.of(context).pop();
//               },
//             ),
//             ElevatedButton(
//               child: Text('Select'),
//               onPressed: () {
//                 setState(() {
//                   // Convert the color to hex format
//                   _selectedColor =
//                       '#${pickerColor.value.toRadixString(16).substring(2).toUpperCase()}';
//                 });
//                 Navigator.of(context).pop();
//               },
//             ),
//           ],
//         );
//       },
//     );
//   }

//   void _handleNlpLineSelection(String line, MappingProvider mappingProvider) {
//     setState(() {
//       // Clear previous selections
//       _clearSelections();

//       // Add the selected NLP line
//       _selectedNlpLines.add(line);

//       // Get mappings for this NLP line
//       final mappings = mappingProvider.getMappingsForNlpLine(
//         widget.solutionId,
//         line,
//       );
//       _selectedMappings = mappings;

//       // If there are mappings, auto-populate Java and SQL selections
//       if (mappings.isNotEmpty) {
//         // Get the first mapping
//         final mapping = mappings.first;

//         // Add Java lines with their line indices
//         for (var javaLine in mapping.javaLines) {
//           _selectedJavaLines.add(javaLine);
//           if (!_selectedLineIndices.containsKey(javaLine.content)) {
//             _selectedLineIndices[javaLine.content] = [];
//           }
//           if (!_selectedLineIndices[javaLine.content]!.contains(
//             javaLine.lineIndex,
//           )) {
//             _selectedLineIndices[javaLine.content]!.add(javaLine.lineIndex);
//           }
//         }

//         // Add SQL lines with their line indices
//         for (var sqlLine in mapping.sqlLines) {
//           _selectedSqlLines.add(sqlLine);
//           if (!_selectedLineIndices.containsKey(sqlLine.content)) {
//             _selectedLineIndices[sqlLine.content] = [];
//           }
//           if (!_selectedLineIndices[sqlLine.content]!.contains(
//             sqlLine.lineIndex,
//           )) {
//             _selectedLineIndices[sqlLine.content]!.add(sqlLine.lineIndex);
//           }
//         }

//         // Set the color from the mapping
//         _selectedColor = mapping.color;

//         // Show mapping info in edit mode
//         if (_isEditMode) {
//           ScaffoldMessenger.of(context).showSnackBar(
//             SnackBar(
//               content: Row(
//                 children: [
//                   Expanded(
//                     child: Column(
//                       crossAxisAlignment: CrossAxisAlignment.start,
//                       mainAxisSize: MainAxisSize.min,
//                       children: [
//                         Text(
//                           'Found existing mapping:',
//                           style: TextStyle(fontWeight: FontWeight.bold),
//                         ),
//                         SizedBox(height: 4),
//                         Text('Java: ${_selectedJavaLines.length} line(s)'),
//                         Text('SQL: ${_selectedSqlLines.length} line(s)'),
//                       ],
//                     ),
//                   ),
//                   Icon(Icons.info_outline, color: Colors.white),
//                 ],
//               ),
//               duration: Duration(seconds: 3),
//               backgroundColor: Colors.blue.shade700,
//             ),
//           );
//         }
//       } else if (_isEditMode) {
//         // If no mappings found and in edit mode, show a message
//         ScaffoldMessenger.of(context).showSnackBar(
//           SnackBar(
//             content: Text('No existing mappings found for this NLP line'),
//             duration: Duration(seconds: 2),
//           ),
//         );
//       }
//     });
//   }

//   void _handleJavaLineSelection(String line, MappingProvider mappingProvider) {
//     if (!_isEditMode) return;

//     setState(() {
//       // Get the line index
//       int lineIndex = -1;
//       if (_selectedLineIndices.containsKey(line) &&
//           _selectedLineIndices[line]!.isNotEmpty) {
//         lineIndex = _selectedLineIndices[line]!.first;
//       } else {
//         final lines = _solution!.java.code.split('\n');
//         lineIndex = lines.indexOf(line);
//       }

//       final lineSelection = LineSelection(line, lineIndex);

//       if (_isRangeSelectionMode && _rangeStartLine != null) {
//         // Handle range selection
//         final start = _rangeStartLine!;
//         final end = lineIndex;
//         final startLine = min(start, end);
//         final endLine = max(start, end);

//         // Clear existing selections
//         _selectedJavaLines.clear();

//         // Add all lines in the range
//         final lines = _solution!.java.code.split('\n');
//         for (int i = startLine; i <= endLine; i++) {
//           if (i >= 0 && i < lines.length) {
//             final rangeLine = lines[i];
//             _selectedJavaLines.add(LineSelection(rangeLine, i));
//             if (!_selectedLineIndices.containsKey(rangeLine)) {
//               _selectedLineIndices[rangeLine] = [];
//             }
//             if (!_selectedLineIndices[rangeLine]!.contains(i)) {
//               _selectedLineIndices[rangeLine]!.add(i);
//             }
//           }
//         }

//         // Reset range selection mode
//         _isRangeSelectionMode = false;
//         _rangeStartLine = null;

//         // Show range selection complete message
//         ScaffoldMessenger.of(context).showSnackBar(
//           SnackBar(
//             content: Text('Selected ${_selectedJavaLines.length} Java lines'),
//             duration: Duration(seconds: 2),
//           ),
//         );
//       } else {
//         // Start range selection
//         _isRangeSelectionMode = true;
//         _rangeStartLine = lineIndex;
//         _selectedJavaLines.add(lineSelection);

//         // Show range selection start message
//         ScaffoldMessenger.of(context).showSnackBar(
//           SnackBar(
//             content: Text('Click another line to complete range selection'),
//             duration: Duration(seconds: 2),
//           ),
//         );
//       }

//       _updateSelectedMappings(mappingProvider);
//     });
//   }

//   void _handleSqlLineSelection(String line, MappingProvider mappingProvider) {
//     if (!_isEditMode) return;

//     setState(() {
//       // Get the line index
//       int lineIndex = -1;
//       if (_selectedLineIndices.containsKey(line) &&
//           _selectedLineIndices[line]!.isNotEmpty) {
//         lineIndex = _selectedLineIndices[line]!.first;
//       } else {
//         final lines = _solution!.sql.code.split('\n');
//         lineIndex = lines.indexOf(line);
//       }

//       final lineSelection = LineSelection(line, lineIndex);

//       if (_isRangeSelectionMode && _rangeStartLine != null) {
//         // Handle range selection
//         final start = _rangeStartLine!;
//         final end = lineIndex;
//         final startLine = min(start, end);
//         final endLine = max(start, end);

//         // Clear existing selections
//         _selectedSqlLines.clear();

//         // Add all lines in the range
//         final lines = _solution!.sql.code.split('\n');
//         for (int i = startLine; i <= endLine; i++) {
//           if (i >= 0 && i < lines.length) {
//             final rangeLine = lines[i];
//             _selectedSqlLines.add(LineSelection(rangeLine, i));
//             if (!_selectedLineIndices.containsKey(rangeLine)) {
//               _selectedLineIndices[rangeLine] = [];
//             }
//             if (!_selectedLineIndices[rangeLine]!.contains(i)) {
//               _selectedLineIndices[rangeLine]!.add(i);
//             }
//           }
//         }

//         // Reset range selection mode
//         _isRangeSelectionMode = false;
//         _rangeStartLine = null;

//         // Show range selection complete message
//         ScaffoldMessenger.of(context).showSnackBar(
//           SnackBar(
//             content: Text('Selected ${_selectedSqlLines.length} SQL lines'),
//             duration: Duration(seconds: 2),
//           ),
//         );
//       } else {
//         // Start range selection
//         _isRangeSelectionMode = true;
//         _rangeStartLine = lineIndex;
//         _selectedSqlLines.add(lineSelection);

//         // Show range selection start message
//         ScaffoldMessenger.of(context).showSnackBar(
//           SnackBar(
//             content: Text('Click another line to complete range selection'),
//             duration: Duration(seconds: 2),
//           ),
//         );
//       }

//       _updateSelectedMappings(mappingProvider);
//     });
//   }

//   void _updateSelectedMappings(MappingProvider mappingProvider) {
//     List<Mapping> mappings = [];

//     if (_selectedNlpLines.isNotEmpty) {
//       // Extract the word from the NLP line if it contains position information
//       String nlpLine = _selectedNlpLines.first;
//       if (nlpLine.contains(":")) {
//         // Extract just the word part, not the position
//         nlpLine = nlpLine.split(":")[0];
//       }

//       // For simplicity, we'll just use the first selected NLP line for mapping
//       mappings = mappingProvider.getMappingsForNlpLine(
//         widget.solutionId,
//         nlpLine,
//       );
//     }

//     setState(() {
//       _selectedMappings = mappings;
//     });
//   }

//   void _clearSelections() {
//     setState(() {
//       _selectedNlpLines = [];
//       _selectedJavaLines = [];
//       _selectedSqlLines = [];
//       _selectedMappings = [];
//       _selectedLineIndices = {};
//       _isRangeSelectionMode = false;
//       _rangeStartLine = null;
//     });
//   }

//   bool _canCreateMapping() {
//     return _selectedNlpLines.isNotEmpty &&
//         (_selectedJavaLines.isNotEmpty || _selectedSqlLines.isNotEmpty);
//   }

//   void _navigateToAddMapping(BuildContext context, Solution solution) {
//     // Make sure we're passing all selected lines with their indices
//     print("Selected Java Lines: ${_selectedJavaLines.length}");

//     // Extract the word from the NLP line if it contains position information
//     String nlpLine = '';
//     if (_selectedNlpLines.isNotEmpty) {
//       String selectedNlp = _selectedNlpLines.first;
//       if (selectedNlp.contains(":")) {
//         // Extract just the word part, not the position
//         nlpLine = selectedNlp.split(":")[0];
//       } else {
//         nlpLine = selectedNlp;
//       }
//     }

//     // If we're editing an existing mapping, pass its ID
//     String? mappingId;
//     if (_selectedMappings.isNotEmpty) {
//       mappingId = _selectedMappings.first.id;
//     }

//     Navigator.push(
//       context,
//       MaterialPageRoute(
//         builder:
//             (context) => EditMappingScreen(
//               solutionId: solution.id,
//               initialNlpLine: nlpLine,
//               initialJavaLines: _selectedJavaLines,
//               initialSqlLines: _selectedSqlLines,
//               initialColor: _selectedColor,
//               mappingId: mappingId, // Pass the mapping ID if editing
//             ),
//       ),
//     ).then((_) {
//       // Clear selections after adding/updating a mapping
//       _clearSelections();
//     });
//   }

//   void _openPreview(
//     BuildContext context,
//     Solution solution,
//     List<Mapping> mappings,
//   ) async {
//     try {
//       // Create a combined data structure
//       final Map<String, dynamic> exportData = {
//         'solution': solution.toJson(),
//         'mappings':
//             mappings.map((mapping) {
//               // Create a copy of the mapping JSON
//               final mappingJson = mapping.toJson();

//               // Add line indices to the mapping
//               if (mappingJson['javaLines'] != null) {
//                 final javaLines = List<dynamic>.from(mappingJson['javaLines']);
//                 final updatedJavaLines =
//                     javaLines.map((line) {
//                       if (line is String) {
//                         // Find the line index in the actual code
//                         final lines = solution.java.code.split('\n');
//                         final lineIndex = lines.indexOf(line);
//                         return {
//                           'content': line,
//                           'line_no': lineIndex >= 0 ? lineIndex : 0,
//                         };
//                       }
//                       return line;
//                     }).toList();
//                 mappingJson['javaLines'] = updatedJavaLines;
//               }

//               if (mappingJson['sqlLines'] != null) {
//                 final sqlLines = List<dynamic>.from(mappingJson['sqlLines']);
//                 final updatedSqlLines =
//                     sqlLines.map((line) {
//                       if (line is String) {
//                         // Find the line index in the actual code
//                         final lines = solution.sql.code.split('\n');
//                         final lineIndex = lines.indexOf(line);
//                         return {
//                           'content': line,
//                           'line_no': lineIndex >= 0 ? lineIndex : 0,
//                         };
//                       }
//                       return line;
//                     }).toList();
//                 mappingJson['sqlLines'] = updatedSqlLines;
//               }

//               return mappingJson;
//             }).toList(),
//       };

//       // Add Java keywords and their explanations
//       final Map<String, String> javaKeywords = {};

//       // Extract Java keywords from mappings
//       for (final mapping in mappings) {
//         if (mapping.tag.isNotEmpty && mapping.javaLines.isNotEmpty) {
//           for (final javaLine in mapping.javaLines) {
//             // Extract Java keywords from the line
//             final keywords = _extractJavaKeywords(javaLine);
//             for (final keyword in keywords) {
//               javaKeywords[keyword] = mapping.tag;
//             }
//           }
//         }
//       }

//       // Add Java keywords to the export data
//       exportData['javaKeywords'] = javaKeywords;

//       // Convert to JSON
//       final jsonString = jsonEncode(exportData);

//       // Navigate to the preview screen
//       Navigator.push(
//         context,
//         MaterialPageRoute(
//           builder: (context) => PreviewScreen(jsonData: jsonString),
//         ),
//       );
//     } catch (e) {
//       ScaffoldMessenger.of(
//         context,
//       ).showSnackBar(SnackBar(content: Text('Error generating preview: $e')));
//     }
//   }

//   // Extract Java keywords from a line of Java code
//   List<String> _extractJavaKeywords(dynamic javaLine) {
//     // Convert LineSelection to String if needed
//     String lineContent =
//         javaLine is LineSelection ? javaLine.content : javaLine;
//     final List<String> foundKeywords = [];
//     final List<String> javaKeywordsList = [
//       'abstract',
//       'assert',
//       'boolean',
//       'break',
//       'byte',
//       'case',
//       'catch',
//       'char',
//       'class',
//       'const',
//       'continue',
//       'default',
//       'do',
//       'double',
//       'else',
//       'enum',
//       'extends',
//       'final',
//       'finally',
//       'float',
//       'for',
//       'goto',
//       'if',
//       'implements',
//       'import',
//       'instanceof',
//       'int',
//       'interface',
//       'long',
//       'native',
//       'new',
//       'package',
//       'private',
//       'protected',
//       'public',
//       'return',
//       'short',
//       'static',
//       'strictfp',
//       'super',
//       'switch',
//       'synchronized',
//       'this',
//       'throw',
//       'throws',
//       'transient',
//       'try',
//       'void',
//       'volatile',
//       'while',
//       // Common Java types and annotations
//       'String',
//       'List',
//       'Map',
//       'Set',
//       'ArrayList',
//       'HashMap',
//       'HashSet',
//       'LinkedList',
//       'Override', 'Deprecated', 'SuppressWarnings', 'FunctionalInterface',
//       // Common Java methods
//       'equals', 'hashCode', 'toString', 'compareTo', 'clone', 'finalize',
//       // Common Java annotations
//       '@Override', '@Deprecated', '@SuppressWarnings', '@FunctionalInterface',
//     ];

//     // Simple approach: check if each keyword is in the line
//     for (final keyword in javaKeywordsList) {
//       // Use regex to match whole words only
//       final RegExp regex = RegExp('\\b$keyword\\b');
//       if (regex.hasMatch(lineContent)) {
//         foundKeywords.add(keyword);
//       }
//     }

//     return foundKeywords;
//   }

//   // Navigate to the edit solution screen
//   void _navigateToEditSolution(BuildContext context, String solutionId) {
//     Navigator.push(
//       context,
//       MaterialPageRoute(
//         builder: (context) => EditSolutionScreen(solutionId: solutionId),
//       ),
//     );
//   }

//   Future<void> _exportSolution(
//     BuildContext context,
//     Solution solution,
//     List<Mapping> mappings,
//   ) async {
//     // Export the solution and mappings
//     try {
//       final filePath = await ExportService.exportSolutionWithMappings(
//         solution,
//         mappings,
//       );

//       ScaffoldMessenger.of(context).showSnackBar(
//         SnackBar(
//           content: Text(
//             'Solution exported successfully${filePath.isNotEmpty ? " to $filePath" : ""}',
//           ),
//         ),
//       );
//     } catch (e) {
//       ScaffoldMessenger.of(
//         context,
//       ).showSnackBar(SnackBar(content: Text('Error exporting solution: $e')));
//     }
//   }

//   // Add keyboard shortcut handling
//   @override
//   void initState() {
//     super.initState();
//     // Add keyboard listener for shortcuts
//     RawKeyboard.instance.addListener(_handleKeyEvent);
//   }

//   @override
//   void dispose() {
//     RawKeyboard.instance.removeListener(_handleKeyEvent);
//     super.dispose();
//   }

//   void _handleKeyEvent(RawKeyEvent event) {
//     if (event is RawKeyDownEvent) {
//       if (event.logicalKey == LogicalKeyboardKey.controlLeft ||
//           event.logicalKey == LogicalKeyboardKey.controlRight) {
//         setState(() {
//           _isMultiSelectMode = true;
//         });
//       }
//     } else if (event is RawKeyUpEvent) {
//       if (event.logicalKey == LogicalKeyboardKey.controlLeft ||
//           event.logicalKey == LogicalKeyboardKey.controlRight) {
//         setState(() {
//           _isMultiSelectMode = false;
//           _selectedLineNumbers.clear();
//         });
//       }
//     }
//   }

//   // Add method to show clear all mappings confirmation dialog
//   void _showClearAllMappingsDialog(
//     BuildContext context,
//     MappingProvider mappingProvider,
//   ) {
//     showDialog(
//       context: context,
//       builder:
//           (context) => AlertDialog(
//             title: Text('Clear All Mappings'),
//             content: Column(
//               mainAxisSize: MainAxisSize.min,
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Text(
//                   'Are you sure you want to clear all mappings for this solution?',
//                 ),
//                 SizedBox(height: 8),
//                 Text(
//                   'This action cannot be undone.',
//                   style: TextStyle(
//                     color: Colors.red,
//                     fontWeight: FontWeight.bold,
//                   ),
//                 ),
//               ],
//             ),
//             actions: [
//               TextButton(
//                 onPressed: () => Navigator.pop(context),
//                 child: Text('Cancel'),
//               ),
//               ElevatedButton(
//                 onPressed: () {
//                   Navigator.pop(context);
//                   _clearAllMappings(mappingProvider);
//                 },
//                 style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
//                 child: Text('Clear All'),
//               ),
//             ],
//           ),
//     );
//   }

//   // Add method to clear all mappings
//   void _clearAllMappings(MappingProvider mappingProvider) {
//     try {
//       // Get all mappings for this solution
//       final mappings = mappingProvider.getMappingsForSolution(
//         widget.solutionId,
//       );

//       // Delete each mapping
//       for (final mapping in mappings) {
//         mappingProvider.deleteMapping(mapping.id);
//       }

//       // Show success message
//       ScaffoldMessenger.of(context).showSnackBar(
//         SnackBar(
//           content: Text('All mappings have been cleared'),
//           backgroundColor: Colors.green,
//         ),
//       );

//       // Clear selections
//       _clearSelections();
//     } catch (e) {
//       // Show error message
//       ScaffoldMessenger.of(context).showSnackBar(
//         SnackBar(
//           content: Text('Error clearing mappings: $e'),
//           backgroundColor: Colors.red,
//         ),
//       );
//     }
//   }
// }
