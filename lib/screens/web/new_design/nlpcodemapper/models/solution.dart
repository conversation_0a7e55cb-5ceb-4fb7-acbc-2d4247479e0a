import 'nsl_section.dart';

class CodeWithIndices {
  final String code;
  final List<int> lineIndices;

  CodeWithIndices({required this.code, required this.lineIndices});

  factory CodeWithIndices.fromJson(Map<String, dynamic> json) {
    if (json.containsKey('lines')) {
      // New format with line_no and content
      final List<dynamic> lines = json['lines'] as List<dynamic>;
      final String code = lines
          .map((line) => line['content'] as String)
          .join('\n');
      final List<int> indices =
          lines.map((line) => line['line_no'] as int).toList();
      return CodeWithIndices(code: code, lineIndices: indices);
    } else {
      // Old format with code and line_indices
      return CodeWithIndices(
        code: json['code'] ?? '',
        lineIndices: List<int>.from(json['line_indices'] ?? []),
      );
    }
  }

  Map<String, dynamic> toJson() {
    // Convert to new format with line_no and content
    final List<String> lines = code.split('\n');
    final List<Map<String, dynamic>> lineObjects = [];

    for (int i = 0; i < lines.length; i++) {
      lineObjects.add({'line_no': lineIndices[i], 'content': lines[i]});
    }

    return {'lines': lineObjects};
  }
}

class Solution {
  final String id;
  final String title;
  final String nlp; // Complete NLP text
  final CodeWithIndices java; // Java code with line indices
  final CodeWithIndices sql; // SQL code with line indices
  final String nslFileName; // NSL file name
  final String javaFileName; // Java file name
  final int prescriptives; // Number of prescriptives
  final int subPrescriptives; // Number of sub-prescriptives
  final int bets; // Number of BETs
  final int pathways; // Number of pathways
  final List<NslSection> sections; // Sections of the NSL code

  Solution({
    required this.id,
    required this.title,
    required this.nlp,
    required this.java,
    required this.sql,
    this.nslFileName = '',
    this.javaFileName = '',
    this.prescriptives = 0,
    this.subPrescriptives = 0,
    this.bets = 0,
    this.pathways = 0,
    this.sections = const [],
  });

  // JSON serialization methods
  factory Solution.fromJson(Map<String, dynamic> json) {
    List<NslSection> sections = [];
    if (json.containsKey('sections') && json['sections'] is List) {
      sections =
          (json['sections'] as List)
              .map((section) => NslSection.fromJson(section))
              .toList();
    }

    // Handle Java code and line indices
    CodeWithIndices javaCode;
    if (json.containsKey('java')) {
      if (json['java'] is Map<String, dynamic>) {
        // If java is already a CodeWithIndices object
        javaCode = CodeWithIndices.fromJson(json['java']);
      } else if (json['java'] is String) {
        // If java is just a string, create line indices
        final javaLines = (json['java'] as String).split('\n');
        final javaLineIndices = List.generate(
          javaLines.length,
          (index) => index,
        );
        javaCode = CodeWithIndices(
          code: json['java'],
          lineIndices: javaLineIndices,
        );
      } else {
        // Fallback to empty code
        javaCode = CodeWithIndices(code: '', lineIndices: []);
      }
    } else {
      javaCode = CodeWithIndices(code: '', lineIndices: []);
    }

    // Handle SQL code and line indices
    CodeWithIndices sqlCode;
    if (json.containsKey('sql')) {
      if (json['sql'] is Map<String, dynamic>) {
        // If sql is already a CodeWithIndices object
        sqlCode = CodeWithIndices.fromJson(json['sql']);
      } else if (json['sql'] is String) {
        // If sql is just a string, create line indices
        final sqlLines = (json['sql'] as String).split('\n');
        final sqlLineIndices = List.generate(sqlLines.length, (index) => index);
        sqlCode = CodeWithIndices(
          code: json['sql'],
          lineIndices: sqlLineIndices,
        );
      } else {
        // Fallback to empty code
        sqlCode = CodeWithIndices(code: '', lineIndices: []);
      }
    } else {
      sqlCode = CodeWithIndices(code: '', lineIndices: []);
    }

    return Solution(
      id: json['id'],
      title: json['title'],
      nlp: json['nlp'],
      java: javaCode,
      sql: sqlCode,
      nslFileName: json['nsl_file_name'] ?? '',
      javaFileName: json['java_file_name'] ?? '',
      prescriptives: json['prescriptives'] ?? 0,
      subPrescriptives: json['sub_prescriptives'] ?? 0,
      bets: json['bets'] ?? 0,
      pathways: json['pathways'] ?? 0,
      sections: sections,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'nlp': nlp,
      'java': java.toJson(),
      'sql': sql.toJson(),
      'nsl_file_name': nslFileName,
      'java_file_name': javaFileName,
      'prescriptives': prescriptives,
      'sub_prescriptives': subPrescriptives,
      'bets': bets,
      'pathways': pathways,
      'sections': sections.map((section) => section.toJson()).toList(),
    };
  }
}
