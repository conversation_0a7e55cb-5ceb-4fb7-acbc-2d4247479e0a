import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/services.dart';
import 'package:nsl/screens/web/new_design/nlpcodemapper/models/line_selection.dart';
import 'package:nsl/screens/web/new_design/nlpcodemapper/models/mapping.dart';
import 'package:nsl/screens/web/new_design/nlpcodemapper/models/nsl_section.dart';
import 'package:nsl/screens/web/new_design/nlpcodemapper/models/solution.dart';
import 'package:nsl/screens/web/new_design/nlpcodemapper/services/java_keyword_service.dart';
import 'package:nsl/screens/web/new_design/nlpcodemapper/widgets/code_viewer.dart';
import 'dart:convert';

class PreviewScreen extends StatefulWidget {
  const PreviewScreen({
    super.key,
  });

  @override
  _PreviewScreenState createState() => _PreviewScreenState();
}

class _PreviewScreenState extends State<PreviewScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  Map<String, dynamic>? _parsedData;
  Solution? _solution;
  List<Map<String, dynamic>> _mappings = [];
  Map<String, String> _javaKeywords = {};
  final Map<String, String> _allJavaKeywords = {};

  // Selection state
  List<String> _selectedNlpLines = [];
  List<LineSelection> _selectedJavaLines = [];
  List<LineSelection> _selectedSqlLines = [];
  List<Mapping> _selectedMappings = [];

  // Store the line indices along with the content
  final Map<String, List<int>> _selectedLineIndices = {};

  // Selection mode: 'line' or 'word'
  String _selectionMode = 'line';
  String _selectedColor = "#3498DB"; // Default blue color

  // Helper method to check if we're in parse selection mode
  bool get _isWordSelectionMode => _selectionMode == 'word';

  // Helper method to get the icon for the current selection mode
  IconData _getSelectionModeIcon() {
    switch (_selectionMode) {
      case 'word':
        return Icons.text_fields;
      case 'paragraph':
        return Icons.article;
      case 'line':
      default:
        return Icons.subject;
    }
  }

  // Keywords from selected lines
  Map<String, String> _selectedLinesKeywords = {};
  List<String> _selectedKeywords = [];

  // Services
  final JavaKeywordService _keywordService = JavaKeywordService();

  // Selected view type (Java or DB)
  String _selectedView = 'Java';

  // Resizable panel state
  double? _leftPanelWidth;
  double? _codeViewerWidth;

  // Scroll controllers for code viewers
  late ScrollController _javaScrollController;
  late ScrollController _dbScrollController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _javaScrollController = ScrollController();
    _dbScrollController = ScrollController();
    _parseJsonData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _javaScrollController.dispose();
    _dbScrollController.dispose();
    super.dispose();
  }

  void _parseJsonData() async {
    try {
      // Clear all selections
      _clearSelections();

      final jsonString =
          await rootBundle.loadString('assets/data/load_solution.json');
      _parsedData = jsonDecode(jsonString) as Map<String, dynamic>;

      // Parse solution
      if (_parsedData!.containsKey('solution')) {
        _solution = Solution.fromJson(_parsedData!['solution']);
      }

      // Parse mappings
      if (_parsedData!.containsKey('mappings')) {
        _mappings = List<Map<String, dynamic>>.from(_parsedData!['mappings']);
      }

      // Parse Java keywords
      if (_parsedData!.containsKey('javaKeywords')) {
        final keywordsMap =
            _parsedData!['javaKeywords'] as Map<String, dynamic>;
        _javaKeywords = keywordsMap.map(
          (key, value) => MapEntry(key, value.toString()),
        );
      }

      // Reset view to Java
      _selectedView = 'Java';

      // Reset selection mode
      _selectionMode = 'line';

      // Reset tab to Used Constructs
      if (_tabController.index != 0) {
        _tabController.animateTo(0);
      }

      setState(() {});
    } catch (e) {
      print('Error parsing JSON data: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_solution == null) {
      return Scaffold(
        appBar: AppBar(title: Text('Preview')),
        body: Center(child: Text('Error loading preview data')),
      );
    }

    return Scaffold(
      // appBar: AppBar(
      //   title: Text('NSL $_selectedView Code Comparison'),
      //   actions: [
      //     IconButton(
      //       icon: Icon(Icons.refresh),
      //       onPressed: _parseJsonData,
      //       tooltip: 'Refresh',
      //     ),
      //   ],
      // ),
      body: _buildResizableLayout(),
    );
  }

  // Build resizable layout
  Widget _buildResizableLayout() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate initial sizes
        double totalWidth = constraints.maxWidth;
        double dividerWidth = 10.0;

        // Use state variables to track the current sizes
        double leftPanelWidth = _leftPanelWidth ??
            (totalWidth * 0.4); // 40% of total width by default
        double rightPanelWidth = totalWidth - leftPanelWidth - dividerWidth;

        // For the right panel, calculate code viewer and constructs panel widths
        double codeViewerWidth = _codeViewerWidth ??
            (rightPanelWidth * 0.7); // 70% of right panel by default
        double constructsPanelWidth = rightPanelWidth -
            codeViewerWidth -
            (_selectedView == 'Java' ? dividerWidth : 0);

        return Row(
          children: [
            // Left panel - NSL code
            SizedBox(
              width: leftPanelWidth,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // NSL file name and controls
                  Container(
                    color: Colors.grey.shade200,
                    padding: EdgeInsets.all(8),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // File name with selection mode toggle icons
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.description),
                                SizedBox(width: 8),
                                Text(
                                  _solution!.nslFileName.isNotEmpty
                                      ? _solution!.nslFileName
                                      : 'NSL File',
                                  style: TextStyle(fontWeight: FontWeight.bold),
                                ),
                              ],
                            ),
                            // Selection mode toggle icons
                            Row(
                              children: [
                                // Line selection mode
                                IconButton(
                                  icon: Icon(
                                    Icons.subject,
                                    color: _selectionMode == 'line'
                                        ? Colors.blue
                                        : Colors.grey,
                                  ),
                                  tooltip: 'Line Selection Mode',
                                  onPressed: () {
                                    setState(() {
                                      _selectionMode = 'line';
                                      _clearSelections();
                                    });
                                  },
                                ),
                                // Word selection mode
                                IconButton(
                                  icon: Icon(
                                    Icons.text_fields,
                                    color: _selectionMode == 'word'
                                        ? Colors.blue
                                        : Colors.grey,
                                  ),
                                  tooltip: 'Parse Selection Mode',
                                  onPressed: () {
                                    setState(() {
                                      _selectionMode = 'word';
                                      _clearSelections();

                                      // Show a tooltip explaining phrase selection
                                      ScaffoldMessenger.of(
                                        context,
                                      ).showSnackBar(
                                        SnackBar(
                                          content: Text(
                                            'Phrase selection mode: Click on phrases to select them',
                                          ),
                                          duration: Duration(seconds: 3),
                                        ),
                                      );
                                    });
                                  },
                                ),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  // Metadata
                  Padding(
                    padding: EdgeInsets.all(8),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                'Prescriptives: ${_solution!.prescriptives}',
                              ),
                            ),
                            Expanded(
                              child: Text(
                                'Sub-Prescriptive: ${_solution!.subPrescriptives}',
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 8),
                        Row(
                          children: [
                            Expanded(child: Text('BETs: ${_solution!.bets}')),
                            Expanded(
                              child: Text('Pathways: ${_solution!.pathways}'),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  // NSL content with expandable sections
                  Expanded(
                    child: _solution!.sections.isNotEmpty
                        ? _buildExpandableSections()
                        : CodeViewer(
                            code: _solution!.nlp,
                            language: 'plaintext',
                            highlightedMappings: _selectedMappings,
                            onLineSelected: _handleNlpLineSelection,
                            selectedColor: _selectedColor,
                            selectedLines: _selectedNlpLines,
                            allowWordSelection: _isWordSelectionMode,
                            isInWordSelectionMode: _isWordSelectionMode,
                          ),
                  ),
                ],
              ),
            ),

            // Vertical divider with drag handle
            MouseRegion(
              cursor: SystemMouseCursors.resizeLeftRight,
              child: GestureDetector(
                onHorizontalDragUpdate: (details) {
                  setState(() {
                    // Update left panel width based on drag
                    _leftPanelWidth = (leftPanelWidth + details.delta.dx).clamp(
                      totalWidth * 0.2, // Min 20% of total width
                      totalWidth * 0.6, // Max 60% of total width
                    );
                  });
                },
                child: Container(
                  width: dividerWidth,
                  color: Colors.grey.shade300,
                  child: Center(
                    child: Container(
                      width: 4,
                      height: 30,
                      decoration: BoxDecoration(
                        color: Colors.grey.shade400,
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                  ),
                ),
              ),
            ),

            // Right panel - Java/DB code and constructs
            SizedBox(
              width: rightPanelWidth,
              child: Column(
                children: [
                  // Java/DB file name
                  Container(
                    color: Colors.blue.shade100,
                    padding: EdgeInsets.all(8),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.code),
                            SizedBox(width: 8),
                            Text(
                              _selectedView == 'Java'
                                  ? (_solution!.javaFileName.isNotEmpty
                                      ? _solution!.javaFileName
                                      : 'Java File')
                                  : 'Database Schema',
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                          ],
                        ),
                        // View type buttons
                        Row(
                          children: [
                            // Java button
                            ElevatedButton(
                              onPressed: () {
                                setState(() {
                                  _selectedView = 'Java';
                                });
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: _selectedView == 'Java'
                                    ? Colors.blue
                                    : Colors.grey.shade300,
                                foregroundColor: _selectedView == 'Java'
                                    ? Colors.white
                                    : Colors.black,
                              ),
                              child: Text('Java'),
                            ),
                            SizedBox(width: 8),
                            // DB button
                            ElevatedButton(
                              onPressed: () {
                                setState(() {
                                  _selectedView = 'DB';
                                });
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: _selectedView == 'DB'
                                    ? Colors.blue
                                    : Colors.grey.shade300,
                                foregroundColor: _selectedView == 'DB'
                                    ? Colors.white
                                    : Colors.black,
                              ),
                              child: Text('DB'),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  // Main content area
                  Expanded(
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Code viewer - full width for DB, partial width for Java
                        SizedBox(
                          width: _selectedView == 'DB'
                              ? rightPanelWidth
                              : codeViewerWidth,
                          child: CodeViewer(
                            code: _selectedView == 'Java'
                                ? _solution!.java.code
                                : _solution!.sql.code,
                            language: _selectedView == 'Java' ? 'java' : 'sql',
                            highlightedMappings: _selectedMappings,
                            // Only allow line selection if no NLP lines are selected
                            onLineSelected: _selectedNlpLines.isEmpty
                                ? (_selectedView == 'Java'
                                    ? _handleJavaLineSelection
                                    : _handleSqlLineSelection)
                                : (
                                    String _,
                                    int __,
                                  ) {}, // Empty function when NLP lines are selected
                            selectedColor: _selectedColor,
                            selectedLines: [], // Don't use selectedLines for highlighting
                            selectedLineIndices:
                                _selectedLineIndices, // Only use selectedLineIndices for highlighting
                            allowWordSelection: _isWordSelectionMode,
                            isInWordSelectionMode: _isWordSelectionMode,
                            scrollController: _selectedView == 'Java'
                                ? _javaScrollController
                                : _dbScrollController,
                          ),
                        ),

                        // Constructs panel (only visible for Java)
                        if (_selectedView == 'Java') ...[
                          // Vertical divider with drag handle
                          MouseRegion(
                            cursor: SystemMouseCursors.resizeLeftRight,
                            child: GestureDetector(
                              onHorizontalDragUpdate: (details) {
                                setState(() {
                                  // Update code viewer width based on drag
                                  _codeViewerWidth =
                                      (codeViewerWidth + details.delta.dx)
                                          .clamp(
                                    rightPanelWidth *
                                        0.4, // Min 40% of right panel
                                    rightPanelWidth *
                                        0.8, // Max 80% of right panel
                                  );
                                });
                              },
                              child: Container(
                                width: dividerWidth,
                                color: Colors.grey.shade300,
                                child: Center(
                                  child: Container(
                                    width: 4,
                                    height: 30,
                                    decoration: BoxDecoration(
                                      color: Colors.grey.shade400,
                                      borderRadius: BorderRadius.circular(2),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),

                          // Constructs panel
                          SizedBox(
                            width: constructsPanelWidth,
                            child: Container(
                              decoration: BoxDecoration(
                                border: Border(
                                  left: BorderSide(color: Colors.grey.shade300),
                                ),
                              ),
                              child: Column(
                                children: [
                                  // Tabs for Used/All Constructs
                                  TabBar(
                                    controller: _tabController,
                                    tabs: [
                                      Tab(text: 'Used Constructs'),
                                      Tab(text: 'All Constructs'),
                                    ],
                                  ),

                                  // Tab content
                                  Expanded(
                                    child: TabBarView(
                                      controller: _tabController,
                                      children: [
                                        // Used Constructs - Only show keywords from selected lines, otherwise show empty
                                        _selectedKeywords.isNotEmpty
                                            ? _buildConstructsList(
                                                _selectedKeywords,
                                              )
                                            : Center(
                                                child: Text(
                                                  'Select Java code to see used constructs',
                                                  style: TextStyle(
                                                    color: Colors.grey.shade600,
                                                    fontStyle: FontStyle.italic,
                                                  ),
                                                ),
                                              ),

                                        // All Constructs
                                        _buildConstructsList(
                                          _keywordService.allKeywords.keys
                                              .toList(),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  // Clear all selections
  void _clearSelections() {
    setState(() {
      _selectedNlpLines = [];
      _selectedJavaLines = [];
      _selectedSqlLines = [];
      _selectedMappings = [];
      _selectedLinesKeywords.clear();
      _selectedKeywords.clear();
    });
  }

  // Handle NLP line selection
  void _handleNlpLineSelection(String line, int lineIndex) {
    // Store the line index
    if (!_selectedLineIndices.containsKey(line)) {
      _selectedLineIndices[line] = [];
    }
    if (!_selectedLineIndices[line]!.contains(lineIndex)) {
      _selectedLineIndices[line]!.add(lineIndex);
    }
    setState(() {
      // If the line is already selected, deselect it
      if (_selectedNlpLines.contains(line)) {
        _selectedNlpLines.remove(line);

        // If no lines are selected, clear all selections
        if (_selectedNlpLines.isEmpty) {
          _clearSelections();
          return;
        }
      } else {
        // Add the selected line or word to the selected NLP lines
        _selectedNlpLines.add(line);
      }

      // Process the selected line or word
      // If in word selection mode, the line parameter might contain position information
      // in the format "word:position"
      String lineToUse = line;
      if (_isWordSelectionMode && line.contains(":")) {
        // Extract just the word part, not the position
        lineToUse = line.split(":")[0];
      }

      // Find mappings for the selected NLP lines
      _findMappingsForSelection();

      // Check if any mappings were found
      if (_selectedMappings.isEmpty) {
        // No mappings found, show a simple snackbar
        WidgetsBinding.instance.addPostFrameCallback((_) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('No mapping available for this selection'),
              duration: Duration(seconds: 2),
            ),
          );
        });
      } else {
        // Find corresponding Java and SQL lines from the mappings
        _findCorrespondingLines();

        // Force a switch to the "Used Constructs" tab
        if (_tabController.index != 0) {
          _tabController.animateTo(0); // Switch to "Used Constructs" tab
        }
      }
    });
  }

  // Show dialog to create a new mapping
  void _showCreateMappingDialog() {
    // Default values for the new mapping
    String mappingType = 'entity';
    String mappingColor = _selectedColor;
    String mappingTag = '';

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Create New Mapping'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Selected NLP Lines:'),
                Container(
                  height: 100,
                  margin: EdgeInsets.symmetric(vertical: 8),
                  padding: EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: ListView.builder(
                    itemCount: _selectedNlpLines.length,
                    itemBuilder: (context, index) {
                      return Text(
                        _selectedNlpLines[index],
                        style: TextStyle(fontSize: 12),
                      );
                    },
                  ),
                ),
                SizedBox(height: 16),
                DropdownButtonFormField<String>(
                  decoration: InputDecoration(
                    labelText: 'Mapping Type',
                    border: OutlineInputBorder(),
                  ),
                  value: mappingType,
                  items: [
                    DropdownMenuItem(value: 'entity', child: Text('Entity')),
                    DropdownMenuItem(
                      value: 'attribute',
                      child: Text('Attribute'),
                    ),
                    DropdownMenuItem(
                      value: 'relation',
                      child: Text('Relation'),
                    ),
                    DropdownMenuItem(
                      value: 'validation',
                      child: Text('Validation'),
                    ),
                    DropdownMenuItem(value: 'other', child: Text('Other')),
                  ],
                  onChanged: (value) {
                    mappingType = value!;
                  },
                ),
                SizedBox(height: 16),
                TextField(
                  decoration: InputDecoration(
                    labelText: 'Tag',
                    border: OutlineInputBorder(),
                    hintText: 'Enter a tag for this mapping',
                  ),
                  onChanged: (value) {
                    mappingTag = value;
                  },
                ),
                SizedBox(height: 16),
                Row(
                  children: [
                    Text('Color: '),
                    SizedBox(width: 8),
                    Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color: Color(
                          int.parse(mappingColor.substring(1), radix: 16) +
                              0xFF000000,
                        ),
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    SizedBox(width: 8),
                    Text(mappingColor),
                  ],
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text('CANCEL'),
            ),
            ElevatedButton(
              onPressed: () {
                _createMapping(
                  type: mappingType,
                  tag: mappingTag,
                  color: mappingColor,
                );
                Navigator.of(context).pop();
              },
              child: Text('CREATE'),
            ),
          ],
        );
      },
    );
  }

  // Create a new mapping
  void _createMapping({
    required String type,
    required String tag,
    required String color,
  }) {
    // Generate a unique ID for the new mapping
    final String id = DateTime.now().millisecondsSinceEpoch.toString();

    // Store the line indices for the selected NLP line
    Map<String, List<int>> lineIndices = {};
    for (String line in _selectedNlpLines) {
      if (_selectedLineIndices.containsKey(line)) {
        lineIndices[line] = List<int>.from(_selectedLineIndices[line]!);
      }
    }

    // Create a new mapping object
    final newMapping = {
      'id': id,
      'solutionId': _solution!.id,
      'nlpLine': _selectedNlpLines.first,
      'javaLines': <String>[],
      'sqlLines': <String>[],
      'type': type,
      'color': color,
      'tag': tag,
      'level': _isWordSelectionMode ? 'word' : 'line',
      'lineIndices': lineIndices, // Store the specific line indices
    };

    // Add the new mapping to the list of mappings
    setState(() {
      _mappings.add(newMapping);

      // Find mappings for the selected NLP lines again
      _findMappingsForSelection();

      // Show a success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Mapping created successfully'),
          duration: Duration(seconds: 2),
        ),
      );
    });
  }

  // Extract entities from the selected text
  void _extractEntities() {
    // This is a simplified implementation
    // In a real application, this would use NLP techniques to extract entities

    final text = _selectedNlpLines.join(' ');
    final words = text.split(' ');

    // Simple heuristic: look for capitalized words as potential entities
    final potentialEntities = words.where((word) {
      if (word.isEmpty) return false;
      final firstChar = word[0];
      return firstChar == firstChar.toUpperCase() &&
          firstChar != firstChar.toLowerCase(); // Check if it's a letter
    }).toList();

    // Show the results
    _showParsingResults('Potential Entities', potentialEntities);
  }

  // Extract attributes from the selected text
  void _extractAttributes() {
    // This is a simplified implementation
    final text = _selectedNlpLines.join(' ');

    // Simple heuristic: look for words followed by a type or value
    final regex = RegExp(
      r'\b\w+\s+(number|id|name|date|amount|value|type|status)\b',
      caseSensitive: false,
    );
    final matches = regex.allMatches(text);

    final potentialAttributes =
        matches.map((match) => match.group(0)!).toList();

    // Show the results
    _showParsingResults('Potential Attributes', potentialAttributes);
  }

  // Extract relationships from the selected text
  void _extractRelationships() {
    // This is a simplified implementation
    final text = _selectedNlpLines.join(' ');

    // Simple heuristic: look for relationship verbs
    final relationshipVerbs = [
      'has',
      'contains',
      'includes',
      'belongs to',
      'owns',
      'relates to',
      'is associated with',
      'depends on',
      'references',
      'links to',
    ];

    final potentialRelationships = <String>[];

    for (final verb in relationshipVerbs) {
      final regex = RegExp(
        r'\b\w+\s+' + verb + r'\s+\w+\b',
        caseSensitive: false,
      );
      final matches = regex.allMatches(text);

      for (final match in matches) {
        potentialRelationships.add(match.group(0)!);
      }
    }

    // Show the results
    _showParsingResults('Potential Relationships', potentialRelationships);
  }

  // Show parsing results
  void _showParsingResults(String title, List<String> results) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: SizedBox(
            width: double.maxFinite,
            child: results.isEmpty
                ? Text('No results found')
                : ListView.builder(
                    shrinkWrap: true,
                    itemCount: results.length,
                    itemBuilder: (context, index) {
                      return ListTile(
                        title: Text(results[index]),
                        trailing: IconButton(
                          icon: Icon(Icons.add_circle_outline),
                          onPressed: () {
                            // Create a mapping for this result
                            _createMapping(
                              type: title.contains('Entities')
                                  ? 'entity'
                                  : title.contains('Attributes')
                                      ? 'attribute'
                                      : 'relation',
                              tag: results[index],
                              color: _selectedColor,
                            );
                            Navigator.of(context).pop();
                          },
                          tooltip: 'Create mapping',
                        ),
                      );
                    },
                  ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text('CLOSE'),
            ),
          ],
        );
      },
    );
  }

  // Find corresponding Java and SQL lines from the mappings
  void _findCorrespondingLines() {
    // Clear previous selections
    _selectedJavaLines = [];
    _selectedSqlLines = [];
    _selectedLineIndices.clear(); // Clear all line indices

    // Find corresponding Java and SQL lines from the mappings
    for (final mapping in _selectedMappings) {
      // Add Java lines - preserve the order from the mapping
      for (final javaLine in mapping.javaLines) {
        bool alreadyAdded = false;
        for (var existing in _selectedJavaLines) {
          if (existing.content == javaLine.content &&
              existing.lineIndex == javaLine.lineIndex) {
            alreadyAdded = true;
            break;
          }
        }
        if (!alreadyAdded) {
          _selectedJavaLines.add(javaLine);
        }
      }

      // Add SQL lines - preserve the order from the mapping
      for (final sqlLine in mapping.sqlLines) {
        bool alreadyAdded = false;
        for (var existing in _selectedSqlLines) {
          if (existing.content == sqlLine.content &&
              existing.lineIndex == sqlLine.lineIndex) {
            alreadyAdded = true;
            break;
          }
        }
        if (!alreadyAdded) {
          _selectedSqlLines.add(sqlLine);
        }
      }

      // Update the selected color from the mapping
      if (_selectedMappings.isNotEmpty) {
        _selectedColor = _selectedMappings.first.color;
      }
    }

    // Find the specific line indices from the mappings
    _findSpecificLineIndices();

    // If we're in Java view, extract keywords from the selected Java lines
    if (_selectedView == 'Java' && _selectedJavaLines.isNotEmpty) {
      _extractKeywordsFromSelectedLines();
      _scrollToFirstMappedLine();
    }
  }

  // Find specific line indices from the mappings
  void _findSpecificLineIndices() {
    // Get all Java and SQL lines with their line numbers
    List<Map<String, dynamic>> javaLines = [];
    List<Map<String, dynamic>> sqlLines = [];

    // Parse Java lines from the new format
    if (_solution!.java.code.isNotEmpty) {
      final lines = _solution!.java.code.split('\n');
      for (int i = 0; i < lines.length; i++) {
        javaLines.add({
          'line_no': _solution!.java.lineIndices[i],
          'content': lines[i],
        });
      }
    }

    // Parse SQL lines from the new format
    if (_solution!.sql.code.isNotEmpty) {
      final lines = _solution!.sql.code.split('\n');
      for (int i = 0; i < lines.length; i++) {
        sqlLines.add({
          'line_no': _solution!.sql.lineIndices[i],
          'content': lines[i],
        });
      }
    }

    // For each mapping, find the specific line indices
    for (final mapping in _selectedMappings) {
      // For Java lines in this mapping
      for (final javaLine in mapping.javaLines) {
        // Find the matching line in javaLines
        for (var line in javaLines) {
          if (line['content'] == javaLine.content) {
            if (!_selectedLineIndices.containsKey(javaLine.content)) {
              _selectedLineIndices[javaLine.content] = [];
            }
            if (!_selectedLineIndices[javaLine.content]!.contains(
              line['line_no'],
            )) {
              _selectedLineIndices[javaLine.content]!.add(line['line_no']);
            }
            break;
          }
        }
      }

      // For SQL lines in this mapping
      for (final sqlLine in mapping.sqlLines) {
        // Find the matching line in sqlLines
        for (var line in sqlLines) {
          if (line['content'] == sqlLine.content) {
            if (!_selectedLineIndices.containsKey(sqlLine.content)) {
              _selectedLineIndices[sqlLine.content] = [];
            }
            if (!_selectedLineIndices[sqlLine.content]!.contains(
              line['line_no'],
            )) {
              _selectedLineIndices[sqlLine.content]!.add(line['line_no']);
            }
            break;
          }
        }
      }
    }
  }

  // Handle Java line selection
  void _handleJavaLineSelection(String line, int lineIndex) {
    // Create a LineSelection object for this line
    final lineSelection = LineSelection(line, lineIndex);

    // Store the line index
    if (!_selectedLineIndices.containsKey(line)) {
      _selectedLineIndices[line] = [];
    }
    if (!_selectedLineIndices[line]!.contains(lineIndex)) {
      _selectedLineIndices[line]!.add(lineIndex);
    }
    setState(() {
      // Check if this line is already selected
      bool isSelected = false;
      for (var selected in _selectedJavaLines) {
        if (selected.content == line && selected.lineIndex == lineIndex) {
          isSelected = true;
          break;
        }
      }

      if (isSelected) {
        // Remove the line if it's already selected
        _selectedJavaLines.removeWhere(
          (item) => item.content == line && item.lineIndex == lineIndex,
        );
      } else {
        // Add the line if it's not already selected
        _selectedJavaLines.add(lineSelection);
      }

      // Find mappings for the selected Java lines
      _findMappingsForSelection();

      // Extract keywords from selected Java lines
      _extractKeywordsFromSelectedLines();
    });
  }

  // Extract keywords from selected Java lines
  void _extractKeywordsFromSelectedLines() {
    if (_selectedView != 'Java') return;

    // Clear previous keywords
    _selectedLinesKeywords.clear();
    _selectedKeywords.clear();

    // Extract keywords from selected Java lines
    if (_selectedJavaLines.isNotEmpty) {
      // Convert LineSelection objects to strings
      List<String> javaLineStrings =
          _selectedJavaLines.map((ls) => ls.content).toList();

      _selectedLinesKeywords = _keywordService.getKeywordsFromLines(
        javaLineStrings,
      );
      _selectedKeywords = _selectedLinesKeywords.keys.toList();
    }

    // Force a tab switch to update the Used Constructs tab
    if (_tabController.index == 0) {
      // Already on Used Constructs tab, force a rebuild
      setState(() {});
    }
  }

  // Handle SQL line selection
  void _handleSqlLineSelection(String line, int lineIndex) {
    // Create a LineSelection object for this line
    final lineSelection = LineSelection(line, lineIndex);

    // Store the line index
    if (!_selectedLineIndices.containsKey(line)) {
      _selectedLineIndices[line] = [];
    }
    if (!_selectedLineIndices[line]!.contains(lineIndex)) {
      _selectedLineIndices[line]!.add(lineIndex);
    }
    setState(() {
      // Check if this line is already selected
      bool isSelected = false;
      for (var selected in _selectedSqlLines) {
        if (selected.content == line && selected.lineIndex == lineIndex) {
          isSelected = true;
          break;
        }
      }

      if (isSelected) {
        // Remove the line if it's already selected
        _selectedSqlLines.removeWhere(
          (item) => item.content == line && item.lineIndex == lineIndex,
        );
      } else {
        // Add the line if it's not already selected
        _selectedSqlLines.add(lineSelection);
      }

      // Find mappings for the selected SQL lines
      _findMappingsForSelection();
    });
  }

  // Find mappings for the selected lines
  void _findMappingsForSelection() {
    List<Mapping> mappings = [];

    // Clear the selected line indices
    _selectedLineIndices.clear();

    // Convert _mappings to Mapping objects
    List<Mapping> allMappings = [];
    List<Map<String, dynamic>> allMappingJsons =
        []; // Store the original JSON for line indices

    for (final mappingJson in _mappings) {
      try {
        final mapping = Mapping.fromJson(mappingJson);
        allMappings.add(mapping);
        allMappingJsons.add(mappingJson); // Store the original JSON

        // Also add child mappings if present
        if (mappingJson.containsKey('children')) {
          final childrenJson = mappingJson['children'] as List<dynamic>;
          for (int i = 0; i < childrenJson.length; i++) {
            try {
              final childJson = childrenJson[i];
              final childMapping = Mapping.fromJson(childJson);
              allMappings.add(childMapping);
              allMappingJsons.add(childJson); // Store the original JSON
            } catch (e) {
              print('Error parsing child mapping: $e');
            }
          }
        }
      } catch (e) {
        print('Error parsing mapping: $e');
      }
    }

    // Find mappings for selected NLP lines
    if (_selectedNlpLines.isNotEmpty) {
      bool foundExactMatch = false;

      for (int i = 0; i < allMappings.length; i++) {
        final mapping = allMappings[i];
        final mappingJson = allMappingJsons[i];

        for (final selectedNlpLine in _selectedNlpLines) {
          String nlpLineToCheck = selectedNlpLine;

          // If in word selection mode, the selected line might contain position information
          if (_isWordSelectionMode && selectedNlpLine.contains(":")) {
            // Extract just the word part, not the position
            nlpLineToCheck = selectedNlpLine.split(":")[0];
          }

          // For word selection mode, we need an exact match or the word must be contained in the mapping
          if (_isWordSelectionMode) {
            // Check if the mapping's NLP line contains the selected word
            if (mapping.nlpLine.contains(nlpLineToCheck)) {
              // If we're in word selection mode, we need to check if this is a word-level mapping
              // or if the word is specifically mapped
              if (mapping.isWordMapping || mapping.nlpLine == nlpLineToCheck) {
                mappings.add(mapping);
                foundExactMatch = true;

                // Load line indices from the mapping JSON if available
                _loadLineIndicesFromMapping(mappingJson);

                break;
              }
            }
          } else {
            // For line selection mode, we need an exact match
            if (mapping.nlpLine == nlpLineToCheck) {
              mappings.add(mapping);
              foundExactMatch = true;

              // Load line indices from the mapping JSON if available
              _loadLineIndicesFromMapping(mappingJson);

              break;
            }
          }
        }
      }

      // If we're in word selection mode and no exact match was found, show a snackbar
      if (_isWordSelectionMode && !foundExactMatch) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('No mapping available for this word/phrase'),
              duration: Duration(seconds: 2),
            ),
          );
        });

        // Clear Java and DB selections
        _selectedJavaLines = [];
        _selectedSqlLines = [];
        _selectedLinesKeywords.clear();
        _selectedKeywords.clear();
      }
    }

    // Find mappings for selected Java lines
    if (_selectedJavaLines.isNotEmpty) {
      for (int i = 0; i < allMappings.length; i++) {
        final mapping = allMappings[i];
        final mappingJson = allMappingJsons[i];

        for (final selectedLine in _selectedJavaLines) {
          for (final javaLine in mapping.javaLines) {
            // Check both content and line index if available
            bool contentMatches = selectedLine.content == javaLine.content;
            bool indexMatches =
                true; // Default to true if either index is -1 (unknown)

            // Only consider line index if both have valid indices (not -1)
            if (selectedLine.lineIndex >= 0 && javaLine.lineIndex >= 0) {
              indexMatches = selectedLine.lineIndex == javaLine.lineIndex;
            }

            if (contentMatches && indexMatches) {
              mappings.add(mapping);

              // Load line indices from the mapping JSON if available
              _loadLineIndicesFromMapping(mappingJson);

              break;
            }
          }
        }
      }
    }

    // Find mappings for selected SQL lines
    if (_selectedSqlLines.isNotEmpty) {
      for (int i = 0; i < allMappings.length; i++) {
        final mapping = allMappings[i];
        final mappingJson = allMappingJsons[i];

        for (final selectedLine in _selectedSqlLines) {
          for (final sqlLine in mapping.sqlLines) {
            // Check both content and line index if available
            bool contentMatches = selectedLine.content == sqlLine.content;
            bool indexMatches =
                true; // Default to true if either index is -1 (unknown)

            // Only consider line index if both have valid indices (not -1)
            if (selectedLine.lineIndex >= 0 && sqlLine.lineIndex >= 0) {
              indexMatches = selectedLine.lineIndex == sqlLine.lineIndex;
            }

            if (contentMatches && indexMatches) {
              mappings.add(mapping);

              // Load line indices from the mapping JSON if available
              _loadLineIndicesFromMapping(mappingJson);

              break;
            }
          }
        }
      }
    }

    setState(() {
      _selectedMappings = mappings;
    });

    // If no line indices were loaded from mappings, find them
    if (_selectedLineIndices.isEmpty &&
        (_selectedJavaLines.isNotEmpty || _selectedSqlLines.isNotEmpty)) {
      _findSpecificLineIndices();
    }
  }

  // Load line indices from a mapping JSON
  void _loadLineIndicesFromMapping(Map<String, dynamic> mappingJson) {
    // Check if the mapping has line indices
    if (mappingJson.containsKey('lineIndices')) {
      try {
        // Get the line indices map
        Map<String, dynamic> lineIndicesJson =
            mappingJson['lineIndices'] as Map<String, dynamic>;

        // Convert the JSON map to our Map<String, List<int>> format
        lineIndicesJson.forEach((line, indices) {
          if (indices is List) {
            if (!_selectedLineIndices.containsKey(line)) {
              _selectedLineIndices[line] = [];
            }

            for (var index in indices) {
              if (index is int &&
                  !_selectedLineIndices[line]!.contains(index)) {
                _selectedLineIndices[line]!.add(index);
              }
            }
          }
        });
      } catch (e) {
        print('Error loading line indices from mapping: $e');
      }
    }
  }

  // Build expandable sections for NSL code
  Widget _buildExpandableSections() {
    return ListView.builder(
      itemCount: _solution!.sections.length,
      itemBuilder: (context, index) {
        final section = _solution!.sections[index];
        return Card(
          margin: EdgeInsets.symmetric(vertical: 4, horizontal: 8),
          child: Theme(
            data: Theme.of(context).copyWith(
              dividerColor: Colors.transparent, // Remove the divider
            ),
            child: ExpansionTile(
              title: Row(
                children: [
                  Icon(
                    _getSectionIcon(section.type),
                    size: 20,
                    color: _getSectionColor(section.type),
                  ),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      section.title,
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ),
                ],
              ),
              subtitle: section.tags.isNotEmpty
                  ? Row(
                      children: [
                        ...section.tags.map(
                          (tag) => Container(
                            margin: EdgeInsets.only(right: 4),
                            padding: EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.grey.shade200,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(tag, style: TextStyle(fontSize: 10)),
                          ),
                        ),
                      ],
                    )
                  : null,
              initiallyExpanded:
                  index == 0, // Expand the first section by default
              childrenPadding: EdgeInsets.all(8),
              children: [_buildSectionContent(section)],
            ),
          ),
        );
      },
    );
  }

  // Get icon for section type
  IconData _getSectionIcon(String type) {
    switch (type.toLowerCase()) {
      case 'prescriptive':
        return Icons.assignment;
      case 'sub-prescriptive':
        return Icons.assignment_turned_in;
      case 'bet':
        return Icons.business;
      case 'pathway':
        return Icons.route;
      default:
        return Icons.description;
    }
  }

  // Get color for section type
  Color _getSectionColor(String type) {
    switch (type.toLowerCase()) {
      case 'prescriptive':
        return Colors.blue;
      case 'sub-prescriptive':
        return Colors.teal;
      case 'bet':
        return Colors.orange;
      case 'pathway':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  // Build section content with selectable lines or words
  Widget _buildSectionContent(NslSection section) {
    // Split the content into lines for selection
    final lines = section.content.split('\n');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section content with selectable lines or words
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade200),
            borderRadius: BorderRadius.circular(4),
          ),
          margin: EdgeInsets.only(top: 8),
          child: _isWordSelectionMode
              ? _buildWordSelectionContent(section.content)
              : ListView.builder(
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  itemCount: lines.length,
                  itemBuilder: (context, index) {
                    final line = lines[index];

                    // Check if this line is selected
                    bool isSelected = _selectedNlpLines.contains(line);

                    // Background color for selected lines
                    Color bgColor = isSelected
                        ? Color(
                            int.parse(
                                  _selectedColor.substring(1),
                                  radix: 16,
                                ) +
                                0xFF000000,
                          ).withOpacity(0.3)
                        : Colors.transparent;

                    // Find tag and color for this line
                    Map<String, String> tagInfo = _findTagForLine(line);
                    String tag = tagInfo['tag']!;
                    String tagColor = tagInfo['color']!;

                    // Convert hex color to Color object
                    Color containerColor = Color(
                      int.parse(tagColor.substring(1), radix: 16) + 0xFF000000,
                    ).withOpacity(0.2);

                    Color textColor = Color(
                      int.parse(tagColor.substring(1), radix: 16) + 0xFF000000,
                    );

                    return InkWell(
                      onTap: () => _handleLineSelection(line, index),
                      child: Container(
                        width: double.infinity,
                        padding: EdgeInsets.symmetric(
                          vertical: 4,
                          horizontal: 8,
                        ),
                        decoration: BoxDecoration(
                          color: bgColor,
                          border: Border(
                            bottom: BorderSide(color: Colors.grey.shade100),
                          ),
                        ),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Only show tag if line is not empty
                            if (line.trim().isNotEmpty) ...[
                              Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: 6,
                                  vertical: 2,
                                ),
                                margin: EdgeInsets.only(right: 8, top: 2),
                                decoration: BoxDecoration(
                                  color: containerColor,
                                  borderRadius: BorderRadius.circular(4),
                                  border: Border.all(
                                    color: containerColor.withOpacity(0.5),
                                  ),
                                ),
                                child: Text(
                                  tag,
                                  style: TextStyle(
                                    fontSize: 10,
                                    color: textColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                            Expanded(child: Text(line)),
                          ],
                        ),
                      ),
                    );
                  },
                ),
        ),
      ],
    );
  }

  // Build content for word selection mode
  Widget _buildWordSelectionContent(String content) {
    return Padding(
      padding: EdgeInsets.all(8),
      child: RichText(
        text: TextSpan(
          style: TextStyle(
            fontFamily: 'monospace',
            fontSize: 14,
            color: Colors.black,
          ),
          children: _buildWordSelectionSpans(content),
        ),
      ),
    );
  }

  // List of common English words to filter out
  final List<String> _commonWords = [
    'a',
    'an',
    'the',
    'and',
    'or',
    'but',
    'if',
    'then',
    'else',
    'when',
    'at',
    'by',
    'for',
    'with',
    'about',
    'against',
    'between',
    'into',
    'through',
    'during',
    'before',
    'after',
    'above',
    'below',
    'to',
    'from',
    'up',
    'down',
    'in',
    'out',
    'on',
    'off',
    'over',
    'under',
    'again',
    'further',
    'then',
    'once',
    'here',
    'there',
    'when',
    'where',
    'why',
    'how',
    'all',
    'any',
    'both',
    'each',
    'few',
    'more',
    'most',
    'other',
    'some',
    'such',
    'no',
    'nor',
    'not',
    'only',
    'own',
    'same',
    'so',
    'than',
    'too',
    'very',
    'can',
    'will',
    'just',
    'should',
    'now',
    'has',
    'have',
    'had',
    'is',
    'are',
    'was',
    'were',
    'be',
    'been',
    'being',
    'do',
    'does',
    'did',
    'doing',
    'of',
    'that',
    'this',
    'these',
    'those',
    'as',
    'it',
    'its',
    'which',
    'who',
    'whom',
    'whose',
    'what',
    'whatever',
    'whenever',
    'wherever',
    'however',
    'whichever',
    'whoever',
    'whomever',
    'would',
    'could',
    'should',
    'may',
    'might',
    'must',
    'shall',
    'need',
    'ought',
    'used',
  ];

  // Check if a word is a common word that should be filtered out
  bool _isCommonWord(String word) {
    // Clean the word by removing punctuation and converting to lowercase
    String cleanWord = word.toLowerCase().replaceAll(RegExp(r'[^\w\s]'), '');
    return _commonWords.contains(cleanWord);
  }

  // Build text spans for word selection - with filtering for common words
  List<TextSpan> _buildWordSelectionSpans(String content) {
    List<TextSpan> spans = [];

    // Split content into phrases with special handling for brackets
    List<String> parts = [];

    // First, let's handle the case with brackets
    // We'll use a more complex approach to handle nested brackets
    String remaining = content;

    while (remaining.isNotEmpty) {
      // Find the next comma
      int commaPos = remaining.indexOf(',');

      // If no more commas, add the rest as the last part
      if (commaPos == -1) {
        parts.add(remaining);
        break;
      }

      // Check if there's an opening bracket before the comma
      int openBracketPos = -1;
      String openBracket = "";
      String closeBracket = "";

      // Check for each type of bracket
      for (var bracketPair in [
        ['(', ')'],
        ['[', ']'],
        ['{', '}'],
      ]) {
        int pos = remaining.indexOf(bracketPair[0]);
        if (pos != -1 &&
            (openBracketPos == -1 || pos < openBracketPos) &&
            pos < commaPos) {
          openBracketPos = pos;
          openBracket = bracketPair[0];
          closeBracket = bracketPair[1];
        }
      }

      // If there's an opening bracket before the comma
      if (openBracketPos != -1) {
        // Find the matching closing bracket
        int bracketCount = 1;
        int closePos = openBracketPos + 1;

        while (bracketCount > 0 && closePos < remaining.length) {
          if (remaining[closePos] == openBracket) {
            bracketCount++;
          } else if (remaining[closePos] == closeBracket) {
            bracketCount--;
          }
          closePos++;
        }

        // If we found the closing bracket
        if (bracketCount == 0) {
          // Check if there's a comma after the closing bracket
          int nextCommaPos = remaining.indexOf(',', closePos);

          if (nextCommaPos != -1) {
            // Add everything up to and including the comma
            parts.add(remaining.substring(0, nextCommaPos + 1));
            remaining = remaining.substring(nextCommaPos + 1);
          } else {
            // No comma after the closing bracket, add the rest
            parts.add(remaining);
            break;
          }
        } else {
          // No matching closing bracket, treat as normal
          parts.add(remaining.substring(0, commaPos + 1));
          remaining = remaining.substring(commaPos + 1);
        }
      } else {
        // No opening bracket before the comma, treat as normal
        parts.add(remaining.substring(0, commaPos + 1));
        remaining = remaining.substring(commaPos + 1);
      }
    }

    // If we didn't find any parts (which shouldn't happen), fall back to simple splitting
    if (parts.isEmpty) {
      RegExp phraseRegex = RegExp(r'[^,]+,|[^,]+$');
      Iterable<RegExpMatch> matches = phraseRegex.allMatches(content);
      for (var match in matches) {
        parts.add(match.group(0)!);
      }
    }

    // Process each part to create spans
    for (int i = 0; i < parts.length; i++) {
      final part = parts[i];

      // Skip empty parts
      if (part.isEmpty) continue;

      // Check if this part is just a common word
      String trimmedPart = part.trim();
      if (trimmedPart.endsWith(",")) {
        trimmedPart = trimmedPart.substring(0, trimmedPart.length - 1).trim();
      }

      // If it's a standalone common word, display it as non-selectable
      if (!trimmedPart.contains(" ") &&
          !trimmedPart.contains("(") &&
          !trimmedPart.contains("[") &&
          !trimmedPart.contains("{") &&
          _isCommonWord(trimmedPart)) {
        spans.add(
          TextSpan(
            text: part,
            style: TextStyle(
              color: Colors.grey.shade500, // Grey out common words
              fontStyle: FontStyle.italic,
            ),
          ),
        );
        continue;
      }

      // Check if this part contains brackets - if so, make the entire part selectable
      bool containsBrackets = trimmedPart.contains('(') ||
          trimmedPart.contains('[') ||
          trimmedPart.contains('{');

      if (containsBrackets) {
        // Check if this part is selected
        bool isSelected = false;
        if (_selectedNlpLines.isNotEmpty) {
          for (var selected in _selectedNlpLines) {
            if (trimmedPart == selected.trim()) {
              isSelected = true;
              break;
            }
          }
        }

        spans.add(
          TextSpan(
            text: part,
            style: TextStyle(
              backgroundColor: isSelected
                  ? Color(
                      int.parse(_selectedColor.substring(1), radix: 16) +
                          0xFF000000,
                    ).withOpacity(0.3)
                  : Colors.transparent,
              // Add a special style for phrases with brackets
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
            recognizer: TapGestureRecognizer()
              ..onTap = () {
                // Handle phrase selection
                _handleWordSelection(trimmedPart);
              },
          ),
        );

        continue; // Skip the rest of the processing for this part
      }

      // For phrases that contain spaces, process all words to find common words
      if (trimmedPart.contains(" ")) {
        // Split the phrase into words
        List<String> words = trimmedPart.split(" ");

        // Create a list to track which words are common
        List<bool> isCommonWord = List.generate(
          words.length,
          (index) => _isCommonWord(words[index]),
        );

        // If any word is common, we need to create multiple spans
        if (isCommonWord.contains(true)) {
          int currentPos = 0;
          String currentText = part;

          for (int j = 0; j < words.length; j++) {
            String word = words[j];
            int wordPos = currentText.indexOf(word, currentPos);

            if (wordPos >= 0) {
              // If this is a common word, make it non-selectable
              if (isCommonWord[j]) {
                // Add any text before this word
                if (wordPos > currentPos) {
                  String beforeText = currentText.substring(
                    currentPos,
                    wordPos,
                  );
                  spans.add(
                    TextSpan(
                      text: beforeText,
                      style: TextStyle(color: Colors.black),
                    ),
                  );
                }

                // Add the common word as non-selectable
                spans.add(
                  TextSpan(
                    text: word + (j < words.length - 1 ? " " : ""),
                    style: TextStyle(
                      color: Colors.grey.shade500, // Grey out common words
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                );

                // Update current position
                currentPos = wordPos +
                    word.length +
                    (j < words.length - 1 ? 1 : 0); // +1 for space
              }
              // If this is the last word or the next word is common, make this word selectable
              else if (j == words.length - 1 || isCommonWord[j + 1]) {
                // Add any text before this word
                if (wordPos > currentPos) {
                  String beforeText = currentText.substring(
                    currentPos,
                    wordPos,
                  );
                  spans.add(
                    TextSpan(
                      text: beforeText,
                      style: TextStyle(color: Colors.black),
                    ),
                  );
                }

                // Add the word as selectable
                String selectableText =
                    word + (j < words.length - 1 ? " " : "");
                bool isSelected = false;
                if (_selectedNlpLines.isNotEmpty) {
                  for (var selected in _selectedNlpLines) {
                    if (word.trim() == selected.trim()) {
                      isSelected = true;
                      break;
                    }
                  }
                }

                spans.add(
                  TextSpan(
                    text: selectableText,
                    style: TextStyle(
                      backgroundColor: isSelected
                          ? Color(
                              int.parse(
                                    _selectedColor.substring(1),
                                    radix: 16,
                                  ) +
                                  0xFF000000,
                            ).withOpacity(0.3)
                          : Colors.transparent,
                      color: Colors.black,
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        _handleWordSelection(word.trim());
                      },
                  ),
                );

                // Update current position
                currentPos = wordPos +
                    word.length +
                    (j < words.length - 1 ? 1 : 0); // +1 for space
              }
              // Otherwise, continue to the next word
              else {
                continue;
              }
            }
          }

          // Add any remaining text
          if (currentPos < currentText.length) {
            spans.add(
              TextSpan(
                text: currentText.substring(currentPos),
                style: TextStyle(color: Colors.black),
              ),
            );
          }

          continue; // Skip the default span creation below
        }
      }

      // Check if this part is selected
      bool isSelected = false;
      if (_selectedNlpLines.isNotEmpty) {
        for (var selected in _selectedNlpLines) {
          if (trimmedPart == selected.trim()) {
            isSelected = true;
            break;
          }
        }
      }

      // Create a text span for this part (default case)
      spans.add(
        TextSpan(
          text: part,
          style: TextStyle(
            backgroundColor: isSelected
                ? Color(
                    int.parse(_selectedColor.substring(1), radix: 16) +
                        0xFF000000,
                  ).withOpacity(0.3)
                : Colors.transparent,
            color: Colors.black,
            fontWeight: FontWeight.normal,
          ),
          recognizer: TapGestureRecognizer()
            ..onTap = () {
              // Handle phrase selection
              _handleWordSelection(trimmedPart);
            },
        ),
      );
    }

    return spans;
  }

  // Handle word selection
  void _handleWordSelection(String word) {
    // Store the line index if available
    if (word.contains(":")) {
      String wordPart = word.split(":")[0];
      if (!_selectedLineIndices.containsKey(wordPart)) {
        _selectedLineIndices[wordPart] = [];
      }
      // We don't have a line index for word selection, so we use -1
      if (!_selectedLineIndices[wordPart]!.contains(-1)) {
        _selectedLineIndices[wordPart]!.add(-1);
      }
      word = wordPart; // Use just the word part for selection
    } else {
      // For regular words without position info
      if (!_selectedLineIndices.containsKey(word)) {
        _selectedLineIndices[word] = [];
      }
      if (!_selectedLineIndices[word]!.contains(-1)) {
        _selectedLineIndices[word]!.add(-1);
      }
    }

    setState(() {
      // Clear previous selections
      _clearSelections();

      // Select the new word
      _selectedNlpLines = [word];

      // Find mappings for the selected word
      _findMappingsForSelection();

      // Find corresponding Java and SQL lines
      _findCorrespondingLines();

      // If we found mappings, switch to the appropriate view
      if (_selectedMappings.isNotEmpty) {
        // If there are SQL lines but no Java lines, switch to DB view
        if (_selectedSqlLines.isNotEmpty && _selectedJavaLines.isEmpty) {
          _selectedView = 'DB';
        } else {
          // Otherwise default to Java view
          _selectedView = 'Java';
        }

        // Force a switch to the "Used Constructs" tab
        if (_tabController.index != 0) {
          _tabController.animateTo(0); // Switch to "Used Constructs" tab
        }
      } else {
        // No mappings found, show a simple snackbar
        WidgetsBinding.instance.addPostFrameCallback((_) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('No mapping available for this word/phrase'),
              duration: Duration(seconds: 2),
            ),
          );
        });
      }
    });
  }

  // Handle line selection - only one line at a time
  void _handleLineSelection(String line, int lineIndex) {
    // Store the line index
    if (!_selectedLineIndices.containsKey(line)) {
      _selectedLineIndices[line] = [];
    }
    if (!_selectedLineIndices[line]!.contains(lineIndex)) {
      _selectedLineIndices[line]!.add(lineIndex);
    }
    setState(() {
      // Clear previous selections
      _clearSelections();

      // Select the new line
      _selectedNlpLines = [line];

      // Find mappings for the selected line
      _findMappingsForSelection();

      // Find corresponding Java and SQL lines
      _findCorrespondingLines();

      // If we found mappings, switch to the appropriate view
      if (_selectedMappings.isNotEmpty) {
        // If there are SQL lines but no Java lines, switch to DB view
        if (_selectedSqlLines.isNotEmpty && _selectedJavaLines.isEmpty) {
          _selectedView = 'DB';
        } else {
          // Otherwise default to Java view
          _selectedView = 'Java';
        }

        // Force a switch to the "Used Constructs" tab
        if (_tabController.index != 0) {
          _tabController.animateTo(0); // Switch to "Used Constructs" tab
        }
      } else {
        // No mappings found, show a simple snackbar
        WidgetsBinding.instance.addPostFrameCallback((_) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('No mapping available for this selection'),
              duration: Duration(seconds: 2),
            ),
          );
        });
      }
    });
  }

  // Scroll to the first mapped line in the Java or DB code viewer
  void _scrollToFirstMappedLine() {
    // Get the lines to scroll to
    List<LineSelection> linesToScroll =
        _selectedView == 'Java' ? _selectedJavaLines : _selectedSqlLines;
    if (linesToScroll.isEmpty) return;

    // Wait for the next frame to ensure the view is rendered
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Get the appropriate scroll controller
      ScrollController controller =
          _selectedView == 'Java' ? _javaScrollController : _dbScrollController;

      if (!controller.hasClients) return;

      // First scroll to top
      controller.animateTo(
        0.0,
        duration: Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );

      // Then after a short delay, scroll to the first highlighted line
      Future.delayed(Duration(milliseconds: 350), () {
        if (!controller.hasClients) return;

        // Get the viewport height and content height
        double viewportHeight = controller.position.viewportDimension;
        double contentHeight =
            controller.position.maxScrollExtent + viewportHeight;

        // Calculate line height based on content height and number of lines
        String code = _selectedView == 'Java'
            ? _solution!.java.code
            : _solution!.sql.code;
        int totalLines = code.split('\n').length;
        double lineHeight = contentHeight / totalLines;

        // Find the first valid line index
        int? firstLineIndex;
        for (var lineSelection in linesToScroll) {
          if (lineSelection.lineIndex >= 0) {
            firstLineIndex = lineSelection.lineIndex;
            break;
          }
        }

        // If no valid line index found, try to find by content
        if (firstLineIndex == null) {
          List<String> allLines = code.split('\n');
          for (int i = 0; i < allLines.length; i++) {
            for (var lineSelection in linesToScroll) {
              if (allLines[i] == lineSelection.content) {
                firstLineIndex = i;
                break;
              }
            }
            if (firstLineIndex != null) break;
          }
        }

        if (firstLineIndex == null) return;

        // Calculate scroll position to show the line at the top of the viewport
        double scrollPosition = firstLineIndex * lineHeight;

        // Ensure scroll position is within bounds
        scrollPosition =
            scrollPosition.clamp(0.0, controller.position.maxScrollExtent);

        // Animate to the position
        controller.animateTo(
          scrollPosition,
          duration: Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      });
    });
  }

  // Get a contrasting color (black or white) based on background color brightness
  Color _getContrastColor(Color backgroundColor) {
    // Calculate the perceived brightness of the background color
    // Using the formula: (299*R + 587*G + 114*B) / 1000
    double brightness = (backgroundColor.red * 299 +
            backgroundColor.green * 587 +
            backgroundColor.blue * 114) /
        1000;

    // Return white for dark backgrounds, black for light backgrounds
    return brightness > 125 ? Colors.black : Colors.white;
  }

  // Find tag and color for a line by looking through mappings
  Map<String, String> _findTagForLine(String line) {
    // Convert _mappings to Mapping objects
    for (final mappingJson in _mappings) {
      try {
        final mapping = Mapping.fromJson(mappingJson);

        // Check if this mapping matches the line
        if (mapping.nlpLine == line) {
          // If tag exists, return it, otherwise use first word of line
          String tag =
              mapping.tag.isNotEmpty ? mapping.tag : line.split(' ').first;

          return {'tag': tag, 'color': mapping.color};
        }

        // Also check child mappings if present
        if (mappingJson.containsKey('children')) {
          final childrenJson = mappingJson['children'] as List<dynamic>;
          for (final childJson in childrenJson) {
            try {
              final childMapping = Mapping.fromJson(childJson);
              if (childMapping.nlpLine == line) {
                // If tag exists, return it, otherwise use first word of line
                String tag = childMapping.tag.isNotEmpty
                    ? childMapping.tag
                    : line.split(' ').first;

                return {'tag': tag, 'color': childMapping.color};
              }
            } catch (e) {
              print('Error parsing child mapping: $e');
            }
          }
        }
      } catch (e) {
        print('Error parsing mapping: $e');
      }
    }

    // If no mapping found, use first word as tag with default color
    return {'tag': line.split(' ').first, 'color': _selectedColor};
  }

  Widget _buildConstructsList(List<String> constructs) {
    return ListView.builder(
      padding: EdgeInsets.all(8),
      itemCount: constructs.length,
      itemBuilder: (context, index) {
        final construct = constructs[index];
        // Always use the explanation from the JavaKeywordService for consistency
        // This ensures we show the proper description, not the tag
        final explanation = _keywordService.allKeywords[construct] ?? '';

        // Check if this keyword is used in the selected lines
        final isUsedInSelectedLines = _selectedKeywords.contains(construct);

        // Get the color for this construct from the mapping if it's used in selected lines
        Color? constructColor;
        if (isUsedInSelectedLines && _selectedMappings.isNotEmpty) {
          // Use the color from the first mapping
          String hexColor = _selectedMappings.first.color;
          constructColor = Color(
            int.parse(hexColor.substring(1), radix: 16) + 0xFF000000,
          ).withOpacity(0.2); // Light opacity for the card background
        }

        return Card(
          margin: EdgeInsets.only(bottom: 8),
          // No background color for the card itself
          child: InkWell(
            onTap: () {
              // If this keyword is used in the selected lines, highlight those lines
              if (isUsedInSelectedLines) {
                // Find all Java lines that contain this keyword
                final lines = _solution!.java.code.split('\n');
                final matchingLines = <LineSelection>[];

                for (int i = 0; i < lines.length; i++) {
                  final line = lines[i];
                  // Use regex to match whole words only
                  final RegExp regex = RegExp('\\b$construct\\b');
                  if (regex.hasMatch(line)) {
                    matchingLines.add(LineSelection(line, i));
                  }
                }

                // Update selected Java lines
                setState(() {
                  _selectedJavaLines = matchingLines;
                  // Find mappings for the selected Java lines
                  _findMappingsForSelection();
                });
              }

              // Show explanation if available
              if (explanation.isNotEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('$construct: $explanation')),
                );
              }
            },
            child: Container(
              decoration: BoxDecoration(
                color: isUsedInSelectedLines
                    ? (_selectedMappings.isNotEmpty
                        ? Color(
                            int.parse(
                                  _selectedMappings.first.color.substring(1),
                                  radix: 16,
                                ) +
                                0xFF000000,
                          ).withOpacity(0.3)
                        : Colors.blue.shade100)
                    : null,
                borderRadius: BorderRadius.circular(4),
              ),
              margin: EdgeInsets.all(4),
              padding: EdgeInsets.all(8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      // Show an icon if the keyword is used in the selected lines
                      if (isUsedInSelectedLines)
                        Icon(Icons.check_circle, color: Colors.white, size: 16),
                      SizedBox(width: isUsedInSelectedLines ? 8 : 0),
                      Text(
                        construct,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          // Highlight the text if the keyword is used in the selected lines
                          color: isUsedInSelectedLines
                              ? (_selectedMappings.isNotEmpty
                                  ? _getContrastColor(
                                      Color(
                                        int.parse(
                                              _selectedMappings.first.color
                                                  .substring(1),
                                              radix: 16,
                                            ) +
                                            0xFF000000,
                                      ),
                                    )
                                  : Colors.white)
                              : null,
                        ),
                      ),
                    ],
                  ),
                  // Always show the explanation, even if it's empty
                  Text(
                    explanation.isNotEmpty
                        ? explanation
                        : "No description available",
                    style: TextStyle(
                      fontSize: 12,
                      color: isUsedInSelectedLines
                          ? (_selectedMappings.isNotEmpty
                              ? _getContrastColor(
                                  Color(
                                    int.parse(
                                          _selectedMappings.first.color
                                              .substring(1),
                                          radix: 16,
                                        ) +
                                        0xFF000000,
                                  ),
                                )
                              : Colors.white.withOpacity(0.9))
                          : Colors.grey.shade700,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
