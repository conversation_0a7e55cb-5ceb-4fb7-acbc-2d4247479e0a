// This file uses conditional exports to provide different implementations
// for different platforms. The correct implementation is chosen automatically
// at compile time.

import 'import_service_io.dart' if (dart.library.html) 'import_service_web.dart';

/// ImportService provides platform-agnostic import capabilities.
/// It uses File API for mobile/desktop platforms and Blob/upload for web.
class ImportService {
  /// Import a solution and its mappings from a JSON file
  static Future<Map<String, dynamic>> importSolutionWithMappings(String jsonString) async {
    return await ImportServiceImpl.importSolutionWithMappings(jsonString);
  }
}
