// This file uses conditional exports to provide different implementations
// for different platforms. The correct implementation is chosen automatically
// at compile time.

import 'export_service_io.dart' if (dart.library.html) 'export_service_web.dart';
import '../models/solution.dart';
import '../models/mapping.dart';

/// ExportService provides platform-agnostic export capabilities.
/// It uses File API for mobile/desktop platforms and Blob/download for web.
class ExportService {
  /// Export a solution and its mappings as a JSON file
  static Future<String> exportSolutionWithMappings(Solution solution, List<Mapping> mappings) async {
    return await ExportServiceImpl.exportSolutionWithMappings(solution, mappings);
  }
}
