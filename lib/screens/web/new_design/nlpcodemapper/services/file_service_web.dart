import 'dart:convert';
import 'dart:html' as html;

class FileServiceImpl {
  // Constants for web storage keys
  static const String _solutionsKey = 'solutions';
  static const String _mappingsKey = 'mappings';

  // Initialize storage if it doesn't exist
  Future<void> initializeFiles() async {
    // For web, check if localStorage has the keys
    if (html.window.localStorage[_solutionsKey] == null) {
      html.window.localStorage[_solutionsKey] = jsonEncode({'solutions': []});
    }
    if (html.window.localStorage[_mappingsKey] == null) {
      html.window.localStorage[_mappingsKey] = jsonEncode({'mappings': []});
    }
  }

  // Read solutions from localStorage
  Future<List<Map<String, dynamic>>> readSolutions() async {
    try {
      final contents = html.window.localStorage[_solutionsKey] ?? jsonEncode({'solutions': []});
      final data = jsonDecode(contents) as Map<String, dynamic>;
      return List<Map<String, dynamic>>.from(data['solutions']);
    } catch (e) {
      print('Error reading solutions: $e');
      return [];
    }
  }

  // Write solutions to localStorage
  Future<void> writeSolutions(List<Map<String, dynamic>> solutions) async {
    try {
      final data = jsonEncode({'solutions': solutions});
      html.window.localStorage[_solutionsKey] = data;
    } catch (e) {
      print('Error writing solutions: $e');
    }
  }

  // Read mappings from localStorage
  Future<List<Map<String, dynamic>>> readMappings() async {
    try {
      final contents = html.window.localStorage[_mappingsKey] ?? jsonEncode({'mappings': []});
      final data = jsonDecode(contents) as Map<String, dynamic>;
      return List<Map<String, dynamic>>.from(data['mappings']);
    } catch (e) {
      print('Error reading mappings: $e');
      return [];
    }
  }

  // Write mappings to localStorage
  Future<void> writeMappings(List<Map<String, dynamic>> mappings) async {
    try {
      final data = jsonEncode({'mappings': mappings});
      html.window.localStorage[_mappingsKey] = data;
    } catch (e) {
      print('Error writing mappings: $e');
    }
  }
}
