import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';

class FileServiceImpl {
  // Get paths to JSON files
  Future<String> get _solutionsPath async {
    final directory = await getApplicationDocumentsDirectory();
    return '${directory.path}/solutions.json';
  }

  Future<String> get _mappingsPath async {
    final directory = await getApplicationDocumentsDirectory();
    return '${directory.path}/mappings.json';
  }

  // Initialize files if they don't exist
  Future<void> initializeFiles() async {
    final solutionsFile = File(await _solutionsPath);
    final mappingsFile = File(await _mappingsPath);

    if (!await solutionsFile.exists()) {
      await solutionsFile.writeAsString(jsonEncode({'solutions': []}));
    }

    if (!await mappingsFile.exists()) {
      await mappingsFile.writeAsString(jsonEncode({'mappings': []}));
    }
  }

  // Read solutions from file
  Future<List<Map<String, dynamic>>> readSolutions() async {
    try {
      final file = File(await _solutionsPath);
      final contents = await file.readAsString();
      final data = jsonDecode(contents) as Map<String, dynamic>;
      return List<Map<String, dynamic>>.from(data['solutions']);
    } catch (e) {
      print('Error reading solutions: $e');
      return [];
    }
  }

  // Write solutions to file
  Future<void> writeSolutions(List<Map<String, dynamic>> solutions) async {
    try {
      final file = File(await _solutionsPath);
      await file.writeAsString(jsonEncode({'solutions': solutions}));
    } catch (e) {
      print('Error writing solutions: $e');
    }
  }

  // Read mappings from file
  Future<List<Map<String, dynamic>>> readMappings() async {
    try {
      final file = File(await _mappingsPath);
      final contents = await file.readAsString();
      final data = jsonDecode(contents) as Map<String, dynamic>;
      return List<Map<String, dynamic>>.from(data['mappings']);
    } catch (e) {
      print('Error reading mappings: $e');
      return [];
    }
  }

  // Write mappings to file
  Future<void> writeMappings(List<Map<String, dynamic>> mappings) async {
    try {
      final file = File(await _mappingsPath);
      await file.writeAsString(jsonEncode({'mappings': mappings}));
    } catch (e) {
      print('Error writing mappings: $e');
    }
  }
}
