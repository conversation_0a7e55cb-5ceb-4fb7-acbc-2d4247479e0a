import 'package:flutter/material.dart';

class TextOverflowDetector {
  /// Checks if the given text would overflow in the available width
  static bool wouldTextOverflow({
    required String text,
    required TextStyle style,
    required double maxWidth,
    int? maxLines,
  }) {
    if (text.isEmpty || maxWidth <= 0) return false;

    final TextPainter textPainter = TextPainter(
      text: TextSpan(text: text, style: style),
      maxLines: maxLines ?? 1,
      textDirection: TextDirection.ltr,
    );

    // Layout the text with the given constraints
    textPainter.layout(maxWidth: maxWidth);

    // Check if text overflows
    return textPainter.didExceedMaxLines || textPainter.width > maxWidth;
  }

  /// Checks if a RichText would overflow in the available width
  static bool wouldRichTextOverflow({
    required List<InlineSpan> textSpans,
    required double maxWidth,
    int? maxLines,
  }) {
    if (textSpans.isEmpty || maxWidth <= 0) return false;

    final TextPainter textPainter = TextPainter(
      text: TextSpan(children: textSpans),
      maxLines: maxLines ?? 1,
      textDirection: TextDirection.ltr,
    );

    // Layout the text with the given constraints
    textPainter.layout(maxWidth: maxWidth);

    // Check if text overflows
    return textPainter.didExceedMaxLines || textPainter.width > maxWidth;
  }

  /// Gets the actual width of text when rendered
  static double getTextWidth({
    required String text,
    required TextStyle style,
  }) {
    if (text.isEmpty) return 0;

    final TextPainter textPainter = TextPainter(
      text: TextSpan(text: text, style: style),
      maxLines: 1,
      textDirection: TextDirection.ltr,
    );

    textPainter.layout(maxWidth: double.infinity);
    return textPainter.width;
  }

  /// Gets the actual width of RichText when rendered
  static double getRichTextWidth({
    required List<InlineSpan> textSpans,
  }) {
    if (textSpans.isEmpty) return 0;

    final TextPainter textPainter = TextPainter(
      text: TextSpan(children: textSpans),
      maxLines: 1,
      textDirection: TextDirection.ltr,
    );

    textPainter.layout(maxWidth: double.infinity);
    return textPainter.width;
  }
}
