import 'package:flutter/material.dart';

class ModelInfo {
  final String id;
  final String name;
  final String description;
  final bool isPro;
  final bool isSelected;

  ModelInfo({
    required this.id,
    required this.name,
    required this.description,
    this.isPro = false,
    this.isSelected = false,
  });
}

class ModelSelectionDropdown extends StatefulWidget {
  final List<ModelInfo> models;
  final ModelInfo selectedModel;
  final Function(ModelInfo) onModelSelected;
  final bool dropdownAbove;

  const ModelSelectionDropdown({
    super.key,
    required this.models,
    required this.selectedModel,
    required this.onModelSelected,
    this.dropdownAbove = false,
  });

  @override
  State<ModelSelectionDropdown> createState() => _ModelSelectionDropdownState();
}

class _ModelSelectionDropdownState extends State<ModelSelectionDropdown> 
    with RouteAware, WidgetsBindingObserver {
  bool isDropdownOpen = false;
  bool isHovered = false;
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;
  RouteObserver<PageRoute>? _routeObserver;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Subscribe to route changes
    final modalRoute = ModalRoute.of(context);
    if (modalRoute is PageRoute) {
      _routeObserver = RouteObserver<PageRoute>();
      _routeObserver?.subscribe(this, modalRoute);
    }
  }

  @override
  void dispose() {
    _removeOverlay();
    _routeObserver?.unsubscribe(this);
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void deactivate() {
    _closeDropdown();
    super.deactivate();
  }

  // RouteAware methods
  @override
  void didPush() {
    // Called when this route is pushed
  }

  @override
  void didPushNext() {
    // Called when a new route is pushed on top of this one
    _closeDropdown();
  }

  @override
  void didPop() {
    // Called when this route is popped
    _closeDropdown();
  }

  @override
  void didPopNext() {
    // Called when returning to this route from another
    _closeDropdown();
  }

  // WidgetsBindingObserver methods
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.paused || state == AppLifecycleState.inactive) {
      _closeDropdown();
    }
  }

  void _removeOverlay() {
    if (_overlayEntry != null) {
      _overlayEntry!.remove();
      _overlayEntry = null;
    }
  }

  void _closeDropdown() {
    if (isDropdownOpen) {
      _removeOverlay();
      if (mounted) {
        setState(() {
          isDropdownOpen = false;
        });
      }
    }
  }

  void _toggleDropdown() {
    if (isDropdownOpen) {
      _closeDropdown();
    } else {
      _showDropdown();
    }
  }

  void _showDropdown() {
    if (!mounted) return;
    
    _overlayEntry = _createOverlayEntry();
    Overlay.of(context).insert(_overlayEntry!);
    
    if (mounted) {
      setState(() {
        isDropdownOpen = true;
      });
    }
  }

  OverlayEntry _createOverlayEntry() {
    RenderBox renderBox = context.findRenderObject() as RenderBox;
    var offset = renderBox.localToGlobal(Offset.zero);
    var size = renderBox.size;

    // Calculate dropdown height (approximate)
    const double dropdownHeight = 120.0; // Approximate height for dropdown content

    return OverlayEntry(
      builder: (context) => GestureDetector(
        onTap: _closeDropdown, // Close dropdown when tapping outside
        behavior: HitTestBehavior.translucent,
        child: Container(
          width: MediaQuery.of(context).size.width,
          height: MediaQuery.of(context).size.height,
          color: Colors.transparent,
          child: Stack(
            children: [
              Positioned(
                left: offset.dx - 200, // Move dropdown 200px to the left to align under text
                top: widget.dropdownAbove 
                    ? offset.dy - dropdownHeight + 10 // Position above the button overlapping slightly
                    : offset.dy + size.height + 6, // Position below the button with 6px gap
                width: 320,
                child: GestureDetector(
                  onTap: () {}, // Prevent tap from bubbling up to close the dropdown
                  child: Material(
                    color: Colors.transparent,
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(7),
                        border: Border.all(color: Colors.grey.shade400),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(6),
                        child: Material(
                          color: Colors.white,
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              // Model options
                              ...widget.models.map((model) {
                                return InkWell(
                                  onTap: () {
                                    widget.onModelSelected(model);
                                    _closeDropdown();
                                  },
                                  child: Container(
                                    width: double.infinity,
                                    padding: EdgeInsets.symmetric(horizontal: 16, vertical: 9),
                                    child: Row(
                                      children: [
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              Row(
                                                children: [
                                                  Text(
                                                    model.name,
                                                    style: TextStyle(
                                                      fontSize: 14,
                                                      fontFamily: 'TimposText',
                                                      fontWeight: FontWeight.w500,
                                                      color: Colors.black,
                                                    ),
                                                  ),
                                                  if (model.isPro) ...[
                                                    SizedBox(width: 8),
                                                    Container(
                                                      padding: EdgeInsets.symmetric(
                                                        horizontal: 6,
                                                        vertical: 2,
                                                      ),
                                                      decoration: BoxDecoration(
                                                        color: Colors.orange.shade100,
                                                        borderRadius: BorderRadius.circular(4),
                                                      ),
                                                      child: Text(
                                                        'PRO',
                                                        style: TextStyle(
                                                          fontSize: 10,
                                                          fontFamily: 'TimposText',
                                                          fontWeight: FontWeight.bold,
                                                          color: Colors.orange.shade800,
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ],
                                              ),
                                              SizedBox(height: 2),
                                              Text(
                                                model.description,
                                                style: TextStyle(
                                                  fontSize: 12,
                                                  fontFamily: 'TimposText',
                                                  color: Colors.grey.shade600,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        if (model.id == widget.selectedModel.id)
                                          Icon(
                                            Icons.check,
                                            color: Colors.grey.shade600,
                                            size: 16,
                                          ),
                                      ],
                                    ),
                                  ),
                                );
                              }),
                              // More models option
                              InkWell(
                                onTap: () {
                                  // Handle "More models" action
                                  _closeDropdown();
                                },
                                child: Container(
                                  width: double.infinity,
                                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                                  child: Row(
                                    children: [
                                      Text(
                                        'More models',
                                        style: TextStyle(
                                          fontSize: 14,
                                          fontFamily: 'TimposText',
                                          color: Colors.black,
                                        ),
                                      ),
                                      Spacer(),
                                      Icon(
                                        Icons.arrow_forward_ios,
                                        size: 14,
                                        color: Colors.grey.shade600,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: MouseRegion(
        onEnter: (_) => setState(() => isHovered = true),
        onExit: (_) => setState(() => isHovered = false),
        child: GestureDetector(
          onTap: _toggleDropdown,
          child: AnimatedContainer(
            duration: Duration(milliseconds: 200),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: isHovered || isDropdownOpen
                    ? Colors.grey.shade400
                    : Colors.transparent,
                width: 1,
              ),
              boxShadow: isHovered || isDropdownOpen
                  ? [
                      // BoxShadow(
                      //   color: Colors.black.withValues(alpha: 0.1),
                      //   blurRadius: 4,
                      //   offset: Offset(0, 2),
                      // ),
                    ]
                  : [],
            ),
            padding: EdgeInsets.symmetric(horizontal: 14, vertical: 8),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  widget.selectedModel.name,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade700,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(width: 4),
                Icon(
                  Icons.keyboard_arrow_down,
                  size: 16,
                  color: Colors.grey.shade600,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
