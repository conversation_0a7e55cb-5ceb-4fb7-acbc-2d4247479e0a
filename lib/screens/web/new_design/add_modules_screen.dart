import 'package:flutter/material.dart';

class AddModulesScreen extends StatelessWidget {
  final List<String> solutionOptions;
  const AddModulesScreen({super.key, required this.solutionOptions});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Modules'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Row(
        children: [
          // Left panel - Solutions list
          Container(
            width: 205,
            decoration: BoxDecoration(
              border: Border(
                right: BorderSide(color: Colors.grey.shade300),
              ),
            ),
            child: ListView.builder(
              itemCount: solutionOptions.length,
              itemBuilder: (context, index) {
                return Container(
                  padding:
                      const EdgeInsets.symmetric(vertical: 6, horizontal: 10),
                  child: Text(
                    solutionOptions[index],
                    style: const TextStyle(fontSize: 14, color: Colors.black),
                  ),
                );
              },
            ),
          ),
          // Middle panel - CRM
          Container(
            width: 255,
            decoration: BoxDecoration(
              border: Border(
                right: BorderSide(color: Colors.grey.shade300),
              ),
            ),
            child: Column(
              children: [
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                  decoration: BoxDecoration(
                    border: Border(
                      bottom: BorderSide(color: Colors.grey.shade300),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: const [
                      Text('CRM',
                          style: TextStyle(fontWeight: FontWeight.bold)),
                      Icon(Icons.add, size: 18),
                    ],
                  ),
                ),
                // Add CRM content here
              ],
            ),
          ),
          // Right panel - Sales
          Container(
            width: 255,
            decoration: BoxDecoration(
              border: Border(
                right: BorderSide(color: Colors.grey.shade300),
              ),
            ),
            child: Column(
              children: [
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                  decoration: BoxDecoration(
                    border: Border(
                      bottom: BorderSide(color: Colors.grey.shade300),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: const [
                      Text('Sales',
                          style: TextStyle(fontWeight: FontWeight.bold)),
                      Icon(Icons.add, size: 18),
                    ],
                  ),
                ),
                // Add Sales content here
              ],
            ),
          ),
          // Empty panel on the right
          Expanded(
            child: Container(color: Colors.white),
          ),
        ],
      ),
    );
  }
}
