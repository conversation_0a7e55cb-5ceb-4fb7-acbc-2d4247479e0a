import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/providers/web_book_solution_provider.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/chat_field.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:provider/provider.dart';

class WebBookSolutionPage extends StatelessWidget {
  final Map<String, dynamic>? initialData;

  const WebBookSolutionPage({super.key, this.initialData});

  @override
  Widget build(BuildContext context) {
    return Consumer<WebBookSolutionProvider>(
      builder: (context, provider, child) {
        return Scaffold(
          backgroundColor: const Color(0xFFF5F7FA),
          body: Row(
            children: [
              Expanded(child: SizedBox()),
              Expanded(
                flex: 9,
                child: Column(
                  children: [
                    // Header
                    _buildHeader(context, provider),

                    // Content
                    Expanded(
                      child: Padding(
                        padding:
                            const EdgeInsets.symmetric(vertical: AppSpacing.lg),
                        child: provider.showAddModules
                            ? _buildAddModulesContent(context, provider)
                            : _buildSolutionsList(provider),
                      ),
                    ),
                    ChatField(
                      isGeneralLoading: false,
                      isFileLoading: false,
                      isSpeechLoading: false,
                      onSendMessage: () {},
                      onFileSelected: (fileName, filePath) {},
                      onToggleRecording: () {},
                      controller: provider.chatController,
                    )
                  ],
                ),
              ),
              Expanded(child: SizedBox()),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context, WebBookSolutionProvider provider) {
    return Container(
      height: AppSpacing.xxl,
      padding: const EdgeInsets.only(
          left: 0), // Remove right padding to allow arrow to go to edge
      decoration: const BoxDecoration(
        color: Colors.transparent, // Ensure no background color
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Left section
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              MouseRegion(
                cursor: SystemMouseCursors.click,
                child: InkWell(
                  borderRadius: BorderRadius.circular(4),
                  onTap: () {
                    Provider.of<WebHomeProvider>(context, listen: false)
                        .currentScreenIndex = ScreenConstants.webMyLibrary;
                  },
                  child: Padding(
                    padding: const EdgeInsets.all(4.0),
                    child: Icon(
                      Icons.arrow_back,
                      color: Colors.grey.shade600,
                      size: AppSpacing.lg,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              const Text(
                'Book Name',
                style: TextStyle(
                  fontSize: AppSpacing.size14,
                  fontWeight: FontWeight.w500,
                  color: Colors.black,
                  fontFamily: 'TiemposText',
                ),
              ),
            ],
          ),
          const SizedBox(width: AppSpacing.xs), // Add Modules section
          MouseRegion(
            cursor: SystemMouseCursors.click,
            onEnter: (_) {
              provider.isAddModulesHeaderButtonHovering = true;
            },
            onExit: (_) {
              provider.isAddModulesHeaderButtonHovering = false;
            },
            child: GestureDetector(
              onTap: provider.toggleAddModules,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                  color: Colors.transparent,
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SvgPicture.asset(
                      'assets/images/add-module.svg',
                      width: AppSpacing.size14,
                      height: AppSpacing.size14,
                      colorFilter: ColorFilter.mode(
                        Colors.grey.shade600,
                        BlendMode.srcIn,
                      ),
                    ),
                    const SizedBox(width: 6),
                    Text(
                      'Add Modules',
                      style: TextStyle(
                        fontSize: AppSpacing.size14,
                        color: Colors.black,
                        fontFamily: 'TiemposText',
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: AppSpacing.xxs),
                    Icon(
                      Icons.arrow_drop_down,
                      size: AppSpacing.lg,
                      color: provider.isAddModulesHeaderButtonHovering ||
                              provider.showAddModules
                          ? const Color(0xff0058FF)
                          : Colors.grey.shade600,
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Spacer to push count items to center
          Expanded(flex: 1, child: SizedBox()),

          // Center section with count items grouped together
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildCountItem('assets/images/agent.svg', '3 Agent (V001)'),
              const SizedBox(width: AppSpacing.lg),
              _buildCountItem(
                  'assets/images/cube-box.svg', '12 Objects (V001)'),
              const SizedBox(width: AppSpacing.lg),
              _buildCountItem(
                  'assets/images/square-box-uncheck.svg', '15 Solutions'),
            ],
          ), // Right section - expand button against the right edge
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                MouseRegion(
                  cursor: SystemMouseCursors.click,
                  child: SvgPicture.asset(
                    'assets/images/expand-arrow-left-new.svg',
                    width: AppSpacing.md,
                    height: AppSpacing.md,
                    colorFilter: ColorFilter.mode(
                      Colors.grey.shade600,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCountItem(String iconPath, String text) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
          color: Colors.transparent,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SvgPicture.asset(
              iconPath,
              width: AppSpacing.size14,
              height: AppSpacing.size14,
              colorFilter: ColorFilter.mode(
                Colors.black,
                BlendMode.srcIn,
              ),
            ),
            const SizedBox(width: 6),
            Text(
              text,
              style: const TextStyle(
                fontSize: AppSpacing.size14,
                fontFamily: 'TiemposText',
                color: Colors.black,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSolutionsList(WebBookSolutionProvider provider) {
    return ListView.builder(
      itemCount: provider.solutionItems.length,
      itemBuilder: (context, index) {
        final item = provider.solutionItems[index];
        return _buildSolutionItem(item);
      },
    );
  }

  Widget _buildSolutionItem(SolutionItem item) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: Container(
        margin: const EdgeInsets.only(
          bottom: 16,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Color(0xffD0D0D0)),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Left content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item.title,
                      style: const TextStyle(
                        fontSize: AppSpacing.size14,
                        color: Color(0xff000000),
                        fontWeight: FontWeight.w600,
                        fontFamily: 'TiemposText',
                      ),
                    ),
                    const SizedBox(height: AppSpacing.xxs),
                    Text(
                      'Last Message: ${item.lastMessageTime}',
                      style: TextStyle(
                        fontSize: AppSpacing.sm,
                        fontFamily: 'TiemposText',
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),

              // Right content
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Row(
                    children: [
                      SvgPicture.asset(
                        'assets/images/folder.svg',
                        width: AppSpacing.md,
                        height: AppSpacing.md,
                        colorFilter: ColorFilter.mode(
                          Colors.grey,
                          BlendMode.srcIn,
                        ),
                      ),
                      const SizedBox(width: AppSpacing.xxs),
                      Text(
                        item.versionId,
                        style: TextStyle(
                          fontSize: AppSpacing.sm,
                          fontFamily: 'TiemposText',
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppSpacing.xxs),
                  Text(
                    item.date,
                    style: TextStyle(
                      fontSize: AppSpacing.sm,
                      fontFamily: 'TiemposText',
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAddModulesContent(
      BuildContext context, WebBookSolutionProvider provider) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Stack(
        children: [
          Column(
            children: [
              // Header row
              Container(
                height: AppSpacing.xxl,
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(color: Colors.grey.shade300),
                  ),
                ),
                child: Row(
                  children: [
                    Flexible(
                      flex: 1,
                      child: Container(
                        constraints:
                            BoxConstraints(minWidth: AppSpacing.width180),
                        alignment: Alignment.centerLeft,
                        padding: EdgeInsets.only(left: 10),
                        decoration: BoxDecoration(
                          border: Border(
                            right: BorderSide(color: Colors.grey.shade300),
                          ),
                        ),
                        child: Text(
                          'Drag Your Solutions',
                          style: TextStyle(
                            fontSize: AppSpacing.size14,
                            fontWeight: FontWeight.w600,
                            color: Colors.black,
                          ),
                        ),
                      ),
                    ),
                    Flexible(
                      flex: 1,
                      child: Container(
                        constraints:
                            BoxConstraints(minWidth: AppSpacing.width180),
                        alignment: Alignment.center,
                        padding: EdgeInsets.only(left: 0),
                        decoration: BoxDecoration(
                          border: Border(
                            right: BorderSide(color: Colors.grey.shade300),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              'Modules',
                              style: TextStyle(
                                fontSize: AppSpacing.sm,
                                fontWeight: FontWeight.w600,
                                color: Colors.black,
                              ),
                            ),
                            SizedBox(width: AppSpacing.sm),
                            CompositedTransformTarget(
                              link: provider.modulesButtonLink,
                              child: MouseRegion(
                                cursor: SystemMouseCursors.click,
                                child: Container(
                                  key: provider.modulesButtonKey,
                                  margin: EdgeInsets.symmetric(horizontal: 0),
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 12, vertical: 6),
                                  decoration: BoxDecoration(
                                    border:
                                        Border.all(color: Colors.grey.shade900),
                                    borderRadius: BorderRadius.circular(4),
                                    color: Colors.white,
                                  ),
                                  child: GestureDetector(
                                    onTap: () {
                                      if (provider.showModulesPopup) {
                                        provider.hideModulesPopupMenu();
                                      } else {
                                        provider.showModulesPopupMenu(context);
                                      }
                                    },
                                    child: Row(
                                      children: [
                                        Icon(Icons.add,
                                            size: AppSpacing.md,
                                            color: Colors.grey.shade700),
                                        SizedBox(width: AppSpacing.xxs),
                                        Text('Modules',
                                            style: TextStyle(
                                                fontSize: AppSpacing.sm)),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Flexible(
                      flex: 1,
                      child: Container(
                        constraints:
                            BoxConstraints(minWidth: AppSpacing.width180),
                        decoration: BoxDecoration(
                          border: Border(
                            right: BorderSide(color: Colors.grey.shade300),
                          ),
                        ),
                      ),
                    ),
                    Expanded(child: Container()),
                  ],
                ),
              ),
              // Content row
              Expanded(
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Left panel - Solutions list
                    Flexible(
                      flex: 1,
                      child: Container(
                        constraints:
                            BoxConstraints(minWidth: AppSpacing.width180),
                        decoration: BoxDecoration(
                          border: Border(
                            right: BorderSide(color: Colors.grey.shade300),
                          ),
                        ),
                        child: Stack(
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(bottom: 40),
                              child: ListView.builder(
                                itemCount: provider.solutionOptions.length,
                                itemBuilder: (context, index) {
                                  return MouseRegion(
                                    cursor: SystemMouseCursors.click,
                                    child: Container(
                                      padding: EdgeInsets.symmetric(
                                          vertical: 6, horizontal: 10),
                                      child: Text(
                                        'Solutions-${(index + 1).toString().padLeft(2, '0')}',
                                        style: TextStyle(
                                            fontSize: AppSpacing.size14,
                                            color: Colors.black),
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                            Positioned(
                              bottom: 0,
                              left: 0,
                              right: 0,
                              child: Container(
                                height: AppSpacing.size40,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  border: Border(
                                    top:
                                        BorderSide(color: Colors.grey.shade300),
                                  ),
                                ),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.end, // Move to right
                                  children: [
                                    // Left chevron (no hover effect)
                                    _PaginationChevronButton(isLeft: true),
                                    SizedBox(width: AppSpacing.xs),
                                    // Right chevron with hover effect
                                    _PaginationChevronButton(isLeft: false),
                                    SizedBox(width: AppSpacing.xl),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    // Middle panel - Modules
                    Flexible(
                      flex: 1,
                      child: Container(
                        key: provider.modulesColumnKey,
                        constraints:
                            BoxConstraints(minWidth: AppSpacing.width180),
                        decoration: BoxDecoration(
                          border: Border(
                            right: BorderSide(color: Colors.grey.shade300),
                          ),
                        ),
                        child: _buildModulesSection(provider),
                      ),
                    ),
                    // Right panel - empty
                    Flexible(
                      flex: 1,
                      child: Container(
                        constraints:
                            BoxConstraints(minWidth: AppSpacing.width180),
                        decoration: BoxDecoration(
                          border: Border(
                            right: BorderSide(color: Colors.grey.shade300),
                          ),
                        ),
                      ),
                    ),
                    // Empty panel on the right
                    Expanded(
                      child: Container(color: Colors.white),
                    ),
                  ],
                ),
              ),
            ],
          ),
          // Cross button in top right
          Positioned(
            top: 8,
            right: 8,
            child: MouseRegion(
              cursor: SystemMouseCursors.click,
              child: GestureDetector(
                onTap: provider.toggleAddModules,
                child: Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.grey.shade200,
                  ),
                  padding: EdgeInsets.all(4),
                  child:
                      Icon(Icons.close, size: 20, color: Colors.grey.shade700),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModulesSection(WebBookSolutionProvider provider) {
    if (provider.modules.isEmpty) {
      return Container(); // Return empty container when no modules exist
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        for (int i = 0; i < provider.modules.length; i++)
          _buildModuleItem(provider.modules[i], i, provider),
      ],
    );
  }

  Widget _buildModuleItem(
      ModuleItem module, int index, WebBookSolutionProvider provider) {
    return Container(
      color: Color(0xFFF8F9FA),
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          // Static dropdown arrow for visual consistency
          Icon(Icons.keyboard_arrow_down, size: AppSpacing.size18),
          SizedBox(width: 8),
          Expanded(
            child: Text(
              module.name,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: AppSpacing.md,
              ),
            ),
          ),
          MouseRegion(
            cursor: SystemMouseCursors.click,
            child: GestureDetector(
              key: provider.submoduleButtonKeys[index],
              onTap: () {
                provider.setActiveModulePopupIndex(index);
                provider.showSubmodulePopup(index);
              },
              child: Icon(Icons.add, size: AppSpacing.size18),
            ),
          ),
        ],
      ),
    );
  }
}

class _PaginationChevronButton extends StatelessWidget {
  final bool isLeft;
  const _PaginationChevronButton({this.isLeft = false});

  @override
  Widget build(BuildContext context) {
    return Consumer<WebBookSolutionProvider>(
      builder: (context, provider, child) {
        final isHovering = isLeft
            ? provider.isPaginationLeftChevronHovering
            : provider.isPaginationRightChevronHovering;

        return MouseRegion(
          cursor: SystemMouseCursors.click,
          onEnter: (_) {
            if (isLeft) {
              provider.isPaginationLeftChevronHovering = true;
            } else {
              provider.isPaginationRightChevronHovering = true;
            }
          },
          onExit: (_) {
            if (isLeft) {
              provider.isPaginationLeftChevronHovering = false;
            } else {
              provider.isPaginationRightChevronHovering = false;
            }
          },
          child: GestureDetector(
            child: AnimatedContainer(
              duration: Duration(milliseconds: 120),
              decoration: BoxDecoration(
                color: isHovering ? Color(0xFFF5F7FA) : Colors.transparent,
                border: isHovering
                    ? Border.all(color: Color(0xFFBDBDBD))
                    : Border.all(color: Colors.transparent),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Icon(
                isLeft ? Icons.chevron_left : Icons.chevron_right,
                size: 22,
                color: Colors.grey.shade600,
              ),
            ),
          ),
        );
      },
    );
  }
}

class _HoverIconButton extends StatefulWidget {
  final IconData icon;
  final Color color;
  final double size;
  final Color? hoverColor;
  final Border? hoverBorder;
  final VoidCallback onTap;

  const _HoverIconButton({
    required this.icon,
    required this.color,
    required this.size,
    this.hoverColor,
    this.hoverBorder,
    required this.onTap,
    Key? key,
  }) : super(key: key);

  @override
  State<_HoverIconButton> createState() => _HoverIconButtonState();
}

class _HoverIconButtonState extends State<_HoverIconButton> {
  bool _hovering = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      onEnter: (_) => setState(() => _hovering = true),
      onExit: (_) => setState(() => _hovering = false),
      child: GestureDetector(
        onTap: widget.onTap,
        child: AnimatedContainer(
          duration: Duration(milliseconds: 120),
          decoration: BoxDecoration(
            color: _hovering ? Color(0xFFF5F7FA) : Colors.transparent,
            border: _hovering
                ? Border.all(color: Color(0xFFBDBDBD))
                : Border.all(color: Colors.transparent),
            borderRadius: BorderRadius.circular(4),
          ),
          padding: const EdgeInsets.all(4.0),
          child: Icon(
            widget.icon,
            size: AppSpacing.md,
            color: widget.color,
          ),
        ),
      ),
    );
  }
}
