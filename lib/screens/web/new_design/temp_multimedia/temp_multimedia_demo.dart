import 'package:flutter/material.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:provider/provider.dart';
import 'temp_web_chat.dart';

/// Demo screen to showcase the multimedia functionality
class TempMultimediaDemo extends StatelessWidget {
  const TempMultimediaDemo({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => WebHomeProvider(),
      child: Scaffold(
        appBar: AppBar(
          title: Text('Multimedia Chat Demo'),
          backgroundColor: Colors.blue.shade600,
          foregroundColor: Colors.white,
          elevation: 2,
        ),
        body: TempWebChat(),
      ),
    );
  }
}
