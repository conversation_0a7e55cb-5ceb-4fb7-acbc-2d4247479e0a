// Web-specific implementation for audio recording widget
import 'dart:html' as html;

/// Web-specific download functionality for audio files
void downloadAudioFile(String audioPath) {
  // Generate a filename with timestamp
  final fileName = 
      'audio_recording_${DateTime.now().millisecondsSinceEpoch}.wav';

  // Create an anchor element for download
  final anchor = html.AnchorElement(href: audioPath);
  anchor.download = fileName;
  anchor.style.display = 'none';

  // Add to DOM, click, and remove
  html.document.body?.children.add(anchor);
  anchor.click();
  html.document.body?.children.remove(anchor);
}

/// Web-specific window operations for opening files in new tabs
void openInNewTab(String url) {
  html.window.open(url, '_blank');
}
