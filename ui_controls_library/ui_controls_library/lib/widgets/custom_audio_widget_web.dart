// Web-specific implementation for custom audio widget
import 'dart:html' as html;

/// Web-specific download functionality for audio files from URLs
void downloadAudioFileFromUrl(String url) {
  // Create an anchor element for download
  final anchor = html.AnchorElement(href: url);

  // Extract filename from URL or use default
  final uri = Uri.parse(url);
  final pathSegments = uri.pathSegments;
  final filename = pathSegments.isNotEmpty && pathSegments.last.isNotEmpty
      ? pathSegments.last
      : 'audio_${DateTime.now().millisecondsSinceEpoch}.mp3';

  anchor.download = filename;
  anchor.style.display = 'none';

  // Add to DOM, click, and remove
  html.document.body?.children.add(anchor);
  anchor.click();
  html.document.body?.children.remove(anchor);
}

/// Web-specific window operations for opening URLs in new tabs
void openUrlInNewTab(String url) {
  html.window.open(url, '_blank');
}
