{"solution": {"id": "c4579b89-6082-4826-855b-282d938c43e7", "title": "Loan Management System", "nlp": "Loan has loanId^PK, customerId^FK, loanAmount, interestRate, term, startDate, endDate, status (Active, Closed, Default, Restructured), paymentFrequency (Monthly, Biweekly, Weekly, Quarterly, Annually), totalPaymentsMade, remainingBalance[derived], loanType (Personal, Mortgage, Auto, Education, Business), collateralId^FK, originationFee, lateFeePercentage, earlyPaymentPenalty.\n\nRelationships for Loan:\n<PERSON>an has many-to-one relationship with Customer using Loan.customerId to Customer.customerId^PK\nLoan has many-to-one relationship with Collateral using Loan.collateralId to Collateral.collateralId^PK\nLoan has one-to-many relationship with Payment using Loan.loanId to Payment.loanId^FK\n\nValidations for Loan:\nLoan.loanId must be unique\nLoan.loanAmount must be greater than 0\nLoan.interestRate must be greater than or equal to 1.0\nLoan.term must be greater than or equal to 3\nLoan.startDate must not be in the future\nLoan.endDate must be after startDate\n\nAttribute Additional Properties: Attribute name: loanId Key: Primary Data Type: Integer Type: Mandatory Format: \"LN-######\" Values: N/A Default: N/A Validation: Auto-increment Error Message: \"Invalid Loan ID format\" Description: Unique identifier for the loan\n\nAttribute Additional Properties: Attribute name: customerId Key: Foreign Data Type: Integer Type: Mandatory Format: \"CUS-######\" Values: N/A Default: N/A Validation: Foreign Key Check Error Message: \"Customer ID must reference an existing customer\" Description: References the customer who took the loan\n\nAttribute Additional Properties: Attribute name: loanAmount Key: Non-unique Data Type: Decimal Type: Mandatory Format: \"$#,###.00\" Values: 1000.00-********.00 Default: N/A Validation: Range Check Error Message: \"Loan amount must be between $1,000 and $10,000,000\" Description: The principal amount borrowed by the customer\n\nAttribute Additional Properties: Attribute name: interestRate Key: Non-unique Data Type: Decimal Type: Mandatory Format: \"0.00%\" Values: 1.00-30.00 Default: \"5.99\" Validation: Range Check Error Message: \"Interest rate must be between 1.00% and 30.00%\" Description: Annual interest rate applied to the loan\n\nAttribute Additional Properties: Attribute name: term Key: Non-unique Data Type: Integer Type: Mandatory Format: \"### months\" Values: 3-480 Default: \"60\" Validation: Range Check Error Message: \"Term must be between 3 and 480 months\" Description: Duration of the loan in months\n\nAttribute Additional Properties: Attribute name: startDate Key: Non-unique Data Type: Date Type: Mandatory Format: \"YYYY-MM-DD\" Values: N/A Default: \"CURRENT_DATE\" Validation: Date Check Error Message: \"Start date cannot be in the future\" Description: Date when the loan becomes active\n\nAttribute Additional Properties: Attribute name: endDate Key: Non-unique Data Type: Date Type: Mandatory Format: \"YYYY-MM-DD\" Values: N/A Default: N/A Validation: Date Check Error Message: \"End date must be after start date\" Description: Scheduled date for loan completion\n\nAttribute Additional Properties: Attribute name: status Key: Non-unique Data Type: Enum Type: Mandatory Format: N/A Values: \"Active\", \"Closed\", \"Default\", \"Restructured\" Default: \"Active\" Validation: List Check Error Message: \"Invalid loan status\" Description: Current status of the loan\n\nAttribute Additional Properties: Attribute name: paymentFrequency Key: Non-unique Data Type: Enum Type: Mandatory Format: N/A Values: \"Monthly\", \"Biweekly\", \"Weekly\", \"Quarterly\", \"Annually\" Default: \"Monthly\" Validation: List Check Error Message: \"Invalid payment frequency\" Description: How often payments are made on the loan\n\nAttribute Additional Properties: Attribute name: totalPaymentsMade Key: Non-unique Data Type: Integer Type: Mandatory Format: \"###\" Values: 0-N Default: \"0\" Validation: Range Check Error Message: \"Total payments cannot be negative\" Description: Number of payments completed for this loan\n\nAttribute Additional Properties: Attribute name: remainingBalance Key: Non-unique Data Type: Decimal Type: Calculated Format: \"$#,###.00\" Values: 0.00-N Default: N/A Validation: Calculation Error Message: N/A Description: Current outstanding balance on the loan.\n\nAttribute Additional Properties: Attribute name: loanType Key: Non-unique Data Type: Enum Type: Mandatory Format: N/A Values: \"Personal\", \"Mortgage\", \"Auto\", \"Education\", \"Business\" Default: \"Personal\" Validation: List Check Error Message: \"Invalid loan type\" Description: Category of loan based on purpose\n\nAttribute Additional Properties: Attribute name: collateralId Key: Foreign Data Type: Integer Type: Optional Format: \"COL-######\" Values: N/A Default: N/A Validation: Foreign Key Check Error Message: \"Collateral ID must reference existing collateral\" Description: References the asset used to secure the loan\n\nAttribute Additional Properties: Attribute name: originationFee Key: Non-unique Data Type: Decimal Type: Optional Format: \"$#,###.00\" Values: 0.00-N Default: \"0.00\" Validation: Range Check Error Message: \"Origination fee cannot be negative\" Description: Fee charged for processing a new loan application\n\nAttribute Additional Properties: Attribute name: lateFeePercentage Key: Non-unique Data Type: Decimal Type: Optional Format: \"0.00%\" Values: 0.00-20.00 Default: \"5.00\" Validation: Range Check Error Message: \"Late fee percentage must be between 0% and 20%\" Description: Percentage charged on late payments\n\nAttribute Additional Properties: Attribute name: earlyPaymentPenalty Key: Non-unique Data Type: Decimal Type: Optional Format: \"0.00%\" Values: 0.00-10.00 Default: \"1.00\" Validation: Range Check Error Message: \"Early payment penalty must be between 0% and 10%\" Description: Penalty charged for early loan payoff\n\nRelationship: Loan to Customer\n\nRelationship Properties: On Delete: Restrict (Prevent deletion of customer with active loans) On Update: Cascade (Update loan records when customer details change) Foreign Key Type: Non-Nullable\n\nRelationship: Loan to Collateral\n\nRelationship Properties: On Delete: Set Null (Remove loan reference when collateral is deleted) On Update: Cascade (Update loan records when collateral details change) Foreign Key Type: Nullable\n\n\nCustomer has customerId^PK, firstName, lastName, email, phone, address, city, state, zipCode, country, dateOfBirth, ssn, creditScore, annualIncome, employmentStatus (Employed, Self-Employed, Unemployed, Retired), employerName, employmentLength, customerType (Individual, Business), status (Active, Inactive, Suspended).\n\nRelationships for Customer:\nCustomer has one-to-many relationship with Loan using Customer.customerId to Loan.customerId^FK\nCustomer has one-to-many relationship with Collateral using Customer.customerId to Collateral.customerId^FK\n\nValidations for Customer:\nCustomer.customerId must be unique\nCustomer.email must be a valid email format\nCustomer.ssn must be a valid format and unique\nCustomer.creditScore must be between 300 and 850\nCustomer.annualIncome must be greater than 0\nCustomer.dateOfBirth must indicate customer is at least 18 years old\n\nAttribute Additional Properties: Attribute name: customerId Key: Primary Data Type: Integer Type: Mandatory Format: \"CUS-######\" Values: N/A Default: N/A Validation: Auto-increment Error Message: \"Invalid Customer ID format\" Description: Unique identifier for the customer\n\nAttribute Additional Properties: Attribute name: firstName Key: Non-unique Data Type: String Type: Mandatory Format: N/A Values: N/A Default: N/A Validation: Length Check Error Message: \"First name is required\" Description: Customer's first name\n\nAttribute Additional Properties: Attribute name: lastName Key: Non-unique Data Type: String Type: Mandatory Format: N/A Values: N/A Default: N/A Validation: Length Check Error Message: \"Last name is required\" Description: Customer's last name\n\nAttribute Additional Properties: Attribute name: email Key: Non-unique Data Type: String Type: Mandatory Format: \"<EMAIL>\" Values: N/A Default: N/A Validation: Format Check Error Message: \"Invalid email format\" Description: Customer's email address\n\nAttribute Additional Properties: Attribute name: phone Key: Non-unique Data Type: String Type: Mandatory Format: \"+#-###-###-####\" Values: N/A Default: N/A Validation: Format Check Error Message: \"Invalid phone number format\" Description: Customer's contact phone number\n\nAttribute Additional Properties: Attribute name: address Key: Non-unique Data Type: String Type: Mandatory Format: N/A Values: N/A Default: N/A Validation: Length Check Error Message: \"Address is required\" Description: Customer's street address\n\nAttribute Additional Properties: Attribute name: city Key: Non-unique Data Type: String Type: Mandatory Format: N/A Values: N/A Default: N/A Validation: Length Check Error Message: \"City is required\" Description: Customer's city of residence\n\nAttribute Additional Properties: Attribute name: state Key: Non-unique Data Type: String Type: Mandatory Format: N/A Values: N/A Default: N/A Validation: Length Check Error Message: \"State is required\" Description: Customer's state of residence\n\nAttribute Additional Properties: Attribute name: zipCode Key: Non-unique Data Type: String Type: Mandatory Format: \"#####\" or \"#####-####\" Values: N/A Default: N/A Validation: Format Check Error Message: \"Invalid zip code format\" Description: Customer's zip code\n\nAttribute Additional Properties: Attribute name: country Key: Non-unique Data Type: String Type: Mandatory Format: N/A Values: N/A Default: \"USA\" Validation: Length Check Error Message: \"Country is required\" Description: Customer's country of residence\n\nAttribute Additional Properties: Attribute name: dateOfBirth Key: Non-unique Data Type: Date Type: Mandatory Format: \"YYYY-MM-DD\" Values: N/A Default: N/A Validation: Age Check Error Message: \"Customer must be at least 18 years old\" Description: Customer's date of birth\n\nAttribute Additional Properties: Attribute name: ssn Key: Non-unique Data Type: String Type: Mandatory Format: \"###-##-####\" Values: N/A Default: N/A Validation: Format Check Error Message: \"Invalid SSN format\" Description: Customer's Social Security Number\n\nAttribute Additional Properties: Attribute name: creditScore Key: Non-unique Data Type: Integer Type: Mandatory Format: \"###\" Values: 300-850 Default: N/A Validation: Range Check Error Message: \"Credit score must be between 300 and 850\" Description: Customer's credit score\n\nAttribute Additional Properties: Attribute name: annualIncome Key: Non-unique Data Type: Decimal Type: Mandatory Format: \"$#,###.00\" Values: 0.00-N Default: N/A Validation: Range Check Error Message: \"Annual income must be greater than 0\" Description: Customer's annual income\n\nAttribute Additional Properties: Attribute name: employmentStatus Key: Non-unique Data Type: Enum Type: Mandatory Format: N/A Values: \"Employed\", \"Self-Employed\", \"Unemployed\", \"Retired\" Default: \"Employed\" Validation: List Check Error Message: \"Invalid employment status\" Description: Customer's current employment status\n\nAttribute Additional Properties: Attribute name: employerName Key: Non-unique Data Type: String Type: Optional Format: N/A Values: N/A Default: N/A Validation: Length Check Error Message: N/A Description: Name of customer's employer\n\nAttribute Additional Properties: Attribute name: employmentLength Key: Non-unique Data Type: Integer Type: Optional Format: \"## years\" Values: 0-N Default: \"0\" Validation: Range Check Error Message: \"Employment length cannot be negative\" Description: Length of employment in years\n\nAttribute Additional Properties: Attribute name: customerType Key: Non-unique Data Type: Enum Type: Mandatory Format: N/A Values: \"Individual\", \"Business\" Default: \"Individual\" Validation: List Check Error Message: \"Invalid customer type\" Description: Type of customer\n\nAttribute Additional Properties: Attribute name: status Key: Non-unique Data Type: Enum Type: Mandatory Format: N/A Values: \"Active\", \"Inactive\", \"Suspended\" Default: \"Active\" Validation: List Check Error Message: \"Invalid status\" Description: Current status of the customer account\n\nRelationship: Customer to Loan\n\nRelationship Properties: On Delete: Restrict (Prevent deletion of customer with active loans) On Update: Cascade (Update loan records when customer details change) Foreign Key Type: Non-Nullable\n\n\nCollateral has collateralId^PK, customerId^FK, loanId^FK, collateralType (RealEstate, Vehicle, Investment, Cash, Other), description, value, appraisalDate, appraisalValue, lienStatus (FirstLien, SecondLien, NoLien), condition (Excellent, Good, Fair, Poor), assetAddress, assetCity, assetState, assetZipCode, insuranceProvider, insurancePolicyNumber, insuranceExpirationDate, documentationComplete (true, false).\n\nRelationships for Collateral:\nCollateral has many-to-one relationship with Customer using Collateral.customerId to Customer.customerId^PK\nCollateral has many-to-one relationship with Loan using Collateral.loanId to Loan.loanId^PK\n\nValidations for Collateral:\nCollateral.collateralId must be unique\nCollateral.customerId must exist in Customer table\nCollateral.loanId must exist in Loan table\nCollateral.value must be greater than 0\nCollateral.appraisalValue must be greater than 0\nCollateral.customerId must match the Loan.customerId of the associated loan\n\nAttribute Additional Properties: Attribute name: collateralId Key: Primary Data Type: Integer Type: Mandatory Format: \"COL-######\" Values: N/A Default: N/A Validation: Auto-increment Error Message: \"Invalid Collateral ID format\" Description: Unique identifier for the collateral\n\nAttribute Additional Properties: Attribute name: customerId Key: Foreign Data Type: Integer Type: Mandatory Format: \"CUS-######\" Values: N/A Default: N/A Validation: Foreign Key Check Error Message: \"Customer ID must reference an existing customer\" Description: References the customer who owns the collateral\n\nAttribute Additional Properties: Attribute name: loanId Key: Foreign Data Type: Integer Type: Optional Format: \"LN-######\" Values: N/A Default: N/A Validation: Foreign Key Check Error Message: \"Loan ID must reference an existing loan\" Description: References the loan secured by this collateral\n\nAttribute Additional Properties: Attribute name: collateralType Key: Non-unique Data Type: Enum Type: Mandatory Format: N/A Values: \"RealEstate\", \"Vehicle\", \"Investment\", \"Cash\", \"Other\" Default: \"RealEstate\" Validation: List Check Error Message: \"Invalid collateral type\" Description: Type of asset used as collateral\n\nAttribute Additional Properties: Attribute name: description Key: Non-unique Data Type: String Type: Mandatory Format: N/A Values: N/A Default: N/A Validation: Length Check Error Message: \"Description is required\" Description: Detailed description of the collateral\n\nAttribute Additional Properties: Attribute name: value Key: Non-unique Data Type: Decimal Type: Mandatory Format: \"$#,###.00\" Values: 0.00-N Default: N/A Validation: Range Check Error Message: \"Value must be greater than 0\" Description: Current market value of the collateral\n\nAttribute Additional Properties: Attribute name: appraisalDate Key: Non-unique Data Type: Date Type: Mandatory Format: \"YYYY-MM-DD\" Values: N/A Default: \"CURRENT_DATE\" Validation: Date Check Error Message: \"Appraisal date is required\" Description: Date when the collateral was appraised\n\nAttribute Additional Properties: Attribute name: appraisalValue Key: Non-unique Data Type: Decimal Type: Mandatory Format: \"$#,###.00\" Values: 0.00-N Default: N/A Validation: Range Check Error Message: \"Appraisal value must be greater than 0\" Description: Value determined during appraisal\n\nAttribute Additional Properties: Attribute name: lienStatus Key: Non-unique Data Type: Enum Type: Mandatory Format: N/A Values: \"FirstLien\", \"SecondLien\", \"NoLien\" Default: \"FirstLien\" Validation: List Check Error Message: \"Invalid lien status\" Description: Status of liens against this collateral\n\nAttribute Additional Properties: Attribute name: condition Key: Non-unique Data Type: Enum Type: Mandatory Format: N/A Values: \"Excellent\", \"Good\", \"Fair\", \"Poor\" Default: \"Good\" Validation: List Check Error Message: \"Invalid condition\" Description: Physical condition of the collateral\n\nAttribute Additional Properties: Attribute name: assetAddress Key: Non-unique Data Type: String Type: Optional Format: N/A Values: N/A Default: N/A Validation: Length Check Error Message: N/A Description: Address of the physical asset\n\nAttribute Additional Properties: Attribute name: assetCity Key: Non-unique Data Type: String Type: Optional Format: N/A Values: N/A Default: N/A Validation: Length Check Error Message: N/A Description: City where the physical asset is located\n\nAttribute Additional Properties: Attribute name: assetState Key: Non-unique Data Type: String Type: Optional Format: N/A Values: N/A Default: N/A Validation: Length Check Error Message: N/A Description: State where the physical asset is located\n\nAttribute Additional Properties: Attribute name: assetZipCode Key: Non-unique Data Type: String Type: Optional Format: \"#####\" or \"#####-####\" Values: N/A Default: N/A Validation: Format Check Error Message: \"Invalid zip code format\" Description: Zip code where the physical asset is located\n\nAttribute Additional Properties: Attribute name: insuranceProvider Key: Non-unique Data Type: String Type: Optional Format: N/A Values: N/A Default: N/A Validation: Length Check Error Message: N/A Description: Insurance company covering the collateral\n\nAttribute Additional Properties: Attribute name: insurancePolicyNumber Key: Non-unique Data Type: String Type: Optional Format: N/A Values: N/A Default: N/A Validation: Length Check Error Message: N/A Description: Insurance policy number for the collateral\n\nAttribute Additional Properties: Attribute name: insuranceExpirationDate Key: Non-unique Data Type: Date Type: Optional Format: \"YYYY-MM-DD\" Values: N/A Default: N/A Validation: Date Check Error Message: \"Expiration date must be in the future\" Description: Date when the insurance policy expires\n\nAttribute Additional Properties: Attribute name: documentationComplete Key: Non-unique Data Type: Boolean Type: Mandatory Format: N/A Values: \"true\", \"false\" Default: \"false\" Validation: Boolean Check Error Message: \"Invalid documentation status\" Description: Indicates if all required documentation is complete\n\nRelationship: Collateral to Customer\n\nRelationship Properties: On Delete: Restrict (Prevent deletion of customer with active collateral) On Update: Cascade (Update collateral records when customer details change) Foreign Key Type: Non-Nullable\n\nRelationship: Collateral to Loan\n\nRelationship Properties: On Delete: Set Null (Remove loan reference when loan is deleted) On Update: Cascade (Update collateral records when loan details change) Foreign Key Type: Nullable\n\n\nPayment has paymentId^PK, loanId^FK, amount, paymentDate, dueDate, status (Pending, Completed, Failed, Cancelled), paymentMethod (CreditCard, DebitCard, BankTransfer, Cash, Check, AutoDebit), referenceNumber, lateFee, paymentType (Regular, ExtraPayment, LatePayment, InterestOnly, PrincipalOnly), notes.\n\nRelationships for Payment:\nPayment has many-to-one relationship with Loan using Payment.loanId to Loan.loanId^PK\n\nValidations for Payment:\nPayment.paymentId must be unique\nPayment.loanId must exist in Loan table\nPayment.amount must be greater than 0\nPayment.dueDate must be a valid date\nPayment.paymentDate must be a valid date\nPayment.status must be one of \"Pending\", \"Completed\", \"Failed\", \"Cancelled\"\n\nAttribute Additional Properties: Attribute name: paymentId Key: Primary Data Type: Integer Type: Mandatory Format: \"PMT-######\" Values: N/A Default: N/A Validation: Auto-increment Error Message: \"Invalid Payment ID format\" Description: Unique identifier for the payment\n\nAttribute Additional Properties: Attribute name: loanId Key: Foreign Data Type: Integer Type: Mandatory Format: \"LN-######\" Values: N/A Default: N/A Validation: Foreign Key Check Error Message: \"Loan ID must reference an existing loan\" Description: References the loan this payment applies to\n\nAttribute Additional Properties: Attribute name: amount Key: Non-unique Data Type: Decimal Type: Mandatory Format: \"$#,###.00\" Values: 0.01-N Default: N/A Validation: Range Check Error Message: \"Payment amount must be greater than 0\" Description: Amount of the payment\n\nAttribute Additional Properties: Attribute name: paymentDate Key: Non-unique Data Type: Date Type: Mandatory Format: \"YYYY-MM-DD\" Values: N/A Default: \"CURRENT_DATE\" Validation: Date Check Error Message: \"Payment date is required\" Description: Date when the payment was made\n\nAttribute Additional Properties: Attribute name: dueDate Key: Non-unique Data Type: Date Type: Mandatory Format: \"YYYY-MM-DD\" Values: N/A Default: N/A Validation: Date Check Error Message: \"Due date is required\" Description: Date when the payment is due\n\nAttribute Additional Properties: Attribute name: status Key: Non-unique Data Type: Enum Type: Mandatory Format: N/A Values: \"Pending\", \"Completed\", \"Failed\", \"Cancelled\" Default: \"Pending\" Validation: List Check Error Message: \"Invalid payment status\" Description: Current status of the payment\n\nAttribute Additional Properties: Attribute name: paymentMethod Key: Non-unique Data Type: Enum Type: Mandatory Format: N/A Values: \"CreditCard\", \"DebitCard\", \"BankTransfer\", \"Cash\", \"Check\", \"AutoDebit\" Default: \"AutoDebit\" Validation: List Check Error Message: \"Invalid payment method\" Description: Method used for payment\n\nAttribute Additional Properties: Attribute name: referenceNumber Key: Non-unique Data Type: String Type: Optional Format: N/A Values: N/A Default: N/A Validation: Length Check Error Message: N/A Description: External reference number for the payment\n\nAttribute Additional Properties: Attribute name: lateFee Key: Non-unique Data Type: Decimal Type: Optional Format: \"$#,###.00\" Values: 0.00-N Default: \"0.00\" Validation: Range Check Error Message: \"Late fee cannot be negative\" Description: Additional fee applied for late payments\n\nAttribute Additional Properties: Attribute name: paymentType Key: Non-unique Data Type: Enum Type: Mandatory Format: N/A Values: \"Regular\", \"ExtraPayment\", \"LatePayment\", \"InterestOnly\", \"PrincipalOnly\" Default: \"Regular\" Validation: List Check Error Message: \"Invalid payment type\" Description: Type of payment being made\n\nAttribute Additional Properties: Attribute name: notes Key: Non-unique Data Type: String Type: Optional Format: N/A Values: N/A Default: N/A Validation: Length Check Error Message: N/A Description: Additional notes about the payment\n\nRelationship: Payment to Loan\n\nRelationship Properties: On Delete: Cascade (Remove payments when loan is deleted) On Update: Cascade (Update payment records when loan details change) Foreign Key Type: Non-Nullable", "java": {"lines": [{"line_no": 0, "content": "package com.banking.entities;"}, {"line_no": 1, "content": ""}, {"line_no": 2, "content": "import java.math.BigDecimal;"}, {"line_no": 3, "content": "import java.time.LocalDate;"}, {"line_no": 4, "content": "import java.util.regex.Pattern;"}, {"line_no": 5, "content": ""}, {"line_no": 6, "content": "public class BankingEntities {"}, {"line_no": 7, "content": ""}, {"line_no": 8, "content": "    public static class Loan {"}, {"line_no": 9, "content": "        private static final Pattern LOAN_ID_PATTERN = Pattern.compile(\"^LN-[0-9]{6}$\");"}, {"line_no": 10, "content": "        private static final BigDecimal MIN_LOAN_AMOUNT = new BigDecimal(\"1000.00\");"}, {"line_no": 11, "content": "        private static final BigDecimal MAX_LOAN_AMOUNT = new BigDecimal(\"********.00\");"}, {"line_no": 12, "content": "        private static final BigDecimal MIN_INTEREST_RATE = new BigDecimal(\"1.00\");"}, {"line_no": 13, "content": "        private static final BigDecimal MAX_INTEREST_RATE = new BigDecimal(\"30.00\");"}, {"line_no": 14, "content": "        private static final int MIN_TERM = 3;"}, {"line_no": 15, "content": "        private static final int MAX_TERM = 480;"}, {"line_no": 16, "content": "        private static final BigDecimal MIN_LATE_FEE = BigDecimal.ZERO;"}, {"line_no": 17, "content": "        private static final BigDecimal MAX_LATE_FEE = new BigDecimal(\"20.00\");"}, {"line_no": 18, "content": "        private static final BigDecimal MIN_EARLY_PAYMENT_PENALTY = BigDecimal.ZERO;"}, {"line_no": 19, "content": "        private static final BigDecimal MAX_EARLY_PAYMENT_PENALTY = new BigDecimal(\"10.00\");"}, {"line_no": 20, "content": "        "}, {"line_no": 21, "content": "        public enum Status {"}, {"line_no": 22, "content": "            Active, Closed, Default, Restructured"}, {"line_no": 23, "content": "        }"}, {"line_no": 24, "content": "        "}, {"line_no": 25, "content": "        public enum PaymentFrequency {"}, {"line_no": 26, "content": "            Monthly, Biweekly, Weekly, Quarterly, Annually"}, {"line_no": 27, "content": "        }"}, {"line_no": 28, "content": "        "}, {"line_no": 29, "content": "        public enum LoanType {"}, {"line_no": 30, "content": "            Personal, Mortgage, Auto, Education, Business"}, {"line_no": 31, "content": "        }"}, {"line_no": 32, "content": "        "}, {"line_no": 33, "content": "        private String loanId;"}, {"line_no": 34, "content": "        private String customerId;"}, {"line_no": 35, "content": "        private BigDecimal loanAmount;"}, {"line_no": 36, "content": "        private BigDecimal interestRate = new BigDecimal(\"5.99\");"}, {"line_no": 37, "content": "        private Integer term = 60;"}, {"line_no": 38, "content": "        private LocalDate startDate = LocalDate.now();"}, {"line_no": 39, "content": "        private LocalDate endDate;"}, {"line_no": 40, "content": "        private Status status = Status.Active;"}, {"line_no": 41, "content": "        private PaymentFrequency paymentFrequency = PaymentFrequency.Monthly;"}, {"line_no": 42, "content": "        private Integer totalPaymentsMade = 0;"}, {"line_no": 43, "content": "        private BigDecimal remainingBalance;"}, {"line_no": 44, "content": "        private LoanType loanType = LoanType.Personal;"}, {"line_no": 45, "content": "        private String collateralId;"}, {"line_no": 46, "content": "        private BigDecimal originationFee = BigDecimal.ZERO;"}, {"line_no": 47, "content": "        private BigDecimal lateFeePercentage = new BigDecimal(\"5.00\");"}, {"line_no": 48, "content": "        private BigDecimal earlyPaymentPenalty = new BigDecimal(\"1.00\");"}, {"line_no": 49, "content": "        "}, {"line_no": 50, "content": "        private Customer customer;"}, {"line_no": 51, "content": "        private Collateral collateral;"}, {"line_no": 52, "content": "        "}, {"line_no": 53, "content": "        public Loan() {"}, {"line_no": 54, "content": "        }"}, {"line_no": 55, "content": "        "}, {"line_no": 56, "content": "        public String getLoanId() {"}, {"line_no": 57, "content": "            return loanId;"}, {"line_no": 58, "content": "        }"}, {"line_no": 59, "content": "        "}, {"line_no": 60, "content": "        public void setLoanId(String loanId) {"}, {"line_no": 61, "content": "            if (loanId == null || loanId.isEmpty()) {"}, {"line_no": 62, "content": "                throw new IllegalArgumentException(\"Loan ID cannot be null or empty\");"}, {"line_no": 63, "content": "            }"}, {"line_no": 64, "content": "            if (!LOAN_ID_PATTERN.matcher(loanId).matches()) {"}, {"line_no": 65, "content": "                throw new IllegalArgumentException(\"Invalid Loan ID format\");"}, {"line_no": 66, "content": "            }"}, {"line_no": 67, "content": "            this.loanId = loanId;"}, {"line_no": 68, "content": "        }"}, {"line_no": 69, "content": "        "}, {"line_no": 70, "content": "        public String getCustomerId() {"}, {"line_no": 71, "content": "            return customerId;"}, {"line_no": 72, "content": "        }"}, {"line_no": 73, "content": "        "}, {"line_no": 74, "content": "        public void setCustomerId(String customerId) {"}, {"line_no": 75, "content": "            if (customerId == null || customerId.isEmpty()) {"}, {"line_no": 76, "content": "                throw new IllegalArgumentException(\"Customer ID must reference an existing customer\");"}, {"line_no": 77, "content": "            }"}, {"line_no": 78, "content": "            this.customerId = customerId;"}, {"line_no": 79, "content": "        }"}, {"line_no": 80, "content": "        "}, {"line_no": 81, "content": "        public BigDecimal getLoanAmount() {"}, {"line_no": 82, "content": "            return loanAmount;"}, {"line_no": 83, "content": "        }"}, {"line_no": 84, "content": "        "}, {"line_no": 85, "content": "        public void setLoanAmount(BigDecimal loanAmount) {"}, {"line_no": 86, "content": "            if (loanAmount == null) {"}, {"line_no": 87, "content": "                throw new IllegalArgumentException(\"Loan amount cannot be null\");"}, {"line_no": 88, "content": "            }"}, {"line_no": 89, "content": "            if (loanAmount.compareTo(MIN_LOAN_AMOUNT) < 0 || loanAmount.compareTo(MAX_LOAN_AMOUNT) > 0) {"}, {"line_no": 90, "content": "                throw new IllegalArgumentException(\"Loan amount must be between $1,000 and $10,000,000\");"}, {"line_no": 91, "content": "            }"}, {"line_no": 92, "content": "            this.loanAmount = loanAmount;"}, {"line_no": 93, "content": "        }"}, {"line_no": 94, "content": "        "}, {"line_no": 95, "content": "        public BigDecimal getInterestRate() {"}, {"line_no": 96, "content": "            return interestRate;"}, {"line_no": 97, "content": "        }"}, {"line_no": 98, "content": "        "}, {"line_no": 99, "content": "        public void setInterestRate(BigDecimal interestRate) {"}, {"line_no": 100, "content": "            if (interestRate == null) {"}, {"line_no": 101, "content": "                throw new IllegalArgumentException(\"Interest rate cannot be null\");"}, {"line_no": 102, "content": "            }"}, {"line_no": 103, "content": "            if (interestRate.compareTo(MIN_INTEREST_RATE) < 0 || interestRate.compareTo(MAX_INTEREST_RATE) > 0) {"}, {"line_no": 104, "content": "                throw new IllegalArgumentException(\"Interest rate must be between 1.00% and 30.00%\");"}, {"line_no": 105, "content": "            }"}, {"line_no": 106, "content": "            this.interestRate = interestRate;"}, {"line_no": 107, "content": "        }"}, {"line_no": 108, "content": "        "}, {"line_no": 109, "content": "        public Integer getTerm() {"}, {"line_no": 110, "content": "            return term;"}, {"line_no": 111, "content": "        }"}, {"line_no": 112, "content": "        "}, {"line_no": 113, "content": "        public void setTerm(Integer term) {"}, {"line_no": 114, "content": "            if (term == null) {"}, {"line_no": 115, "content": "                throw new IllegalArgumentException(\"Term cannot be null\");"}, {"line_no": 116, "content": "            }"}, {"line_no": 117, "content": "            if (term < MIN_TERM || term > MAX_TERM) {"}, {"line_no": 118, "content": "                throw new IllegalArgumentException(\"Term must be between 3 and 480 months\");"}, {"line_no": 119, "content": "            }"}, {"line_no": 120, "content": "            this.term = term;"}, {"line_no": 121, "content": "        }"}, {"line_no": 122, "content": "        "}, {"line_no": 123, "content": "        public LocalDate getStartDate() {"}, {"line_no": 124, "content": "            return startDate;"}, {"line_no": 125, "content": "        }"}, {"line_no": 126, "content": "        "}, {"line_no": 127, "content": "        public void setStartDate(LocalDate startDate) {"}, {"line_no": 128, "content": "            if (startDate == null) {"}, {"line_no": 129, "content": "                throw new IllegalArgumentException(\"Start date cannot be null\");"}, {"line_no": 130, "content": "            }"}, {"line_no": 131, "content": "            if (startDate.isAfter(LocalDate.now())) {"}, {"line_no": 132, "content": "                throw new IllegalArgumentException(\"Start date cannot be in the future\");"}, {"line_no": 133, "content": "            }"}, {"line_no": 134, "content": "            this.startDate = startDate;"}, {"line_no": 135, "content": "        }"}, {"line_no": 136, "content": "        "}, {"line_no": 137, "content": "        public LocalDate getEndDate() {"}, {"line_no": 138, "content": "            return endDate;"}, {"line_no": 139, "content": "        }"}, {"line_no": 140, "content": "        "}, {"line_no": 141, "content": "        public void setEndDate(LocalDate endDate) {"}, {"line_no": 142, "content": "            if (endDate == null) {"}, {"line_no": 143, "content": "                throw new IllegalArgumentException(\"End date cannot be null\");"}, {"line_no": 144, "content": "            }"}, {"line_no": 145, "content": "            if (this.startDate != null && !endDate.isAfter(this.startDate)) {"}, {"line_no": 146, "content": "                throw new IllegalArgumentException(\"End date must be after start date\");"}, {"line_no": 147, "content": "            }"}, {"line_no": 148, "content": "            this.endDate = endDate;"}, {"line_no": 149, "content": "        }"}, {"line_no": 150, "content": "        "}, {"line_no": 151, "content": "        public Status getStatus() {"}, {"line_no": 152, "content": "            return status;"}, {"line_no": 153, "content": "        }"}, {"line_no": 154, "content": "        "}, {"line_no": 155, "content": "        public void setStatus(Status status) {"}, {"line_no": 156, "content": "            if (status == null) {"}, {"line_no": 157, "content": "                throw new IllegalArgumentException(\"Invalid loan status\");"}, {"line_no": 158, "content": "            }"}, {"line_no": 159, "content": "            this.status = status;"}, {"line_no": 160, "content": "        }"}, {"line_no": 161, "content": "        "}, {"line_no": 162, "content": "        public PaymentFrequency getPaymentFrequency() {"}, {"line_no": 163, "content": "            return paymentFrequency;"}, {"line_no": 164, "content": "        }"}, {"line_no": 165, "content": "        "}, {"line_no": 166, "content": "        public void setPaymentFrequency(PaymentFrequency paymentFrequency) {"}, {"line_no": 167, "content": "            if (paymentFrequency == null) {"}, {"line_no": 168, "content": "                throw new IllegalArgumentException(\"Invalid payment frequency\");"}, {"line_no": 169, "content": "            }"}, {"line_no": 170, "content": "            this.paymentFrequency = paymentFrequency;"}, {"line_no": 171, "content": "        }"}, {"line_no": 172, "content": "        "}, {"line_no": 173, "content": "        public Integer getTotalPaymentsMade() {"}, {"line_no": 174, "content": "            return totalPaymentsMade;"}, {"line_no": 175, "content": "        }"}, {"line_no": 176, "content": "        "}, {"line_no": 177, "content": "        public void setTotalPaymentsMade(Integer totalPaymentsMade) {"}, {"line_no": 178, "content": "            if (totalPaymentsMade == null || totalPaymentsMade < 0) {"}, {"line_no": 179, "content": "                throw new IllegalArgumentException(\"Total payments cannot be negative\");"}, {"line_no": 180, "content": "            }"}, {"line_no": 181, "content": "            this.totalPaymentsMade = totalPaymentsMade;"}, {"line_no": 182, "content": "        }"}, {"line_no": 183, "content": "        "}, {"line_no": 184, "content": "        public BigDecimal getRemainingBalance() {"}, {"line_no": 185, "content": "            return remainingBalance;"}, {"line_no": 186, "content": "        }"}, {"line_no": 187, "content": "        "}, {"line_no": 188, "content": "        protected void setRemainingBalance(BigDecimal remainingBalance) {"}, {"line_no": 189, "content": "            this.remainingBalance = remainingBalance;"}, {"line_no": 190, "content": "        }"}, {"line_no": 191, "content": "        "}, {"line_no": 192, "content": "        public LoanType getLoanType() {"}, {"line_no": 193, "content": "            return loanType;"}, {"line_no": 194, "content": "        }"}, {"line_no": 195, "content": "        "}, {"line_no": 196, "content": "        public void setLoanType(LoanType loanType) {"}, {"line_no": 197, "content": "            if (loanType == null) {"}, {"line_no": 198, "content": "                throw new IllegalArgumentException(\"Invalid loan type\");"}, {"line_no": 199, "content": "            }"}, {"line_no": 200, "content": "            this.loanType = loanType;"}, {"line_no": 201, "content": "        }"}, {"line_no": 202, "content": "        "}, {"line_no": 203, "content": "        public String getCollateralId() {"}, {"line_no": 204, "content": "            return collateralId;"}, {"line_no": 205, "content": "        }"}, {"line_no": 206, "content": "        "}, {"line_no": 207, "content": "        public void setCollateralId(String collateralId) {"}, {"line_no": 208, "content": "            this.collateralId = collateralId;"}, {"line_no": 209, "content": "        }"}, {"line_no": 210, "content": "        "}, {"line_no": 211, "content": "        public BigDecimal getOriginationFee() {"}, {"line_no": 212, "content": "            return originationFee;"}, {"line_no": 213, "content": "        }"}, {"line_no": 214, "content": "        "}, {"line_no": 215, "content": "        public void setOriginationFee(BigDecimal originationFee) {"}, {"line_no": 216, "content": "            if (originationFee == null) {"}, {"line_no": 217, "content": "                throw new IllegalArgumentException(\"Origination fee cannot be null\");"}, {"line_no": 218, "content": "            }"}, {"line_no": 219, "content": "            if (originationFee.compareTo(BigDecimal.ZERO) < 0) {"}, {"line_no": 220, "content": "                throw new IllegalArgumentException(\"Origination fee cannot be negative\");"}, {"line_no": 221, "content": "            }"}, {"line_no": 222, "content": "            this.originationFee = originationFee;"}, {"line_no": 223, "content": "        }"}, {"line_no": 224, "content": "        "}, {"line_no": 225, "content": "        public BigDecimal getLateFeePercentage() {"}, {"line_no": 226, "content": "            return lateFeePercentage;"}, {"line_no": 227, "content": "        }"}, {"line_no": 228, "content": "        "}, {"line_no": 229, "content": "        public void setLateFeePercentage(BigDecimal lateFeePercentage) {"}, {"line_no": 230, "content": "            if (lateFeePercentage == null) {"}, {"line_no": 231, "content": "                throw new IllegalArgumentException(\"Late fee percentage cannot be null\");"}, {"line_no": 232, "content": "            }"}, {"line_no": 233, "content": "            if (lateFeePercentage.compareTo(MIN_LATE_FEE) < 0 || lateFeePercentage.compareTo(MAX_LATE_FEE) > 0) {"}, {"line_no": 234, "content": "                throw new IllegalArgumentException(\"Late fee percentage must be between 0% and 20%\");"}, {"line_no": 235, "content": "            }"}, {"line_no": 236, "content": "            this.lateFeePercentage = lateFeePercentage;"}, {"line_no": 237, "content": "        }"}, {"line_no": 238, "content": "        "}, {"line_no": 239, "content": "        public BigDecimal getEarlyPaymentPenalty() {"}, {"line_no": 240, "content": "            return earlyPaymentPenalty;"}, {"line_no": 241, "content": "        }"}, {"line_no": 242, "content": "        "}, {"line_no": 243, "content": "        public void setEarlyPaymentPenalty(BigDecimal earlyPaymentPenalty) {"}, {"line_no": 244, "content": "            if (earlyPaymentPenalty == null) {"}, {"line_no": 245, "content": "                throw new IllegalArgumentException(\"Early payment penalty cannot be null\");"}, {"line_no": 246, "content": "            }"}, {"line_no": 247, "content": "            if (earlyPaymentPenalty.compareTo(MIN_EARLY_PAYMENT_PENALTY) < 0 || "}, {"line_no": 248, "content": "                earlyPaymentPenalty.compareTo(MAX_EARLY_PAYMENT_PENALTY) > 0) {"}, {"line_no": 249, "content": "                throw new IllegalArgumentException(\"Early payment penalty must be between 0% and 10%\");"}, {"line_no": 250, "content": "            }"}, {"line_no": 251, "content": "            this.earlyPaymentPenalty = earlyPaymentPenalty;"}, {"line_no": 252, "content": "        }"}, {"line_no": 253, "content": "        "}, {"line_no": 254, "content": "        public Customer getCustomer() {"}, {"line_no": 255, "content": "            return customer;"}, {"line_no": 256, "content": "        }"}, {"line_no": 257, "content": "        "}, {"line_no": 258, "content": "        public void setCustomer(Customer customer) {"}, {"line_no": 259, "content": "            this.customer = customer;"}, {"line_no": 260, "content": "            if (customer != null) {"}, {"line_no": 261, "content": "                this.customerId = customer.getCustomerId();"}, {"line_no": 262, "content": "            }"}, {"line_no": 263, "content": "        }"}, {"line_no": 264, "content": "        "}, {"line_no": 265, "content": "        public Collateral getCollateral() {"}, {"line_no": 266, "content": "            return collateral;"}, {"line_no": 267, "content": "        }"}, {"line_no": 268, "content": "        "}, {"line_no": 269, "content": "        public void setCollateral(Collateral collateral) {"}, {"line_no": 270, "content": "            if (collateral != null && this.customer != null && "}, {"line_no": 271, "content": "                !collateral.getCustomerId().equals(this.customerId)) {"}, {"line_no": 272, "content": "                throw new IllegalArgumentException(\"Collateral must belong to selected Customer\");"}, {"line_no": 273, "content": "            }"}, {"line_no": 274, "content": "            "}, {"line_no": 275, "content": "            this.collateral = collateral;"}, {"line_no": 276, "content": "            if (collateral != null) {"}, {"line_no": 277, "content": "                this.collateralId = collateral.getCollateralId();"}, {"line_no": 278, "content": "            }"}, {"line_no": 279, "content": "        }"}, {"line_no": 280, "content": "        "}, {"line_no": 281, "content": "        public void validateForUpdate() {"}, {"line_no": 282, "content": "            if (this.status != Status.Active) {"}, {"line_no": 283, "content": "                throw new IllegalStateException(\"Loan must be Active for any modifications\");"}, {"line_no": 284, "content": "            }"}, {"line_no": 285, "content": "        }"}, {"line_no": 286, "content": "        "}, {"line_no": 287, "content": "        public BigDecimal calculateMonthlyPayment() {"}, {"line_no": 288, "content": "            if (this.loanAmount == null || this.interestRate == null || this.term == null) {"}, {"line_no": 289, "content": "                return BigDecimal.ZERO;"}, {"line_no": 290, "content": "            }"}, {"line_no": 291, "content": "            "}, {"line_no": 292, "content": "            double monthlyRate = this.interestRate.doubleValue() / 1200;"}, {"line_no": 293, "content": "            double termPower = Math.pow(1 + monthlyRate, this.term);"}, {"line_no": 294, "content": "            "}, {"line_no": 295, "content": "            double payment = (this.loanAmount.doubleValue() * monthlyRate * termPower) / (termPower - 1);"}, {"line_no": 296, "content": "            "}, {"line_no": 297, "content": "            return new BigDecimal(payment).setScale(2, java.math.RoundingMode.HALF_UP);"}, {"line_no": 298, "content": "        }"}, {"line_no": 299, "content": "    }"}, {"line_no": 300, "content": ""}, {"line_no": 301, "content": "    public static class Customer {"}, {"line_no": 302, "content": "        private static final Pattern CUSTOMER_ID_PATTERN = Pattern.compile(\"^CUS-[0-9]{6}$\");"}, {"line_no": 303, "content": "        private static final int MAX_NAME_LENGTH = 50;"}, {"line_no": 304, "content": "        private static final Pattern EMAIL_PATTERN = Pattern.compile("}, {"line_no": 305, "content": "            \"^[a-zA-Z0-9_+&*-]+(?:\\\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\\\.)+[a-zA-Z]{2,7}$\");"}, {"line_no": 306, "content": "        private static final Pattern PHONE_PATTERN = Pattern.compile(\"^\\\\+?[0-9]{10,15}$\");"}, {"line_no": 307, "content": "        "}, {"line_no": 308, "content": "        private String customerId;"}, {"line_no": 309, "content": "        private String firstName;"}, {"line_no": 310, "content": "        private String lastName;"}, {"line_no": 311, "content": "        private String email;"}, {"line_no": 312, "content": "        private String phone;"}, {"line_no": 313, "content": "        "}, {"line_no": 314, "content": "        public Customer() {"}, {"line_no": 315, "content": "        }"}, {"line_no": 316, "content": "        "}, {"line_no": 317, "content": "        public String getCustomerId() {"}, {"line_no": 318, "content": "            return customerId;"}, {"line_no": 319, "content": "        }"}, {"line_no": 320, "content": "        "}, {"line_no": 321, "content": "        public void setCustomerId(String customerId) {"}, {"line_no": 322, "content": "            if (customerId == null || customerId.isEmpty()) {"}, {"line_no": 323, "content": "                throw new IllegalArgumentException(\"Customer ID cannot be null or empty\");"}, {"line_no": 324, "content": "            }"}, {"line_no": 325, "content": "            if (!CUSTOMER_ID_PATTERN.matcher(customerId).matches()) {"}, {"line_no": 326, "content": "                throw new IllegalArgumentException(\"Invalid Customer ID format\");"}, {"line_no": 327, "content": "            }"}, {"line_no": 328, "content": "            this.customerId = customerId;"}, {"line_no": 329, "content": "        }"}, {"line_no": 330, "content": "        "}, {"line_no": 331, "content": "        public String getFirstName() {"}, {"line_no": 332, "content": "            return firstName;"}, {"line_no": 333, "content": "        }"}, {"line_no": 334, "content": "        "}, {"line_no": 335, "content": "        public void setFirstName(String firstName) {"}, {"line_no": 336, "content": "            if (firstName == null || firstName.trim().isEmpty()) {"}, {"line_no": 337, "content": "                throw new IllegalArgumentException(\"First name cannot be null or empty\");"}, {"line_no": 338, "content": "            }"}, {"line_no": 339, "content": "            if (firstName.length() > MAX_NAME_LENGTH) {"}, {"line_no": 340, "content": "                throw new IllegalArgumentException(\"First name cannot exceed 50 characters\");"}, {"line_no": 341, "content": "            }"}, {"line_no": 342, "content": "            this.firstName = firstName;"}, {"line_no": 343, "content": "        }"}, {"line_no": 344, "content": "        "}, {"line_no": 345, "content": "        public String getLastName() {"}, {"line_no": 346, "content": "            return lastName;"}, {"line_no": 347, "content": "        }"}, {"line_no": 348, "content": "        "}, {"line_no": 349, "content": "        public void setLastName(String lastName) {"}, {"line_no": 350, "content": "            if (lastName == null || lastName.trim().isEmpty()) {"}, {"line_no": 351, "content": "                throw new IllegalArgumentException(\"Last name cannot be null or empty\");"}, {"line_no": 352, "content": "            }"}, {"line_no": 353, "content": "            if (lastName.length() > MAX_NAME_LENGTH) {"}, {"line_no": 354, "content": "                throw new IllegalArgumentException(\"Last name cannot exceed 50 characters\");"}, {"line_no": 355, "content": "            }"}, {"line_no": 356, "content": "            this.lastName = lastName;"}, {"line_no": 357, "content": "        }"}, {"line_no": 358, "content": "        "}, {"line_no": 359, "content": "        public String getEmail() {"}, {"line_no": 360, "content": "            return email;"}, {"line_no": 361, "content": "        }"}, {"line_no": 362, "content": "        "}, {"line_no": 363, "content": "        public void setEmail(String email) {"}, {"line_no": 364, "content": "            if (email == null || email.trim().isEmpty()) {"}, {"line_no": 365, "content": "                throw new IllegalArgumentException(\"Email cannot be null or empty\");"}, {"line_no": 366, "content": "            }"}, {"line_no": 367, "content": "            if (!EMAIL_PATTERN.matcher(email).matches()) {"}, {"line_no": 368, "content": "                throw new IllegalArgumentException(\"Invalid email format\");"}, {"line_no": 369, "content": "            }"}, {"line_no": 370, "content": "            this.email = email;"}, {"line_no": 371, "content": "        }"}, {"line_no": 372, "content": "        "}, {"line_no": 373, "content": "        public String getPhone() {"}, {"line_no": 374, "content": "            return phone;"}, {"line_no": 375, "content": "        }"}, {"line_no": 376, "content": "        "}, {"line_no": 377, "content": "        public void setPhone(String phone) {"}, {"line_no": 378, "content": "            if (phone == null || phone.trim().isEmpty()) {"}, {"line_no": 379, "content": "                throw new IllegalArgumentException(\"Phone cannot be null or empty\");"}, {"line_no": 380, "content": "            }"}, {"line_no": 381, "content": "            if (!PHONE_PATTERN.matcher(phone).matches()) {"}, {"line_no": 382, "content": "                throw new IllegalArgumentException(\"Invalid phone number format\");"}, {"line_no": 383, "content": "            }"}, {"line_no": 384, "content": "            this.phone = phone;"}, {"line_no": 385, "content": "        }"}, {"line_no": 386, "content": "        "}, {"line_no": 387, "content": "        public String getFullName() {"}, {"line_no": 388, "content": "            return firstName + \" \" + lastName;"}, {"line_no": 389, "content": "        }"}, {"line_no": 390, "content": "    }"}, {"line_no": 391, "content": ""}, {"line_no": 392, "content": "    public static class Collateral {"}, {"line_no": 393, "content": "        private static final Pattern COLLATERAL_ID_PATTERN = Pattern.compile(\"^COL-[0-9]{6}$\");"}, {"line_no": 394, "content": "        "}, {"line_no": 395, "content": "        public enum CollateralType {"}, {"line_no": 396, "content": "            REAL_ESTATE, VEHICLE, INVESTMENT, SAVINGS, OTHER"}, {"line_no": 397, "content": "        }"}, {"line_no": 398, "content": "        "}, {"line_no": 399, "content": "        private String collateralId;"}, {"line_no": 400, "content": "        private String customerId;"}, {"line_no": 401, "content": "        private String description;"}, {"line_no": 402, "content": "        private BigDecimal value;"}, {"line_no": 403, "content": "        private CollateralType type;"}, {"line_no": 404, "content": "        "}, {"line_no": 405, "content": "        public Collateral() {"}, {"line_no": 406, "content": "        }"}, {"line_no": 407, "content": "        "}, {"line_no": 408, "content": "        public String getCollateralId() {"}, {"line_no": 409, "content": "            return collateralId;"}, {"line_no": 410, "content": "        }"}, {"line_no": 411, "content": "        "}, {"line_no": 412, "content": "        public void setCollateralId(String collateralId) {"}, {"line_no": 413, "content": "            if (collateralId == null || collateralId.isEmpty()) {"}, {"line_no": 414, "content": "                throw new IllegalArgumentException(\"Collateral ID cannot be null or empty\");"}, {"line_no": 415, "content": "            }"}, {"line_no": 416, "content": "            if (!COLLATERAL_ID_PATTERN.matcher(collateralId).matches()) {"}, {"line_no": 417, "content": "                throw new IllegalArgumentException(\"Invalid Collateral ID format\");"}, {"line_no": 418, "content": "            }"}, {"line_no": 419, "content": "            this.collateralId = collateralId;"}, {"line_no": 420, "content": "        }"}, {"line_no": 421, "content": "        "}, {"line_no": 422, "content": "        public String getCustomerId() {"}, {"line_no": 423, "content": "            return customerId;"}, {"line_no": 424, "content": "        }"}, {"line_no": 425, "content": "        "}, {"line_no": 426, "content": "        public void setCustomerId(String customerId) {"}, {"line_no": 427, "content": "            if (customerId == null || customerId.isEmpty()) {"}, {"line_no": 428, "content": "                throw new IllegalArgumentException(\"Customer ID cannot be null or empty\");"}, {"line_no": 429, "content": "            }"}, {"line_no": 430, "content": "            this.customerId = customerId;"}, {"line_no": 431, "content": "        }"}, {"line_no": 432, "content": "        "}, {"line_no": 433, "content": "        public String getDescription() {"}, {"line_no": 434, "content": "            return description;"}, {"line_no": 435, "content": "        }"}, {"line_no": 436, "content": "        "}, {"line_no": 437, "content": "        public void setDescription(String description) {"}, {"line_no": 438, "content": "            if (description == null || description.trim().isEmpty()) {"}, {"line_no": 439, "content": "                throw new IllegalArgumentException(\"Description cannot be null or empty\");"}, {"line_no": 440, "content": "            }"}, {"line_no": 441, "content": "            if (description.length() > 500) {"}, {"line_no": 442, "content": "                throw new IllegalArgumentException(\"Description cannot exceed 500 characters\");"}, {"line_no": 443, "content": "            }"}, {"line_no": 444, "content": "            this.description = description;"}, {"line_no": 445, "content": "        }"}, {"line_no": 446, "content": "        "}, {"line_no": 447, "content": "        public BigDecimal getValue() {"}, {"line_no": 448, "content": "            return value;"}, {"line_no": 449, "content": "        }"}, {"line_no": 450, "content": "        "}, {"line_no": 451, "content": "        public void setValue(BigDecimal value) {"}, {"line_no": 452, "content": "            if (value == null) {"}, {"line_no": 453, "content": "                throw new IllegalArgumentException(\"Value cannot be null\");"}, {"line_no": 454, "content": "            }"}, {"line_no": 455, "content": "            if (value.compareTo(BigDecimal.ZERO) <= 0) {"}, {"line_no": 456, "content": "                throw new IllegalArgumentException(\"Collateral value must be positive\");"}, {"line_no": 457, "content": "            }"}, {"line_no": 458, "content": "            this.value = value;"}, {"line_no": 459, "content": "        }"}, {"line_no": 460, "content": "        "}, {"line_no": 461, "content": "        public CollateralType getType() {"}, {"line_no": 462, "content": "            return type;"}, {"line_no": 463, "content": "        }"}, {"line_no": 464, "content": "        "}, {"line_no": 465, "content": "        public void setType(CollateralType type) {"}, {"line_no": 466, "content": "            if (type == null) {"}, {"line_no": 467, "content": "                throw new IllegalArgumentException(\"Collateral type cannot be null\");"}, {"line_no": 468, "content": "            }"}, {"line_no": 469, "content": "            this.type = type;"}, {"line_no": 470, "content": "        }"}, {"line_no": 471, "content": "    }"}, {"line_no": 472, "content": "    "}, {"line_no": 473, "content": "    public static class Payment {"}, {"line_no": 474, "content": "        private static final BigDecimal MIN_AMOUNT = new BigDecimal(\"0.01\");"}, {"line_no": 475, "content": "        "}, {"line_no": 476, "content": "        private Long paymentId;"}, {"line_no": 477, "content": "        private String loanId;"}, {"line_no": 478, "content": "        private BigDecimal amount;"}, {"line_no": 479, "content": "        private LocalDate paymentDate;"}, {"line_no": 480, "content": "        private LocalDate dueDate;"}, {"line_no": 481, "content": "        private PaymentStatus status;"}, {"line_no": 482, "content": "        private PaymentMethod paymentMethod;"}, {"line_no": 483, "content": "        private String referenceNumber;"}, {"line_no": 484, "content": "        private BigDecimal lateFee;"}, {"line_no": 485, "content": "        private PaymentType paymentType;"}, {"line_no": 486, "content": "        private String notes;"}, {"line_no": 487, "content": "        private Loan loan;"}, {"line_no": 488, "content": "        "}, {"line_no": 489, "content": "        public enum PaymentStatus {"}, {"line_no": 490, "content": "            Pending, Completed, Failed, Cancelled"}, {"line_no": 491, "content": "        }"}, {"line_no": 492, "content": "        "}, {"line_no": 493, "content": "        public enum PaymentMethod {"}, {"line_no": 494, "content": "            CreditCard, DebitCard, BankTransfer, Cash, Check, AutoDebit"}, {"line_no": 495, "content": "        }"}, {"line_no": 496, "content": "        "}, {"line_no": 497, "content": "        public enum PaymentType {"}, {"line_no": 498, "content": "            Regular, ExtraPayment, LatePayment, InterestOnly, PrincipalOnly"}, {"line_no": 499, "content": "        }"}, {"line_no": 500, "content": "        "}, {"line_no": 501, "content": "        public Payment() {"}, {"line_no": 502, "content": "            this.status = PaymentStatus.Pending;"}, {"line_no": 503, "content": "            this.paymentMethod = PaymentMethod.AutoDebit;"}, {"line_no": 504, "content": "            this.paymentType = PaymentType.Regular;"}, {"line_no": 505, "content": "            this.lateFee = BigDecimal.ZERO;"}, {"line_no": 506, "content": "        }"}, {"line_no": 507, "content": "        "}, {"line_no": 508, "content": "        public void validate() {"}, {"line_no": 509, "content": "            if (dueDate != null && paymentDate != null && paymentDate.isAfter(dueDate)) {"}, {"line_no": 510, "content": "                this.paymentType = PaymentType.LatePayment;"}, {"line_no": 511, "content": "                "}, {"line_no": 512, "content": "                if (loan != null && loan.getLateFeePercentage() != null) {"}, {"line_no": 513, "content": "                    BigDecimal lateFeePercentage = loan.getLateFeePercentage();"}, {"line_no": 514, "content": "                    this.lateFee = amount.multiply(lateFeePercentage.divide(new BigDecimal(100)));"}, {"line_no": 515, "content": "                }"}, {"line_no": 516, "content": "            }"}, {"line_no": 517, "content": "        }"}, {"line_no": 518, "content": "        "}, {"line_no": 519, "content": "        public boolean isLate() {"}, {"line_no": 520, "content": "            return LocalDate.now().isAfter(dueDate) && status != PaymentStatus.Completed;"}, {"line_no": 521, "content": "        }"}, {"line_no": 522, "content": "        "}, {"line_no": 523, "content": "        public BigDecimal getTotalAmount() {"}, {"line_no": 524, "content": "            if (lateFee != null) {"}, {"line_no": 525, "content": "                return amount.add(lateFee);"}, {"line_no": 526, "content": "            }"}, {"line_no": 527, "content": "            return amount;"}, {"line_no": 528, "content": "        }"}, {"line_no": 529, "content": "        "}, {"line_no": 530, "content": "        public Long getPaymentId() {"}, {"line_no": 531, "content": "            return paymentId;"}, {"line_no": 532, "content": "        }"}, {"line_no": 533, "content": ""}, {"line_no": 534, "content": "        public void setPaymentId(Long paymentId) {"}, {"line_no": 535, "content": "            this.paymentId = paymentId;"}, {"line_no": 536, "content": "        }"}, {"line_no": 537, "content": ""}, {"line_no": 538, "content": "        public String getLoanId() {"}, {"line_no": 539, "content": "            return loanId;"}, {"line_no": 540, "content": "        }"}, {"line_no": 541, "content": ""}, {"line_no": 542, "content": "        public void setLoanId(String loanId) {"}, {"line_no": 543, "content": "            if (loanId == null || loanId.isEmpty()) {"}, {"line_no": 544, "content": "                throw new IllegalArgumentException(\"Loan ID is required\");"}, {"line_no": 545, "content": "            }"}, {"line_no": 546, "content": "            this.loanId = loanId;"}, {"line_no": 547, "content": "        }"}, {"line_no": 548, "content": ""}, {"line_no": 549, "content": "        public BigDecimal getAmount() {"}, {"line_no": 550, "content": "            return amount;"}, {"line_no": 551, "content": "        }"}, {"line_no": 552, "content": ""}, {"line_no": 553, "content": "        public void setAmount(BigDecimal amount) {"}, {"line_no": 554, "content": "            if (amount == null || amount.compareTo(MIN_AMOUNT) < 0) {"}, {"line_no": 555, "content": "                throw new IllegalArgumentException(\"Payment amount must be greater than 0\");"}, {"line_no": 556, "content": "            }"}, {"line_no": 557, "content": "            this.amount = amount;"}, {"line_no": 558, "content": "        }"}, {"line_no": 559, "content": ""}, {"line_no": 560, "content": "        public LocalDate getPaymentDate() {"}, {"line_no": 561, "content": "            return paymentDate;"}, {"line_no": 562, "content": "        }"}, {"line_no": 563, "content": ""}, {"line_no": 564, "content": "        public void setPaymentDate(LocalDate paymentDate) {"}, {"line_no": 565, "content": "            if (paymentDate == null) {"}, {"line_no": 566, "content": "                throw new IllegalArgumentException(\"Payment date is required\");"}, {"line_no": 567, "content": "            }"}, {"line_no": 568, "content": "            this.paymentDate = paymentDate;"}, {"line_no": 569, "content": "        }"}, {"line_no": 570, "content": ""}, {"line_no": 571, "content": "        public LocalDate getDueDate() {"}, {"line_no": 572, "content": "            return dueDate;"}, {"line_no": 573, "content": "        }"}, {"line_no": 574, "content": ""}, {"line_no": 575, "content": "        public void setDueDate(LocalDate dueDate) {"}, {"line_no": 576, "content": "            if (dueDate == null) {"}, {"line_no": 577, "content": "                throw new IllegalArgumentException(\"Due date is required\");"}, {"line_no": 578, "content": "            }"}, {"line_no": 579, "content": "            this.dueDate = dueDate;"}, {"line_no": 580, "content": "        }"}, {"line_no": 581, "content": ""}, {"line_no": 582, "content": "        public PaymentStatus getStatus() {"}, {"line_no": 583, "content": "            return status;"}, {"line_no": 584, "content": "        }"}, {"line_no": 585, "content": ""}, {"line_no": 586, "content": "        public void setStatus(PaymentStatus status) {"}, {"line_no": 587, "content": "            if (status == null) {"}, {"line_no": 588, "content": "                throw new IllegalArgumentException(\"Invalid payment status\");"}, {"line_no": 589, "content": "            }"}, {"line_no": 590, "content": "            this.status = status;"}, {"line_no": 591, "content": "        }"}, {"line_no": 592, "content": ""}, {"line_no": 593, "content": "        public PaymentMethod getPaymentMethod() {"}, {"line_no": 594, "content": "            return paymentMethod;"}, {"line_no": 595, "content": "        }"}, {"line_no": 596, "content": ""}, {"line_no": 597, "content": "        public void setPaymentMethod(PaymentMethod paymentMethod) {"}, {"line_no": 598, "content": "            if (paymentMethod == null) {"}, {"line_no": 599, "content": "                throw new IllegalArgumentException(\"Payment method cannot be null\");"}, {"line_no": 600, "content": "            }"}, {"line_no": 601, "content": "            this.paymentMethod = paymentMethod;"}, {"line_no": 602, "content": "        }"}, {"line_no": 603, "content": ""}, {"line_no": 604, "content": "        public String getReferenceNumber() {"}, {"line_no": 605, "content": "            return referenceNumber;"}, {"line_no": 606, "content": "        }"}, {"line_no": 607, "content": ""}, {"line_no": 608, "content": "        public void setReferenceNumber(String referenceNumber) {"}, {"line_no": 609, "content": "            this.referenceNumber = referenceNumber;"}, {"line_no": 610, "content": "        }"}, {"line_no": 611, "content": ""}, {"line_no": 612, "content": "        public BigDecimal getLateFee() {"}, {"line_no": 613, "content": "            return lateFee;"}, {"line_no": 614, "content": "        }"}, {"line_no": 615, "content": ""}, {"line_no": 616, "content": "        public void setLateFee(BigDecimal lateFee) {"}, {"line_no": 617, "content": "            this.lateFee = lateFee;"}, {"line_no": 618, "content": "        }"}, {"line_no": 619, "content": ""}, {"line_no": 620, "content": "        public PaymentType getPaymentType() {"}, {"line_no": 621, "content": "            return paymentType;"}, {"line_no": 622, "content": "        }"}, {"line_no": 623, "content": ""}, {"line_no": 624, "content": "        public void setPaymentType(PaymentType paymentType) {"}, {"line_no": 625, "content": "            if (paymentType == null) {"}, {"line_no": 626, "content": "                throw new IllegalArgumentException(\"Invalid payment type\");"}, {"line_no": 627, "content": "            }"}, {"line_no": 628, "content": "            this.paymentType = paymentType;"}, {"line_no": 629, "content": "        }"}, {"line_no": 630, "content": ""}, {"line_no": 631, "content": "        public String getNotes() {"}, {"line_no": 632, "content": "            return notes;"}, {"line_no": 633, "content": "        }"}, {"line_no": 634, "content": ""}, {"line_no": 635, "content": "        public void setNotes(String notes) {"}, {"line_no": 636, "content": "            this.notes = notes;"}, {"line_no": 637, "content": "        }"}, {"line_no": 638, "content": ""}, {"line_no": 639, "content": "        public Loan getLoan() {"}, {"line_no": 640, "content": "            return loan;"}, {"line_no": 641, "content": "        }"}, {"line_no": 642, "content": ""}, {"line_no": 643, "content": "        public void setLoan(Loan loan) {"}, {"line_no": 644, "content": "            this.loan = loan;"}, {"line_no": 645, "content": "            if (loan != null) {"}, {"line_no": 646, "content": "                this.loanId = loan.getLoanId();"}, {"line_no": 647, "content": "            }"}, {"line_no": 648, "content": "        }"}, {"line_no": 649, "content": "    }"}, {"line_no": 650, "content": "}"}]}, "sql": {"lines": [{"line_no": 0, "content": "CREATE TYPE loan_status AS ENUM ('ACTIVE', 'CLOSED', 'DEFAULT', 'RESTRUCTURED');"}, {"line_no": 1, "content": "CREATE TYPE payment_frequency AS ENUM ('MONTHLY', 'BIWEEKLY', 'WEEKLY', 'QUARTERLY', 'ANNUALLY');"}, {"line_no": 2, "content": "CREATE TYPE loan_type AS ENUM ('PER<PERSON><PERSON><PERSON>', 'MORTGA<PERSON>', 'AUTO', 'EDUCATION', 'BUSINESS');"}, {"line_no": 3, "content": "CREATE TYPE collateral_type AS ENUM ('REAL_ESTATE', 'VEHICLE', 'INVESTMENT', 'SAVINGS', 'OTHER');"}, {"line_no": 4, "content": "CREATE TYPE payment_status AS ENUM ('PENDING', 'COMPLETED', 'FAILED', 'REFUNDED');"}, {"line_no": 5, "content": ""}, {"line_no": 6, "content": "CREATE TABLE customers ("}, {"line_no": 7, "content": "    customer_id VARCHAR(10) PRIMARY KEY CHECK (customer_id ~ '^CUS-[0-9]{6}$'),"}, {"line_no": 8, "content": "    first_name VARCHAR(50) NOT NULL,"}, {"line_no": 9, "content": "    last_name VARCHAR(50) NOT NULL,"}, {"line_no": 10, "content": "    email VARCHAR(100) NOT NULL UNIQUE,"}, {"line_no": 11, "content": "    phone VARCHAR(20) NOT NULL CHECK (phone ~ '^\\+?[0-9]{10,15}$'),"}, {"line_no": 12, "content": "    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,"}, {"line_no": 13, "content": "    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP"}, {"line_no": 14, "content": ");"}, {"line_no": 15, "content": ""}, {"line_no": 16, "content": "CREATE TABLE collaterals ("}, {"line_no": 17, "content": "    collateral_id VARCHAR(10) PRIMARY KEY CHECK (collateral_id ~ '^COL-[0-9]{6}$'),"}, {"line_no": 18, "content": "    customer_id VARCHAR(10) NOT NULL REFERENCES customers(customer_id),"}, {"line_no": 19, "content": "    description VARCHAR(500) NOT NULL,"}, {"line_no": 20, "content": "    value DECIMAL(12,2) NOT NULL CHECK (value > 0),"}, {"line_no": 21, "content": "    type collateral_type NOT NULL,"}, {"line_no": 22, "content": "    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,"}, {"line_no": 23, "content": "    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP"}, {"line_no": 24, "content": ");"}, {"line_no": 25, "content": ""}, {"line_no": 26, "content": "CREATE TABLE loans ("}, {"line_no": 27, "content": "    loan_id VARCHAR(12) PRIMARY KEY CHECK (loan_id ~ '^LN-[0-9]{6}$'),"}, {"line_no": 28, "content": "    customer_id VARCHAR(10) NOT NULL REFERENCES customers(customer_id),"}, {"line_no": 29, "content": "    loan_amount DECIMAL(12,2) NOT NULL CHECK (loan_amount BETWEEN 1000 AND ********),"}, {"line_no": 30, "content": "    interest_rate DECIMAL(5,2) NOT NULL CHECK (interest_rate BETWEEN 1.00 AND 30.00),"}, {"line_no": 31, "content": "    term INTEGER NOT NULL CHECK (term BETWEEN 3 AND 480),"}, {"line_no": 32, "content": "    start_date DATE NOT NULL CHECK (start_date <= CURRENT_DATE),"}, {"line_no": 33, "content": "    end_date DATE NOT NULL CHECK (end_date > start_date),"}, {"line_no": 34, "content": "    status loan_status NOT NULL DEFAULT 'ACTIVE',"}, {"line_no": 35, "content": "    payment_frequency payment_frequency NOT NULL DEFAULT 'MONTHLY',"}, {"line_no": 36, "content": "    total_payments_made INTEGER NOT NULL DEFAULT 0 CHECK (total_payments_made >= 0),"}, {"line_no": 37, "content": "    loan_type loan_type NOT NULL DEFAULT 'PERSONAL',"}, {"line_no": 38, "content": "    collateral_id VARCHAR(10) REFERENCES collaterals(collateral_id),"}, {"line_no": 39, "content": "    origination_fee DECIMAL(10,2) DEFAULT 0.00 CHECK (origination_fee >= 0),"}, {"line_no": 40, "content": "    late_fee_percentage DECIMAL(5,2) DEFAULT 5.00 CHECK (late_fee_percentage BETWEEN 0 AND 20),"}, {"line_no": 41, "content": "    early_payment_penalty DECIMAL(5,2) DEFAULT 1.00 CHECK (early_payment_penalty BETWEEN 0 AND 10),"}, {"line_no": 42, "content": "    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,"}, {"line_no": 43, "content": "    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,"}, {"line_no": 44, "content": "    "}, {"line_no": 45, "content": "    CONSTRAINT loan_collateral_customer_fk CHECK ("}, {"line_no": 46, "content": "        (collateral_id IS NULL) OR "}, {"line_no": 47, "content": "        (collateral_id IS NOT NULL AND "}, {"line_no": 48, "content": "         EXISTS (SELECT 1 FROM collaterals c "}, {"line_no": 49, "content": "                 WHERE c.collateral_id = loans.collateral_id "}, {"line_no": 50, "content": "                 AND c.customer_id = loans.customer_id))"}, {"line_no": 51, "content": "    )"}, {"line_no": 52, "content": ");"}, {"line_no": 53, "content": ""}, {"line_no": 54, "content": "CREATE TABLE payments ("}, {"line_no": 55, "content": "    payment_id SERIAL PRIMARY KEY,"}, {"line_no": 56, "content": "    loan_id VARCHAR(12) NOT NULL REFERENCES loans(loan_id),"}, {"line_no": 57, "content": "    amount DECIMAL(12,2) NOT NULL CHECK (amount > 0),"}, {"line_no": 58, "content": "    payment_date TIMESTAMP NOT NULL,"}, {"line_no": 59, "content": "    status payment_status NOT NULL DEFAULT 'PENDING',"}, {"line_no": 60, "content": "    payment_method VARCHAR(50) NOT NULL,"}, {"line_no": 61, "content": "    reference_number VARCHAR(100),"}, {"line_no": 62, "content": "    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,"}, {"line_no": 63, "content": "    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP"}, {"line_no": 64, "content": ");"}, {"line_no": 65, "content": ""}, {"line_no": 66, "content": "CREATE OR REPLACE VIEW loan_balances AS"}, {"line_no": 67, "content": "SELECT "}, {"line_no": 68, "content": "    l.loan_id,"}, {"line_no": 69, "content": "    l.loan_amount,"}, {"line_no": 70, "content": "    COALESCE(SUM(p.amount) FILTER (WHERE p.status = 'COMPLETED'), 0) as total_paid,"}, {"line_no": 71, "content": "    (l.loan_amount - COALESCE(SUM(p.amount) FILTER (WHERE p.status = 'COMPLETED'), 0)) as remaining_balance"}, {"line_no": 72, "content": "FROM "}, {"line_no": 73, "content": "    loans l"}, {"line_no": 74, "content": "LEFT JOIN "}, {"line_no": 75, "content": "    payments p ON l.loan_id = p.loan_id"}, {"line_no": 76, "content": "GROUP BY "}, {"line_no": 77, "content": "    l.loan_id, l.loan_amount;"}, {"line_no": 78, "content": ""}, {"line_no": 79, "content": "CREATE INDEX idx_loans_customer ON loans(customer_id);"}, {"line_no": 80, "content": "CREATE INDEX idx_loans_status ON loans(status);"}, {"line_no": 81, "content": "CREATE INDEX idx_loans_type ON loans(loan_type);"}, {"line_no": 82, "content": "CREATE INDEX idx_payments_loan ON payments(loan_id);"}, {"line_no": 83, "content": "CREATE INDEX idx_payments_status ON payments(status);"}, {"line_no": 84, "content": "CREATE INDEX idx_collaterals_customer ON collaterals(customer_id);"}, {"line_no": 85, "content": ""}, {"line_no": 86, "content": "CREATE OR REPLACE FUNCTION update_loan_on_payment_completion()"}, {"line_no": 87, "content": "RETURNS TRIGGER AS $$"}, {"line_no": 88, "content": "BEGIN"}, {"line_no": 89, "content": "    IF (NEW.status = 'COMPLETED' AND (OLD.status IS NULL OR OLD.status != 'COMPLETED')) THEN"}, {"line_no": 90, "content": "        UPDATE loans"}, {"line_no": 91, "content": "        SET total_payments_made = total_payments_made + 1,"}, {"line_no": 92, "content": "            updated_at = CURRENT_TIMESTAMP"}, {"line_no": 93, "content": "        WHERE loan_id = NEW.loan_id;"}, {"line_no": 94, "content": "    ELSIF (OLD.status = 'COMPLETED' AND NEW.status != 'COMPLETED') THEN"}, {"line_no": 95, "content": "        UPDATE loans"}, {"line_no": 96, "content": "        SET total_payments_made = total_payments_made - 1,"}, {"line_no": 97, "content": "            updated_at = CURRENT_TIMESTAMP"}, {"line_no": 98, "content": "        WHERE loan_id = NEW.loan_id;"}, {"line_no": 99, "content": "    END IF;"}, {"line_no": 100, "content": "    RETURN NEW;"}, {"line_no": 101, "content": "END;"}, {"line_no": 102, "content": "$$ LANGUAGE plpgsql;"}, {"line_no": 103, "content": ""}, {"line_no": 104, "content": "DROP TRIGGER IF EXISTS after_payment_update ON payments;"}, {"line_no": 105, "content": "CREATE TRIGGER after_payment_update"}, {"line_no": 106, "content": "AFTER UPDATE OF status ON payments"}, {"line_no": 107, "content": "FOR EACH ROW"}, {"line_no": 108, "content": "EXECUTE FUNCTION update_loan_on_payment_completion();"}, {"line_no": 109, "content": ""}, {"line_no": 110, "content": "CREATE OR REPLACE FUNCTION validate_loan_update()"}, {"line_no": 111, "content": "RETURNS TRIGGER AS $$"}, {"line_no": 112, "content": "BEGIN"}, {"line_no": 113, "content": "    IF (OLD.status != 'ACTIVE') THEN"}, {"line_no": 114, "content": "        RAISE EXCEPTION 'Loan must be Active for any modifications';"}, {"line_no": 115, "content": "    END IF;"}, {"line_no": 116, "content": "    RETURN NEW;"}, {"line_no": 117, "content": "END;"}, {"line_no": 118, "content": "$$ LANGUAGE plpgsql;"}, {"line_no": 119, "content": ""}, {"line_no": 120, "content": "DROP TRIGGER IF EXISTS before_loan_update ON loans;"}, {"line_no": 121, "content": "CREATE TRIGGER before_loan_update"}, {"line_no": 122, "content": "BEFORE UPDATE ON loans"}, {"line_no": 123, "content": "FOR EACH ROW"}, {"line_no": 124, "content": "WHEN (OLD.* IS DISTINCT FROM NEW.*)"}, {"line_no": 125, "content": "EXECUTE FUNCTION validate_loan_update();"}, {"line_no": 126, "content": ""}, {"line_no": 127, "content": "CREATE OR REPLACE FUNCTION update_modified_timestamp()"}, {"line_no": 128, "content": "RETURNS TRIGGER AS $$"}, {"line_no": 129, "content": "BEGIN"}, {"line_no": 130, "content": "    NEW.updated_at = CURRENT_TIMESTAMP;"}, {"line_no": 131, "content": "    RETURN NEW;"}, {"line_no": 132, "content": "END;"}, {"line_no": 133, "content": "$$ LANGUAGE plpgsql;"}, {"line_no": 134, "content": ""}, {"line_no": 135, "content": "DROP TRIGGER IF EXISTS update_loans_timestamp ON loans;"}, {"line_no": 136, "content": "CREATE TRIGGER update_loans_timestamp"}, {"line_no": 137, "content": "BEFORE UPDATE ON loans"}, {"line_no": 138, "content": "FOR EACH ROW"}, {"line_no": 139, "content": "EXECUTE FUNCTION update_modified_timestamp();"}, {"line_no": 140, "content": ""}, {"line_no": 141, "content": "DROP TRIGGER IF EXISTS update_customers_timestamp ON customers;"}, {"line_no": 142, "content": "CREATE TRIGGER update_customers_timestamp"}, {"line_no": 143, "content": "BEFORE UPDATE ON customers"}, {"line_no": 144, "content": "FOR EACH ROW"}, {"line_no": 145, "content": "EXECUTE FUNCTION update_modified_timestamp();"}, {"line_no": 146, "content": ""}, {"line_no": 147, "content": "DROP TRIGGER IF EXISTS update_collaterals_timestamp ON collaterals;"}, {"line_no": 148, "content": "CREATE TRIGGER update_collaterals_timestamp"}, {"line_no": 149, "content": "BEFORE UPDATE ON collaterals"}, {"line_no": 150, "content": "FOR EACH ROW"}, {"line_no": 151, "content": "EXECUTE FUNCTION update_modified_timestamp();"}, {"line_no": 152, "content": ""}, {"line_no": 153, "content": "DROP TRIGGER IF EXISTS update_payments_timestamp ON payments;"}, {"line_no": 154, "content": "CREATE TRIGGER update_payments_timestamp"}, {"line_no": 155, "content": "BEFORE UPDATE ON payments"}, {"line_no": 156, "content": "FOR EACH ROW"}, {"line_no": 157, "content": "EXECUTE FUNCTION update_modified_timestamp();"}]}, "nsl_file_name": "Presctiptive", "java_file_name": "Programming", "prescriptives": 0, "sub_prescriptives": 0, "bets": 0, "pathways": 0, "sections": [{"id": "153035e4-18d6-40e1-abee-7aef211ea024", "title": "Loan Application Entity", "content": "Loan has loanId^PK, customerId^FK, loanAmount, interestRate, term, startDate, endDate, status (Active, Closed, Default, Restructured), paymentFrequency (Monthly, Biweekly, Weekly, Quarterly, Annually), totalPaymentsMade, remainingBalance[derived], loanType (Personal, Mortgage, Auto, Education, Business), collateralId^FK, originationFee, lateFeePercentage, earlyPaymentPenalty.", "type": "prescriptive", "order": 0, "solutionId": "0061b657-8643-4385-ba2e-618644f01c73", "tags": []}, {"id": "4e3295af-a686-4890-96d4-d6f156eee80c", "title": "Relationships for <PERSON>an", "content": "Loan has many-to-one relationship with Customer using Loan.customerId to Customer.customerId^PK\nLoan has many-to-one relationship with Collateral using Loan.collateralId to Collateral.collateralId^PK\nLoan has one-to-many relationship with Payment using Loan.loanId to Payment.loanId^FK", "type": "prescriptive", "order": 1, "solutionId": "0061b657-8643-4385-ba2e-618644f01c73", "tags": []}, {"id": "a5ba8a64-db63-4409-a866-b3ade144cc45", "title": "Validations for Loan", "content": "Loan.loanId must be unique\nLoan.loanAmount must be greater than 0\nLoan.interestRate must be greater than or equal to 1.0\nLoan.term must be greater than or equal to 3\nLoan.startDate must not be in the future\nLoan.endDate must be after startDate\n", "type": "prescriptive", "order": 2, "solutionId": "0061b657-8643-4385-ba2e-618644f01c73", "tags": []}, {"id": "a6867feb-f124-42fc-864e-6f0bc1a95fe2", "title": "Attribute Additional Properties", "content": "Attribute name: loanId Key: Primary Data Type: Integer Type: Mandatory Format: \"LN-######\" Values: N/A Default: N/A Validation: Auto-increment Error Message: \"Invalid Loan ID format\" Description: Unique identifier for the loan.\nAttribute name: customerId Key: Foreign Data Type: Integer Type: Mandatory Format: \"CUS-######\" Values: N/A Default: N/A Validation: Foreign Key Check Error Message: \"Customer ID must reference an existing customer\" Description: References the customer who took the loan.\nAttribute name: loanAmount Key: Non-unique Data Type: Decimal Type: Mandatory Format: \"$#,###.00\" Values: 1000.00-********.00 Default: N/A Validation: Range Check Error Message: \"Loan amount must be between $1,000 and $10,000,000\" Description: The principal amount borrowed by the customer.\nAttribute name: interestRate Key: Non-unique Data Type: Decimal Type: Mandatory Format: \"0.00%\" Values: 1.00-30.00 Default: \"5.99\" Validation: Range Check Error Message: \"Interest rate must be between 1.00% and 30.00%\" Description: Annual interest rate applied to the loan.\nAttribute name: term Key: Non-unique Data Type: Integer Type: Mandatory Format: \"### months\" Values: 3-480 Default: \"60\" Validation: Range Check Error Message: \"Term must be between 3 and 480 months\" Description: Duration of the loan in months.\nAttribute name: startDate Key: Non-unique Data Type: Date Type: Mandatory Format: \"YYYY-MM-DD\" Values: N/A Default: \"CURRENT_DATE\" Validation: Date Check Error Message: \"Start date cannot be in the future\" Description: Date when the loan becomes active.\nAttribute name: endDate Key: Non-unique Data Type: Date Type: Mandatory Format: \"YYYY-MM-DD\" Values: N/A Default: N/A Validation: Date Check Error Message: \"End date must be after start date\" Description: Scheduled date for loan completion.\nAttribute name: status Key: Non-unique Data Type: Enum Type: Mandatory Format: N/A Values: \"Active\", \"Closed\", \"Default\", \"Restructured\" Default: \"Active\" Validation: List Check Error Message: \"Invalid loan status\" Description: Current status of the loan.\nAttribute name: paymentFrequency Key: Non-unique Data Type: Enum Type: Mandatory Format: N/A Values: \"Monthly\", \"Biweekly\", \"Weekly\", \"Quarterly\", \"Annually\" Default: \"Monthly\" Validation: List Check Error Message: \"Invalid payment frequency\" Description: How often payments are made on the loan.\nAttribute name: totalPaymentsMade Key: Non-unique Data Type: Integer Type: Mandatory Format: \"###\" Values: 0-N Default: \"0\" Validation: Range Check Error Message: \"Total payments cannot be negative\" Description: Number of payments completed for this loan.\nremainingBalance Key: Non-unique Data Type: Decimal Type: Calculated Format: \"$#,###.00\" Values: 0.00-N Default: N/A Validation: Calculation Error Message: N/A Description: Current outstanding balance on the loan.\nAttribute name: loanType Key: Non-unique Data Type: Enum Type: Mandatory Format: N/A Values: \"Personal\", \"Mortgage\", \"Auto\", \"Education\", \"Business\" Default: \"Personal\" Validation: List Check Error Message: \"Invalid loan type\" Description: Category of loan based on purpose.\nAttribute name: collateralId Key: Foreign Data Type: Integer Type: Optional Format: \"COL-######\" Values: N/A Default: N/A Validation: Foreign Key Check Error Message: \"Collateral ID must reference existing collateral\" Description: References the asset used to secure the loan.\nAttribute name: originationFee Key: Non-unique Data Type: Decimal Type: Optional Format: \"$#,###.00\" Values: 0.00-N Default: \"0.00\" Validation: Range Check Error Message: \"Origination fee cannot be negative\" Description: Fee charged for processing a new loan application.\n Attribute name: lateFeePercentage Key: Non-unique Data Type: Decimal Type: Optional Format: \"0.00%\" Values: 0.00-20.00 Default: \"5.00\" Validation: Range Check Error Message: \"Late fee percentage must be between 0% and 20%\" Description: Percentage charged on late payments.\nAttribute name: earlyPaymentPenalty Key: Non-unique Data Type: Decimal Type: Optional Format: \"0.00%\" Values: 0.00-10.00 Default: \"1.00\" Validation: Range Check Error Message: \"Early payment penalty must be between 0% and 10%\" Description: Penalty charged for early loan payoff.\n\n\n\n", "type": "prescriptive", "order": 3, "solutionId": "0061b657-8643-4385-ba2e-618644f01c73", "tags": []}, {"id": "ee88c8e6-031d-4959-b4df-c55842845071", "title": "Relationship Properties", "content": "On Delete: Restrict (Prevent deletion of customer with active loans) On Update: Cascade (Update loan records when customer details change) Foreign Key Type: Non-Nullable.\nOn Delete: Set Null (Remove loan reference when collateral is deleted) On Update: Cascade (Update loan records when collateral details change) Foreign Key Type: Nullable.\n\n", "type": "prescriptive", "order": 4, "solutionId": "0061b657-8643-4385-ba2e-618644f01c73", "tags": []}, {"id": "c772b7c6-45e7-4baf-8a78-24b203495064", "title": "Customer En<PERSON><PERSON>", "content": "Customer has customerId^PK, firstName, lastName, email, phone, address, city, state, zipCode, country, dateOfBirth, ssn, creditScore, annualIncome, employmentStatus (Employed, Self-Employed, Unemployed, Retired), employerName, employmentLength, customerType (Individual, Business), status (Active, Inactive, Suspended).\n", "type": "prescriptive", "order": 5, "solutionId": "0061b657-8643-4385-ba2e-618644f01c73", "tags": []}, {"id": "cd95dd90-1b74-4e97-a145-16ba6c6ee337", "title": "Validations for Customer", "content": "Customer.customerId must be unique\nCustomer.email must be a valid email format\nCustomer.ssn must be a valid format and unique\nCustomer.creditScore must be between 300 and 850\nCustomer.annualIncome must be greater than 0\nCustomer.dateOfBirth must indicate customer is at least 18 years old", "type": "prescriptive", "order": 6, "solutionId": "0061b657-8643-4385-ba2e-618644f01c73", "tags": []}, {"id": "8e01a0a2-26d8-4dd1-baa7-b2c6a6f99926", "title": "Attribute Additional Properties", "content": "Attribute name: customerId Key: Primary Data Type: Integer Type: Mandatory Format: \"CUS-######\" Values: N/A Default: N/A Validation: Auto-increment Error Message: \"Invalid Customer ID format\" Description: Unique identifier for the customer.\nAttribute name: firstName Key: Non-unique Data Type: String Type: Mandatory Format: N/A Values: N/A Default: N/A Validation: Length Check Error Message: \"First name is required\" Description: Customer's first name.\nAttribute name: lastName Key: Non-unique Data Type: String Type: Mandatory Format: N/A Values: N/A Default: N/A Validation: Length Check Error Message: \"Last name is required\" Description: Customer's last name.\nAttribute name: email Key: Non-unique Data Type: String Type: Mandatory Format: \"<EMAIL>\" Values: N/A Default: N/A Validation: Format Check Error Message: \"Invalid email format\" Description: Customer's email address.\nAttribute name: phone Key: Non-unique Data Type: String Type: Mandatory Format: \"+#-###-###-####\" Values: N/A Default: N/A Validation: Format Check Error Message: \"Invalid phone number format\" Description: Customer's contact phone number.\nAttribute name: address Key: Non-unique Data Type: String Type: Mandatory Format: N/A Values: N/A Default: N/A Validation: Length Check Error Message: \"Address is required\" Description: Customer's street address.\nAttribute name: city Key: Non-unique Data Type: String Type: Mandatory Format: N/A Values: N/A Default: N/A Validation: Length Check Error Message: \"City is required\" Description: Customer's city of residence.\nAttribute name: state Key: Non-unique Data Type: String Type: Mandatory Format: N/A Values: N/A Default: N/A Validation: Length Check Error Message: \"State is required\" Description: Customer's state of residence.\nAttribute name: zipCode Key: Non-unique Data Type: String Type: Mandatory Format: \"#####\" or \"#####-####\" Values: N/A Default: N/A Validation: Format Check Error Message: \"Invalid zip code format\" Description: Customer's zip code.\nAttribute name: country Key: Non-unique Data Type: String Type: Mandatory Format: N/A Values: N/A Default: \"USA\" Validation: Length Check Error Message: \"Country is required\" Description: Customer's country of residence.\nAttribute name: dateOfBirth Key: Non-unique Data Type: Date Type: Mandatory Format: \"YYYY-MM-DD\" Values: N/A Default: N/A Validation: Age Check Error Message: \"Customer must be at least 18 years old\" Description: Customer's date of birth.\nAttribute name: ssn Key: Non-unique Data Type: String Type: Mandatory Format: \"###-##-####\" Values: N/A Default: N/A Validation: Format Check Error Message: \"Invalid SSN format\" Description: Customer's Social Security Number.\nAttribute name: annualIncome Key: Non-unique Data Type: Decimal Type: Mandatory Format: \"$#,###.00\" Values: 0.00-N Default: N/A Validation: Range Check Error Message: \"Annual income must be greater than 0\" Description: Customer's annual income.\nAttribute name: employmentStatus Key: Non-unique Data Type: Enum Type: Mandatory Format: N/A Values: \"Employed\", \"Self-Employed\", \"Unemployed\", \"Retired\" Default: \"Employed\" Validation: List Check Error Message: \"Invalid employment status\" Description: Customer's current employment status.\nAttribute name: employerName Key: Non-unique Data Type: String Type: Optional Format: N/A Values: N/A Default: N/A Validation: Length Check Error Message: N/A Description: Name of customer's employer.\nAttribute name: employmentLength Key: Non-unique Data Type: Integer Type: Optional Format: \"## years\" Values: 0-N Default: \"0\" Validation: Range Check Error Message: \"Employment length cannot be negative\" Description: Length of employment in years.\nAttribute name: customerType Key: Non-unique Data Type: Enum Type: Mandatory Format: N/A Values: \"Individual\", \"Business\" Default: \"Individual\" Validation: List Check Error Message: \"Invalid customer type\" Description: Type of customer.\nAttribute name: status Key: Non-unique Data Type: Enum Type: Mandatory Format: N/A Values: \"Active\", \"Inactive\", \"Suspended\" Default: \"Active\" Validation: List Check Error Message: \"Invalid status\" Description: Current status of the customer account.\n", "type": "prescriptive", "order": 7, "solutionId": "0061b657-8643-4385-ba2e-618644f01c73", "tags": []}, {"id": "572b3900-46c6-4d7b-9553-eefdd094c42b", "title": " Relationship: Customer to <PERSON>an", "content": " On Delete: Restrict (Prevent deletion of customer with active loans) On Update: Cascade (Update loan records when customer details change) Foreign Key Type: Non-Nullable", "type": "prescriptive", "order": 8, "solutionId": "0061b657-8643-4385-ba2e-618644f01c73", "tags": []}, {"id": "1222f0f5-5ccf-468e-8e7c-3043279c288d", "title": "Relationships for Customer", "content": "Customer has one-to-many relationship with <PERSON><PERSON> using Customer.customerId to Loan.customerId^FK\nCustomer has one-to-many relationship with Collateral using Customer.customerId to Collateral.customerId^FK", "type": "prescriptive", "order": 9, "solutionId": "0061b657-8643-4385-ba2e-618644f01c73", "tags": []}, {"id": "9bda0879-b239-4c3e-aaa8-************", "title": "Collateral Entity", "content": "Collateral has collateralId^PK, customerId^FK, loanId^FK, collateralType (RealEstate, Vehicle, Investment, Cash, Other), description, value, appraisalDate, appraisalValue, lienStatus (FirstLien, SecondLien, NoLien), condition (Excellent, Good, Fair, Poor), assetAddress, assetCity, assetState, assetZipCode, insuranceProvider, insurancePolicyNumber, insuranceExpirationDate, documentationComplete (true, false).", "type": "prescriptive", "order": 10, "solutionId": "0061b657-8643-4385-ba2e-618644f01c73", "tags": []}, {"id": "3e34546c-50eb-4e80-9e10-36add8a8c419", "title": "Validations for Collateral", "content": "Collateral.collateralId must be unique\nCollateral.customerId must exist in Customer table\nCollateral.loanId must exist in Loan table\nCollateral.value must be greater than 0\nCollateral.appraisalValue must be greater than 0\nCollateral.customerId must match the Loan.customerId of the associated loan", "type": "prescriptive", "order": 11, "solutionId": "0061b657-8643-4385-ba2e-618644f01c73", "tags": []}, {"id": "aebc4c72-1249-43c3-bb9f-2a5efa974408", "title": "Attribute Additional Properties", "content": "Attribute name: collateralId Key: Primary Data Type: Integer Type: Mandatory Format: \"COL-######\" Values: N/A Default: N/A Validation: Auto-increment Error Message: \"Invalid Collateral ID format\" Description: Unique identifier for the collateral.\nAttribute name: customerId Key: Foreign Data Type: Integer Type: Mandatory Format: \"CUS-######\" Values: N/A Default: N/A Validation: Foreign Key Check Error Message: \"Customer ID must reference an existing customer\" Description: References the customer who owns the collateral.\nAttribute name: loanId Key: Foreign Data Type: Integer Type: Optional Format: \"LN-######\" Values: N/A Default: N/A Validation: Foreign Key Check Error Message: \"Loan ID must reference an existing loan\" Description: References the loan secured by this collateral.\nAttribute name: collateralType Key: Non-unique Data Type: Enum Type: Mandatory Format: N/A Values: \"RealEstate\", \"Vehicle\", \"Investment\", \"Cash\", \"Other\" Default: \"RealEstate\" Validation: List Check Error Message: \"Invalid collateral type\" Description: Type of asset used as collateral.\nAttribute name: description Key: Non-unique Data Type: String Type: Mandatory Format: N/A Values: N/A Default: N/A Validation: Length Check Error Message: \"Description is required\" Description: Detailed description of the collateral.\nAttribute name: value Key: Non-unique Data Type: Decimal Type: Mandatory Format: \"$#,###.00\" Values: 0.00-N Default: N/A Validation: Range Check Error Message: \"Value must be greater than 0\" Description: Current market value of the collateral.\nAttribute name: appraisalDate Key: Non-unique Data Type: Date Type: Mandatory Format: \"YYYY-MM-DD\" Values: N/A Default: \"CURRENT_DATE\" Validation: Date Check Error Message: \"Appraisal date is required\" Description: Date when the collateral was appraised.\nAttribute name: appraisalValue Key: Non-unique Data Type: Decimal Type: Mandatory Format: \"$#,###.00\" Values: 0.00-N Default: N/A Validation: Range Check Error Message: \"Appraisal value must be greater than 0\" Description: Value determined during appraisal.\nAttribute name: lienStatus Key: Non-unique Data Type: Enum Type: Mandatory Format: N/A Values: \"FirstLien\", \"SecondLien\", \"NoLien\" Default: \"FirstLien\" Validation: List Check Error Message: \"Invalid lien status\" Description: Status of liens against this collateral.\nAttribute name: condition Key: Non-unique Data Type: Enum Type: Mandatory Format: N/A Values: \"Excellent\", \"Good\", \"Fair\", \"Poor\" Default: \"Good\" Validation: List Check Error Message: \"Invalid condition\" Description: Physical condition of the collateral.\nAttribute name: assetAddress Key: Non-unique Data Type: String Type: Optional Format: N/A Values: N/A Default: N/A Validation: Length Check Error Message: N/A Description: Address of the physical asset.\nAttribute name: assetCity Key: Non-unique Data Type: String Type: Optional Format: N/A Values: N/A Default: N/A Validation: Length Check Error Message: N/A Description: City where the physical asset is located.\nAttribute name: assetState Key: Non-unique Data Type: String Type: Optional Format: N/A Values: N/A Default: N/A Validation: Length Check Error Message: N/A Description: State where the physical asset is located.\nAttribute name: assetZipCode Key: Non-unique Data Type: String Type: Optional Format: \"#####\" or \"#####-####\" Values: N/A Default: N/A Validation: Format Check Error Message: \"Invalid zip code format\" Description: Zip code where the physical asset is located.\nAttribute name: insuranceProvider Key: Non-unique Data Type: String Type: Optional Format: N/A Values: N/A Default: N/A Validation: Length Check Error Message: N/A Description: Insurance company covering the collateral.\nAttribute name: insurancePolicyNumber Key: Non-unique Data Type: String Type: Optional Format: N/A Values: N/A Default: N/A Validation: Length Check Error Message: N/A Description: Insurance policy number for the collateral.\nAttribute name: insuranceExpirationDate Key: Non-unique Data Type: Date Type: Optional Format: \"YYYY-MM-DD\" Values: N/A Default: N/A Validation: Date Check Error Message: \"Expiration date must be in the future\" Description: Date when the insurance policy expires.\nAttribute name: documentationComplete Key: Non-unique Data Type: Boolean Type: Mandatory Format: N/A Values: \"true\", \"false\" Default: \"false\" Validation: Boolean Check Error Message: \"Invalid documentation status\" Description: Indicates if all required documentation is complete.\n\n", "type": "prescriptive", "order": 12, "solutionId": "0061b657-8643-4385-ba2e-618644f01c73", "tags": []}, {"id": "dce9345f-3ce5-4250-a5d9-03bc2ae1701b", "title": "Relationships for Collateral", "content": "Collateral has many-to-one relationship with Customer using Collateral.customerId to Customer.customerId^PK\nCollateral has many-to-one relationship with Loan using Collateral.loanId to Loan.loanId^PK", "type": "prescriptive", "order": 13, "solutionId": "0061b657-8643-4385-ba2e-618644f01c73", "tags": []}, {"id": "3b1d1c44-024a-4db3-b9ce-10dce7a113c4", "title": "Relationship: Collateral to Customer", "content": " On Delete: Restrict (Prevent deletion of customer with active collateral) On Update: Cascade (Update collateral records when customer details change) Foreign Key Type: Non-Nullable", "type": "prescriptive", "order": 14, "solutionId": "0061b657-8643-4385-ba2e-618644f01c73", "tags": []}, {"id": "82b15f1b-ce57-4991-9a28-204bfdac08aa", "title": "Relationship: Collateral to <PERSON><PERSON>", "content": "Relationship Properties: On Delete: Set Null (Remove loan reference when loan is deleted) On Update: Cascade (Update collateral records when loan details change) Foreign Key Type: Nullable", "type": "prescriptive", "order": 15, "solutionId": "0061b657-8643-4385-ba2e-618644f01c73", "tags": []}, {"id": "74d90456-fed8-420d-a233-8ae1be20f2a8", "title": "Payment Entity", "content": "Payment has paymentId^PK, loanId^FK, amount, paymentDate, dueDate, status (Pending, Completed, Failed, Cancelled), paymentMethod (CreditCard, DebitCard, BankTransfer, Cash, Check, AutoDebit), referenceNumber, lateFee, paymentType (Regular, ExtraPayment, LatePayment, InterestOnly, PrincipalOnly), notes.", "type": "prescriptive", "order": 16, "solutionId": "0061b657-8643-4385-ba2e-618644f01c73", "tags": []}, {"id": "0473cb53-a5b2-4464-b76c-c49529e9c7ce", "title": "Validations for Payment:", "content": "Payment.paymentId must be unique\nPayment.loanId must exist in Loan table\nPayment.amount must be greater than 0\nPayment.dueDate must be a valid date\nPayment.paymentDate must be a valid date\nPayment.status must be one of \"Pending\", \"Completed\", \"Failed\", \"Cancelled\"", "type": "prescriptive", "order": 17, "solutionId": "0061b657-8643-4385-ba2e-618644f01c73", "tags": []}, {"id": "cfbe80d0-b505-4122-8f56-97f0f2c960b9", "title": "Attribute Additional Properties", "content": "Attribute name: paymentId Key: Primary Data Type: Integer Type: Mandatory Format: \"PMT-######\" Values: N/A Default: N/A Validation: Auto-increment Error Message: \"Invalid Payment ID format\" Description: Unique identifier for the payment.\nAttribute name: loanId Key: Foreign Data Type: Integer Type: Mandatory Format: \"LN-######\" Values: N/A Default: N/A Validation: Foreign Key Check Error Message: \"Loan ID must reference an existing loan\" Description: References the loan this payment applies to.\nAttribute name: amount Key: Non-unique Data Type: Decimal Type: Mandatory Format: \"$#,###.00\" Values: 0.01-N Default: N/A Validation: Range Check Error Message: \"Payment amount must be greater than 0\" Description: Amount of the payment.\nAttribute name: paymentDate Key: Non-unique Data Type: Date Type: Mandatory Format: \"YYYY-MM-DD\" Values: N/A Default: \"CURRENT_DATE\" Validation: Date Check Error Message: \"Payment date is required\" Description: Date when the payment was made.\nAttribute name: dueDate Key: Non-unique Data Type: Date Type: Mandatory Format: \"YYYY-MM-DD\" Values: N/A Default: N/A Validation: Date Check Error Message: \"Due date is required\" Description: Date when the payment is due.\nAttribute name: status Key: Non-unique Data Type: Enum Type: Mandatory Format: N/A Values: \"Pending\", \"Completed\", \"Failed\", \"Cancelled\" Default: \"Pending\" Validation: List Check Error Message: \"Invalid payment status\" Description: Current status of the payment.\nAttribute name: paymentMethod Key: Non-unique Data Type: Enum Type: Mandatory Format: N/A Values: \"CreditCard\", \"DebitCard\", \"BankTransfer\", \"Cash\", \"Check\", \"AutoDebit\" Default: \"AutoDebit\" Validation: List Check Error Message: \"Invalid payment method\" Description: Method used for payment.\nProperties: Attribute name: referenceNumber Key: Non-unique Data Type: String Type: Optional Format: N/A Values: N/A Default: N/A Validation: Length Check Error Message: N/A Description: External reference number for the payment.\nAttribute name: lateFee Key: Non-unique Data Type: Decimal Type: Optional Format: \"$#,###.00\" Values: 0.00-N Default: \"0.00\" Validation: Range Check Error Message: \"Late fee cannot be negative\" Description: Additional fee applied for late payments.\nAttribute name: paymentType Key: Non-unique Data Type: Enum Type: Mandatory Format: N/A Values: \"Regular\", \"ExtraPayment\", \"LatePayment\", \"InterestOnly\", \"PrincipalOnly\" Default: \"Regular\" Validation: List Check Error Message: \"Invalid payment type\" Description: Type of payment being made.\nAttribute name: notes Key: Non-unique Data Type: String Type: Optional Format: N/A Values: N/A Default: N/A Validation: Length Check Error Message: N/A Description: Additional notes about the payment.\n", "type": "prescriptive", "order": 18, "solutionId": "0061b657-8643-4385-ba2e-618644f01c73", "tags": []}, {"id": "367506a4-56d6-4328-9b67-3fe7acf4ecb5", "title": "Relationships for Payment", "content": "Payment has many-to-one relationship with Loan using Payment.loanId to Loan.loanId^PK", "type": "prescriptive", "order": 19, "solutionId": "0061b657-8643-4385-ba2e-618644f01c73", "tags": []}, {"id": "741c3274-d05e-40e4-b58a-b71b620b8eac", "title": "Relationship: Payment to <PERSON>an", "content": "On Delete: Cascade (Remove payments when loan is deleted) On Update: Cascade (Update payment records when loan details change) Foreign Key Type: Non-Nullable", "type": "prescriptive", "order": 20, "solutionId": "0061b657-8643-4385-ba2e-618644f01c73", "tags": []}]}, "mappings": [{"id": "a9659d1e-9ac6-405e-ba8f-95d1fa98b745", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Loan has loanId^PK, customerId^FK, loanAmount, interestRate, term, startDate, endDate, status (Active, Closed, Default, Restructured), paymentFrequency (Monthly, Biweekly, Weekly, Quarterly, Annually), totalPaymentsMade, remainingBalance[derived], loanType (Personal, Mortgage, Auto, Education, Business), collateralId^FK, originationFee, lateFeePercentage, earlyPaymentPenalty.", "javaLines": [{"content": "    public static class Loan {", "lineIndex": 8}, {"content": "        public enum Status {", "lineIndex": 21}, {"content": "            Active, Closed, Default, Restructured", "lineIndex": 22}, {"content": "        }", "lineIndex": 23}, {"content": "        public enum PaymentFrequency {", "lineIndex": 25}, {"content": "            Monthly, Biweekly, Weekly, Quarterly, Annually", "lineIndex": 26}, {"content": "        }", "lineIndex": 27}, {"content": "        public enum LoanType {", "lineIndex": 29}, {"content": "            Personal, Mortgage, Auto, Education, Business", "lineIndex": 30}, {"content": "        }", "lineIndex": 31}], "sqlLines": [{"content": "CREATE TYPE loan_status AS ENUM ('ACTIVE', 'CLOSED', 'DEFAULT', 'RESTRUCTURED');", "lineIndex": 0}, {"content": "CREATE TYPE payment_frequency AS ENUM ('MONTHLY', 'BIWEEKLY', 'WEEKLY', 'QUARTERLY', 'ANNUALLY');", "lineIndex": 1}, {"content": "CREATE TYPE loan_type AS ENUM ('PER<PERSON><PERSON><PERSON>', 'MORTGA<PERSON>', 'AUTO', 'EDUCATION', 'BUSINESS');", "lineIndex": 2}, {"content": "CREATE TABLE loans (", "lineIndex": 26}], "type": "other", "color": "#F39C12", "tag": "E1", "level": "line", "children": [{"id": "child-loan-loanId", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "loanId^PK", "javaLines": [{"content": "        private String loanId;", "lineIndex": 33}, {"content": "        public String getLoanId() {", "lineIndex": 56}, {"content": "            return loanId;", "lineIndex": 57}, {"content": "        }", "lineIndex": 58}, {"content": "        public void setLoanId(String loanId) {", "lineIndex": 60}, {"content": "            this.loanId = loanId;", "lineIndex": 67}, {"content": "        }", "lineIndex": 68}], "sqlLines": [{"content": "    loan_id VARCHAR(12) PRIMARY KEY CHECK (loan_id ~ '^LN-[0-9]{6}$'),", "lineIndex": 27}], "type": "other", "color": "#3498DB", "tag": "", "level": "word", "parentId": "a9659d1e-9ac6-405e-ba8f-95d1fa98b745"}, {"id": "child-loan-customerId", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "customerId^FK", "javaLines": [{"content": "        private String customerId;", "lineIndex": 34}, {"content": "        public String getCustomerId() {", "lineIndex": 70}, {"content": "            return customerId;", "lineIndex": 71}, {"content": "        }", "lineIndex": 72}, {"content": "        public void setCustomerId(String customerId) {", "lineIndex": 74}, {"content": "            this.customerId = customerId;", "lineIndex": 78}, {"content": "        }", "lineIndex": 79}], "sqlLines": [{"content": "    customer_id VARCHAR(10) NOT NULL REFERENCES customers(customer_id),", "lineIndex": 28}], "type": "other", "color": "#3498DB", "tag": "", "level": "word", "parentId": "a9659d1e-9ac6-405e-ba8f-95d1fa98b745"}, {"id": "child-loan-loanAmount", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "loanAmount", "javaLines": [{"content": "        private BigDecimal loanAmount;", "lineIndex": 35}, {"content": "        public BigDecimal getLoanAmount() {", "lineIndex": 81}, {"content": "            return loanAmount;", "lineIndex": 82}, {"content": "        }", "lineIndex": 83}, {"content": "        public void setLoanAmount(BigDecimal loanAmount) {", "lineIndex": 85}, {"content": "            this.loanAmount = loanAmount;", "lineIndex": 92}, {"content": "        }", "lineIndex": 93}], "sqlLines": [{"content": "    loan_amount DECIMAL(12,2) NOT NULL CHECK (loan_amount BETWEEN 1000 AND ********),", "lineIndex": 29}], "type": "other", "color": "#3498DB", "tag": "", "level": "word", "parentId": "a9659d1e-9ac6-405e-ba8f-95d1fa98b745"}, {"id": "child-loan-interestRate", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "interestRate", "javaLines": [{"content": "        private BigDecimal interestRate = new BigDecimal(\"5.99\");", "lineIndex": 36}, {"content": "        public BigDecimal getInterestRate() {", "lineIndex": 95}, {"content": "            return interestRate;", "lineIndex": 96}, {"content": "        }", "lineIndex": 97}, {"content": "        public void setInterestRate(BigDecimal interestRate) {", "lineIndex": 99}, {"content": "            this.interestRate = interestRate;", "lineIndex": 106}, {"content": "        }", "lineIndex": 107}], "sqlLines": [{"content": "    interest_rate DECIMAL(5,2) NOT NULL CHECK (interest_rate BETWEEN 1.00 AND 30.00),", "lineIndex": 30}], "type": "other", "color": "#3498DB", "tag": "", "level": "word", "parentId": "a9659d1e-9ac6-405e-ba8f-95d1fa98b745"}, {"id": "child-loan-term", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "term", "javaLines": [{"content": "        private Integer term = 60;", "lineIndex": 37}, {"content": "        public Integer getTerm() {", "lineIndex": 109}, {"content": "            return term;", "lineIndex": 110}, {"content": "        }", "lineIndex": 111}, {"content": "        public void setTerm(Integer term) {", "lineIndex": 113}, {"content": "            this.term = term;", "lineIndex": 120}, {"content": "        }", "lineIndex": 121}], "sqlLines": [{"content": "    term INTEGER NOT NULL CHECK (term BETWEEN 3 AND 480),", "lineIndex": 31}], "type": "other", "color": "#3498DB", "tag": "", "level": "word", "parentId": "a9659d1e-9ac6-405e-ba8f-95d1fa98b745"}, {"id": "child-loan-startDate", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "startDate", "javaLines": [{"content": "        private LocalDate startDate = LocalDate.now();", "lineIndex": 38}, {"content": "        public LocalDate getStartDate() {", "lineIndex": 123}, {"content": "            return startDate;", "lineIndex": 124}, {"content": "        }", "lineIndex": 125}, {"content": "        public void setStartDate(LocalDate startDate) {", "lineIndex": 127}, {"content": "            this.startDate = startDate;", "lineIndex": 134}, {"content": "        }", "lineIndex": 135}], "sqlLines": [{"content": "    start_date DATE NOT NULL CHECK (start_date <= CURRENT_DATE),", "lineIndex": 32}], "type": "other", "color": "#3498DB", "tag": "", "level": "word", "parentId": "a9659d1e-9ac6-405e-ba8f-95d1fa98b745"}, {"id": "child-loan-endDate", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "endDate", "javaLines": [{"content": "        private LocalDate endDate;", "lineIndex": 39}, {"content": "        public LocalDate getEndDate() {", "lineIndex": 137}, {"content": "            return endDate;", "lineIndex": 138}, {"content": "        }", "lineIndex": 139}, {"content": "        public void setEndDate(LocalDate endDate) {", "lineIndex": 141}, {"content": "            this.endDate = endDate;", "lineIndex": 148}, {"content": "        }", "lineIndex": 149}], "sqlLines": [{"content": "    end_date DATE NOT NULL CHECK (end_date > start_date),", "lineIndex": 33}], "type": "other", "color": "#3498DB", "tag": "", "level": "word", "parentId": "a9659d1e-9ac6-405e-ba8f-95d1fa98b745"}, {"id": "child-loan-status", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "status (Active, Closed, Default, Restructured)", "javaLines": [{"content": "        public enum Status {", "lineIndex": 21}, {"content": "            Active, Closed, Default, Restructured", "lineIndex": 22}, {"content": "        }", "lineIndex": 23}, {"content": "        private Status status = Status.Active;", "lineIndex": 40}, {"content": "        public Status getStatus() {", "lineIndex": 151}, {"content": "            return status;", "lineIndex": 152}, {"content": "        }", "lineIndex": 153}, {"content": "        public void setStatus(Status status) {", "lineIndex": 155}, {"content": "            this.status = status;", "lineIndex": 159}, {"content": "        }", "lineIndex": 160}], "sqlLines": [{"content": "CREATE TYPE loan_status AS ENUM ('ACTIVE', 'CLOSED', 'DEFAULT', 'RESTRUCTURED');", "lineIndex": 0}, {"content": "    status loan_status NOT NULL DEFAULT 'ACTIVE',", "lineIndex": 34}], "type": "other", "color": "#3498DB", "tag": "", "level": "word", "parentId": "a9659d1e-9ac6-405e-ba8f-95d1fa98b745"}, {"id": "child-loan-paymentFrequency", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "paymentFrequency (Monthly, Biweekly, Weekly, Quarterly, Annually)", "javaLines": [{"content": "        public enum PaymentFrequency {", "lineIndex": 25}, {"content": "            Monthly, Biweekly, Weekly, Quarterly, Annually", "lineIndex": 26}, {"content": "        }", "lineIndex": 27}, {"content": "        private PaymentFrequency paymentFrequency = PaymentFrequency.Monthly;", "lineIndex": 41}, {"content": "        public PaymentFrequency getPaymentFrequency() {", "lineIndex": 162}, {"content": "            return paymentFrequency;", "lineIndex": 163}, {"content": "        }", "lineIndex": 164}, {"content": "        public void setPaymentFrequency(PaymentFrequency paymentFrequency) {", "lineIndex": 166}, {"content": "            this.paymentFrequency = paymentFrequency;", "lineIndex": 170}, {"content": "        }", "lineIndex": 171}], "sqlLines": [{"content": "CREATE TYPE payment_frequency AS ENUM ('MONTHLY', 'BIWEEKLY', 'WEEKLY', 'QUARTERLY', 'ANNUALLY');", "lineIndex": 1}, {"content": "    payment_frequency payment_frequency NOT NULL DEFAULT 'MONTHLY',", "lineIndex": 35}], "type": "other", "color": "#3498DB", "tag": "", "level": "word", "parentId": "a9659d1e-9ac6-405e-ba8f-95d1fa98b745"}, {"id": "child-loan-totalPaymentsMade", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "totalPaymentsMade", "javaLines": [{"content": "        private Integer totalPaymentsMade = 0;", "lineIndex": 42}, {"content": "        public Integer getTotalPaymentsMade() {", "lineIndex": 173}, {"content": "            return totalPaymentsMade;", "lineIndex": 174}, {"content": "        }", "lineIndex": 175}, {"content": "        public void setTotalPaymentsMade(Integer totalPaymentsMade) {", "lineIndex": 177}, {"content": "            this.totalPaymentsMade = totalPaymentsMade;", "lineIndex": 181}, {"content": "        }", "lineIndex": 182}], "sqlLines": [{"content": "    total_payments_made INTEGER NOT NULL DEFAULT 0 CHECK (total_payments_made >= 0),", "lineIndex": 36}], "type": "other", "color": "#3498DB", "tag": "", "level": "word", "parentId": "a9659d1e-9ac6-405e-ba8f-95d1fa98b745"}, {"id": "child-loan-remainingBalance", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "remainingBalance[derived]", "javaLines": [{"content": "        private BigDecimal remainingBalance;", "lineIndex": 43}, {"content": "        public BigDecimal getRemainingBalance() {", "lineIndex": 184}, {"content": "            return remainingBalance;", "lineIndex": 185}, {"content": "        }", "lineIndex": 186}, {"content": "        protected void setRemainingBalance(BigDecimal remainingBalance) {", "lineIndex": 188}, {"content": "            this.remainingBalance = remainingBalance;", "lineIndex": 189}, {"content": "        }", "lineIndex": 190}], "sqlLines": [{"content": "CREATE OR REPLACE VIEW loan_balances AS", "lineIndex": 66}, {"content": "    (l.loan_amount - COALESCE(SUM(p.amount) FILTER (WHERE p.status = 'COMPLETED'), 0)) as remaining_balance", "lineIndex": 71}], "type": "other", "color": "#3498DB", "tag": "", "level": "word", "parentId": "a9659d1e-9ac6-405e-ba8f-95d1fa98b745"}, {"id": "child-loan-loanType", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "loanType (Personal, Mortgage, Auto, Education, Business)", "javaLines": [{"content": "        public enum LoanType {", "lineIndex": 29}, {"content": "            Personal, Mortgage, Auto, Education, Business", "lineIndex": 30}, {"content": "        }", "lineIndex": 31}, {"content": "        private LoanType loanType = LoanType.Personal;", "lineIndex": 44}, {"content": "        public LoanType getLoanType() {", "lineIndex": 192}, {"content": "            return loanType;", "lineIndex": 193}, {"content": "        }", "lineIndex": 194}, {"content": "        public void setLoanType(LoanType loanType) {", "lineIndex": 196}, {"content": "            this.loanType = loanType;", "lineIndex": 200}, {"content": "        }", "lineIndex": 201}], "sqlLines": [{"content": "CREATE TYPE loan_type AS ENUM ('PER<PERSON><PERSON><PERSON>', 'MORTGA<PERSON>', 'AUTO', 'EDUCATION', 'BUSINESS');", "lineIndex": 2}, {"content": "    loan_type loan_type NOT NULL DEFAULT 'PERSONAL',", "lineIndex": 37}], "type": "other", "color": "#3498DB", "tag": "", "level": "word", "parentId": "a9659d1e-9ac6-405e-ba8f-95d1fa98b745"}, {"id": "child-loan-collateralId", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "collateralId^FK", "javaLines": [{"content": "        private String collateralId;", "lineIndex": 45}, {"content": "        public String getCollateralId() {", "lineIndex": 203}, {"content": "            return collateralId;", "lineIndex": 204}, {"content": "        }", "lineIndex": 205}, {"content": "        public void setCollateralId(String collateralId) {", "lineIndex": 207}, {"content": "            this.collateralId = collateralId;", "lineIndex": 208}, {"content": "        }", "lineIndex": 209}], "sqlLines": [{"content": "    collateral_id VARCHAR(10) REFERENCES collaterals(collateral_id),", "lineIndex": 38}], "type": "other", "color": "#3498DB", "tag": "", "level": "word", "parentId": "a9659d1e-9ac6-405e-ba8f-95d1fa98b745"}, {"id": "child-loan-originationFee", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "originationFee", "javaLines": [{"content": "        private BigDecimal originationFee = BigDecimal.ZERO;", "lineIndex": 46}, {"content": "        public BigDecimal getOriginationFee() {", "lineIndex": 211}, {"content": "            return originationFee;", "lineIndex": 212}, {"content": "        }", "lineIndex": 213}, {"content": "        public void setOriginationFee(BigDecimal originationFee) {", "lineIndex": 215}, {"content": "            this.originationFee = originationFee;", "lineIndex": 222}, {"content": "        }", "lineIndex": 223}], "sqlLines": [{"content": "    origination_fee DECIMAL(10,2) DEFAULT 0.00 CHECK (origination_fee >= 0),", "lineIndex": 39}], "type": "other", "color": "#3498DB", "tag": "", "level": "word", "parentId": "a9659d1e-9ac6-405e-ba8f-95d1fa98b745"}, {"id": "child-loan-late<PERSON><PERSON>P<PERSON><PERSON>age", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "lateFeePercentage", "javaLines": [{"content": "        private BigDecimal lateFeePercentage = new BigDecimal(\"5.00\");", "lineIndex": 47}, {"content": "        public BigDecimal getLateFeePercentage() {", "lineIndex": 225}, {"content": "            return lateFeePercentage;", "lineIndex": 226}, {"content": "        }", "lineIndex": 227}, {"content": "        public void setLateFeePercentage(BigDecimal lateFeePercentage) {", "lineIndex": 229}, {"content": "            this.lateFeePercentage = lateFeePercentage;", "lineIndex": 236}, {"content": "        }", "lineIndex": 237}], "sqlLines": [{"content": "    late_fee_percentage DECIMAL(5,2) DEFAULT 5.00 CHECK (late_fee_percentage BETWEEN 0 AND 20),", "lineIndex": 40}], "type": "other", "color": "#3498DB", "tag": "", "level": "word", "parentId": "a9659d1e-9ac6-405e-ba8f-95d1fa98b745"}, {"id": "child-loan-earlyPaymentPenalty", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "earlyPaymentPenalty", "javaLines": [{"content": "        private BigDecimal earlyPaymentPenalty = new BigDecimal(\"1.00\");", "lineIndex": 48}, {"content": "        public BigDecimal getEarlyPaymentPenalty() {", "lineIndex": 239}, {"content": "            return earlyPaymentPenalty;", "lineIndex": 240}, {"content": "        }", "lineIndex": 241}, {"content": "        public void setEarlyPaymentPenalty(BigDecimal earlyPaymentPenalty) {", "lineIndex": 243}, {"content": "            this.earlyPaymentPenalty = earlyPaymentPenalty;", "lineIndex": 251}, {"content": "        }", "lineIndex": 252}], "sqlLines": [{"content": "    early_payment_penalty DECIMAL(5,2) DEFAULT 1.00 CHECK (early_payment_penalty BETWEEN 0 AND 10),", "lineIndex": 41}], "type": "other", "color": "#3498DB", "tag": "", "level": "word", "parentId": "a9659d1e-9ac6-405e-ba8f-95d1fa98b745"}]}, {"id": "mapping-line-003", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Loan has many-to-one relationship with Customer using Loan.customerId to Customer.customerId^PK", "javaLines": [{"content": "        private Customer customer;", "lineIndex": 50}, {"content": "        public Customer getCustomer() {", "lineIndex": 254}, {"content": "            return customer;", "lineIndex": 255}, {"content": "        }", "lineIndex": 256}, {"content": "        public void setCustomer(Customer customer) {", "lineIndex": 258}, {"content": "            this.customer = customer;", "lineIndex": 259}, {"content": "            if (customer != null) {", "lineIndex": 260}, {"content": "                this.customerId = customer.getCustomerId();", "lineIndex": 261}, {"content": "            }", "lineIndex": 262}, {"content": "        }", "lineIndex": 263}], "sqlLines": [{"content": "    customer_id VARCHAR(10) NOT NULL REFERENCES customers(customer_id),", "lineIndex": 28}], "type": "relationship", "color": "#9B59B6", "tag": "R1", "level": "line"}, {"id": "mapping-line-004", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Loan has many-to-one relationship with Collateral using Loan.collateralId to Collateral.collateralId^PK", "javaLines": [{"content": "        private Collateral collateral;", "lineIndex": 51}, {"content": "        public Collateral getCollateral() {", "lineIndex": 265}, {"content": "            return collateral;", "lineIndex": 266}, {"content": "        }", "lineIndex": 267}, {"content": "        public void setCollateral(Collateral collateral) {", "lineIndex": 269}, {"content": "            if (collateral != null && this.customer != null && ", "lineIndex": 270}, {"content": "                !collateral.getCustomerId().equals(this.customerId)) {", "lineIndex": 271}, {"content": "                throw new IllegalArgumentException(\"Collateral must belong to selected Customer\");", "lineIndex": 272}, {"content": "            }", "lineIndex": 273}, {"content": "            this.collateral = collateral;", "lineIndex": 275}, {"content": "            if (collateral != null) {", "lineIndex": 276}, {"content": "                this.collateralId = collateral.getCollateralId();", "lineIndex": 277}, {"content": "            }", "lineIndex": 278}, {"content": "        }", "lineIndex": 279}], "sqlLines": [{"content": "    collateral_id VARCHAR(10) REFERENCES collaterals(collateral_id),", "lineIndex": 38}], "type": "relationship", "color": "#9B59B6", "tag": "R2", "level": "line"}, {"id": "mapping-line-005", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Loan has one-to-many relationship with Payment using Loan.loanId to Payment.loanId^FK", "javaLines": [], "sqlLines": [{"content": "    loan_id VARCHAR(12) NOT NULL REFERENCES loans(loan_id),", "lineIndex": 56}], "type": "relationship", "color": "#9B59B6", "tag": "R3", "level": "line"}, {"id": "mapping-line-007", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Loan.loanId must be unique", "javaLines": [{"content": "        private static final Pattern LOAN_ID_PATTERN = Pattern.compile(\"^LN-[0-9]{6}$\");", "lineIndex": 9}, {"content": "            if (!LOAN_ID_PATTERN.matcher(loanId).matches()) {", "lineIndex": 64}, {"content": "                throw new IllegalArgumentException(\"Invalid Loan ID format\");", "lineIndex": 65}], "sqlLines": [{"content": "    loan_id VARCHAR(12) PRIMARY KEY CHECK (loan_id ~ '^LN-[0-9]{6}$'),", "lineIndex": 27}], "type": "validation", "color": "#E74C3C", "tag": "V1", "level": "line"}, {"id": "mapping-line-008", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Loan.loanAmount must be greater than 0", "javaLines": [{"content": "        private static final BigDecimal MIN_LOAN_AMOUNT = new BigDecimal(\"1000.00\");", "lineIndex": 10}, {"content": "        private static final BigDecimal MAX_LOAN_AMOUNT = new BigDecimal(\"********.00\");", "lineIndex": 11}, {"content": "            if (loanAmount.compareTo(MIN_LOAN_AMOUNT) < 0 || loanAmount.compareTo(MAX_LOAN_AMOUNT) > 0) {", "lineIndex": 89}, {"content": "                throw new IllegalArgumentException(\"Loan amount must be between $1,000 and $10,000,000\");", "lineIndex": 90}], "sqlLines": [{"content": "    loan_amount DECIMAL(12,2) NOT NULL CHECK (loan_amount BETWEEN 1000 AND ********),", "lineIndex": 29}], "type": "validation", "color": "#E74C3C", "tag": "V2", "level": "line"}, {"id": "mapping-line-009", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Loan.interestRate must be greater than or equal to 1.0", "javaLines": [{"content": "        private static final BigDecimal MIN_INTEREST_RATE = new BigDecimal(\"1.00\");", "lineIndex": 12}, {"content": "        private static final BigDecimal MAX_INTEREST_RATE = new BigDecimal(\"30.00\");", "lineIndex": 13}, {"content": "            if (interestRate.compareTo(MIN_INTEREST_RATE) < 0 || interestRate.compareTo(MAX_INTEREST_RATE) > 0) {", "lineIndex": 103}, {"content": "                throw new IllegalArgumentException(\"Interest rate must be between 1.00% and 30.00%\");", "lineIndex": 104}], "sqlLines": [{"content": "    interest_rate DECIMAL(5,2) NOT NULL CHECK (interest_rate BETWEEN 1.00 AND 30.00),", "lineIndex": 30}], "type": "validation", "color": "#E74C3C", "tag": "V3", "level": "line"}, {"id": "mapping-line-010", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Loan.term must be greater than or equal to 3", "javaLines": [{"content": "        private static final int MIN_TERM = 3;", "lineIndex": 14}, {"content": "        private static final int MAX_TERM = 480;", "lineIndex": 15}, {"content": "            if (term < MIN_TERM || term > MAX_TERM) {", "lineIndex": 117}, {"content": "                throw new IllegalArgumentException(\"Term must be between 3 and 480 months\");", "lineIndex": 118}], "sqlLines": [{"content": "    term INTEGER NOT NULL CHECK (term BETWEEN 3 AND 480),", "lineIndex": 31}], "type": "validation", "color": "#E74C3C", "tag": "V4", "level": "line"}, {"id": "mapping-line-011", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Loan.startDate must not be in the future", "javaLines": [{"content": "            if (startDate.isAfter(LocalDate.now())) {", "lineIndex": 131}, {"content": "                throw new IllegalArgumentException(\"Start date cannot be in the future\");", "lineIndex": 132}], "sqlLines": [{"content": "    start_date DATE NOT NULL CHECK (start_date <= CURRENT_DATE),", "lineIndex": 32}], "type": "validation", "color": "#E74C3C", "tag": "V5", "level": "line"}, {"id": "mapping-line-012", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Loan.endDate must be after startDate", "javaLines": [{"content": "            if (this.startDate != null && !endDate.isAfter(this.startDate)) {", "lineIndex": 145}, {"content": "                throw new IllegalArgumentException(\"End date must be after start date\");", "lineIndex": 146}], "sqlLines": [{"content": "    end_date DATE NOT NULL CHECK (end_date > start_date),", "lineIndex": 33}], "type": "validation", "color": "#E74C3C", "tag": "V6", "level": "line"}, {"id": "mapping-line-013", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: loanId Key: Primary Data Type: Integer Type: Mandatory Format: \"LN-######\" Values: N/A Default: N/A Validation: Auto-increment Error Message: \"Invalid Loan ID format\" Description: Unique identifier for the loan", "javaLines": [{"content": "        private static final Pattern LOAN_ID_PATTERN = Pattern.compile(\"^LN-[0-9]{6}$\");", "lineIndex": 9}, {"content": "        private String loanId;", "lineIndex": 33}, {"content": "        public String getLoanId() {", "lineIndex": 56}, {"content": "            return loanId;", "lineIndex": 57}, {"content": "        }", "lineIndex": 58}, {"content": "        public void setLoanId(String loanId) {", "lineIndex": 60}, {"content": "            if (loanId == null || loanId.isEmpty()) {", "lineIndex": 61}, {"content": "                throw new IllegalArgumentException(\"Loan ID cannot be null or empty\");", "lineIndex": 62}, {"content": "            }", "lineIndex": 63}, {"content": "            if (!LOAN_ID_PATTERN.matcher(loanId).matches()) {", "lineIndex": 64}, {"content": "                throw new IllegalArgumentException(\"Invalid Loan ID format\");", "lineIndex": 65}, {"content": "            }", "lineIndex": 66}, {"content": "            this.loanId = loanId;", "lineIndex": 67}, {"content": "        }", "lineIndex": 68}], "sqlLines": [{"content": "    loan_id VARCHAR(12) PRIMARY KEY CHECK (loan_id ~ '^LN-[0-9]{6}$'),", "lineIndex": 27}], "type": "attribute", "color": "#3498DB", "tag": "A1", "level": "line"}, {"id": "mapping-line-014", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: customerId Key: Foreign Data Type: Integer Type: Mandatory Format: \"CUS-######\" Values: N/A Default: N/A Validation: Foreign Key Check Error Message: \"Customer ID must reference an existing customer\" Description: References the customer who took the loan", "javaLines": [{"content": "        private String customerId;", "lineIndex": 34}, {"content": "        public String getCustomerId() {", "lineIndex": 70}, {"content": "            return customerId;", "lineIndex": 71}, {"content": "        }", "lineIndex": 72}, {"content": "        public void setCustomerId(String customerId) {", "lineIndex": 74}, {"content": "            if (customerId == null || customerId.isEmpty()) {", "lineIndex": 75}, {"content": "                throw new IllegalArgumentException(\"Customer ID must reference an existing customer\");", "lineIndex": 76}, {"content": "            }", "lineIndex": 77}, {"content": "            this.customerId = customerId;", "lineIndex": 78}, {"content": "        }", "lineIndex": 79}], "sqlLines": [{"content": "    customer_id VARCHAR(10) NOT NULL REFERENCES customers(customer_id),", "lineIndex": 28}], "type": "attribute", "color": "#3498DB", "tag": "A2", "level": "line"}, {"id": "mapping-line-015", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: loanAmount Key: Non-unique Data Type: Decimal Type: Mandatory Format: \"$#,###.00\" Values: 1000.00-********.00 Default: N/A Validation: Range Check Error Message: \"Loan amount must be between $1,000 and $10,000,000\" Description: The principal amount borrowed by the customer", "javaLines": [{"content": "        private static final BigDecimal MIN_LOAN_AMOUNT = new BigDecimal(\"1000.00\");", "lineIndex": 10}, {"content": "        private static final BigDecimal MAX_LOAN_AMOUNT = new BigDecimal(\"********.00\");", "lineIndex": 11}, {"content": "        private BigDecimal loanAmount;", "lineIndex": 35}, {"content": "        public BigDecimal getLoanAmount() {", "lineIndex": 81}, {"content": "            return loanAmount;", "lineIndex": 82}, {"content": "        }", "lineIndex": 83}, {"content": "        public void setLoanAmount(BigDecimal loanAmount) {", "lineIndex": 85}, {"content": "            if (loanAmount == null) {", "lineIndex": 86}, {"content": "                throw new IllegalArgumentException(\"Loan amount cannot be null\");", "lineIndex": 87}, {"content": "            }", "lineIndex": 88}, {"content": "            if (loanAmount.compareTo(MIN_LOAN_AMOUNT) < 0 || loanAmount.compareTo(MAX_LOAN_AMOUNT) > 0) {", "lineIndex": 89}, {"content": "                throw new IllegalArgumentException(\"Loan amount must be between $1,000 and $10,000,000\");", "lineIndex": 90}, {"content": "            }", "lineIndex": 91}, {"content": "            this.loanAmount = loanAmount;", "lineIndex": 92}, {"content": "        }", "lineIndex": 93}], "sqlLines": [{"content": "    loan_amount DECIMAL(12,2) NOT NULL CHECK (loan_amount BETWEEN 1000 AND ********),", "lineIndex": 29}], "type": "attribute", "color": "#3498DB", "tag": "A3", "level": "line"}, {"id": "mapping-line-016", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: interestRate Key: Non-unique Data Type: Decimal Type: Mandatory Format: \"0.00%\" Values: 1.00-30.00 Default: \"5.99\" Validation: Range Check Error Message: \"Interest rate must be between 1.00% and 30.00%\" Description: Annual interest rate applied to the loan", "javaLines": [{"content": "        private static final BigDecimal MIN_INTEREST_RATE = new BigDecimal(\"1.00\");", "lineIndex": 12}, {"content": "        private static final BigDecimal MAX_INTEREST_RATE = new BigDecimal(\"30.00\");", "lineIndex": 13}, {"content": "        private BigDecimal interestRate = new BigDecimal(\"5.99\");", "lineIndex": 36}, {"content": "        public BigDecimal getInterestRate() {", "lineIndex": 95}, {"content": "            return interestRate;", "lineIndex": 96}, {"content": "        }", "lineIndex": 97}, {"content": "        public void setInterestRate(BigDecimal interestRate) {", "lineIndex": 99}, {"content": "            if (interestRate == null) {", "lineIndex": 100}, {"content": "                throw new IllegalArgumentException(\"Interest rate cannot be null\");", "lineIndex": 101}, {"content": "            }", "lineIndex": 102}, {"content": "            if (interestRate.compareTo(MIN_INTEREST_RATE) < 0 || interestRate.compareTo(MAX_INTEREST_RATE) > 0) {", "lineIndex": 103}, {"content": "                throw new IllegalArgumentException(\"Interest rate must be between 1.00% and 30.00%\");", "lineIndex": 104}, {"content": "            }", "lineIndex": 105}, {"content": "            this.interestRate = interestRate;", "lineIndex": 106}, {"content": "        }", "lineIndex": 107}], "sqlLines": [{"content": "    interest_rate DECIMAL(5,2) NOT NULL CHECK (interest_rate BETWEEN 1.00 AND 30.00),", "lineIndex": 30}], "type": "attribute", "color": "#3498DB", "tag": "A4", "level": "line"}, {"id": "mapping-line-017", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: term Key: Non-unique Data Type: Integer Type: Mandatory Format: \"### months\" Values: 3-480 Default: \"60\" Validation: Range Check Error Message: \"Term must be between 3 and 480 months\" Description: Duration of the loan in months", "javaLines": [{"content": "        private static final int MIN_TERM = 3;", "lineIndex": 14}, {"content": "        private static final int MAX_TERM = 480;", "lineIndex": 15}, {"content": "        private Integer term = 60;", "lineIndex": 37}, {"content": "        public Integer getTerm() {", "lineIndex": 109}, {"content": "            return term;", "lineIndex": 110}, {"content": "        }", "lineIndex": 111}, {"content": "        public void setTerm(Integer term) {", "lineIndex": 113}, {"content": "            if (term == null) {", "lineIndex": 114}, {"content": "                throw new IllegalArgumentException(\"Term cannot be null\");", "lineIndex": 115}, {"content": "            }", "lineIndex": 116}, {"content": "            if (term < MIN_TERM || term > MAX_TERM) {", "lineIndex": 117}, {"content": "                throw new IllegalArgumentException(\"Term must be between 3 and 480 months\");", "lineIndex": 118}, {"content": "            }", "lineIndex": 119}, {"content": "            this.term = term;", "lineIndex": 120}, {"content": "        }", "lineIndex": 121}], "sqlLines": [{"content": "    term INTEGER NOT NULL CHECK (term BETWEEN 3 AND 480),", "lineIndex": 31}], "type": "attribute", "color": "#3498DB", "tag": "A5", "level": "line"}, {"id": "mapping-line-018", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: startDate Key: Non-unique Data Type: Date Type: Mandatory Format: \"YYYY-MM-DD\" Values: N/A Default: \"CURRENT_DATE\" Validation: Date Check Error Message: \"Start date cannot be in the future\" Description: Date when the loan becomes active", "javaLines": [{"content": "        private LocalDate startDate = LocalDate.now();", "lineIndex": 38}, {"content": "        public LocalDate getStartDate() {", "lineIndex": 123}, {"content": "            return startDate;", "lineIndex": 124}, {"content": "        }", "lineIndex": 125}, {"content": "        public void setStartDate(LocalDate startDate) {", "lineIndex": 127}, {"content": "            if (startDate == null) {", "lineIndex": 128}, {"content": "                throw new IllegalArgumentException(\"Start date cannot be null\");", "lineIndex": 129}, {"content": "            }", "lineIndex": 130}, {"content": "            if (startDate.isAfter(LocalDate.now())) {", "lineIndex": 131}, {"content": "                throw new IllegalArgumentException(\"Start date cannot be in the future\");", "lineIndex": 132}, {"content": "            }", "lineIndex": 133}, {"content": "            this.startDate = startDate;", "lineIndex": 134}, {"content": "        }", "lineIndex": 135}], "sqlLines": [{"content": "    start_date DATE NOT NULL CHECK (start_date <= CURRENT_DATE),", "lineIndex": 32}], "type": "attribute", "color": "#3498DB", "tag": "A6", "level": "line"}, {"id": "mapping-line-019", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: endDate Key: Non-unique Data Type: Date Type: Mandatory Format: \"YYYY-MM-DD\" Values: N/A Default: N/A Validation: Date Check Error Message: \"End date must be after start date\" Description: Scheduled date for loan completion", "javaLines": [{"content": "        private LocalDate endDate;", "lineIndex": 39}, {"content": "        public LocalDate getEndDate() {", "lineIndex": 137}, {"content": "            return endDate;", "lineIndex": 138}, {"content": "        }", "lineIndex": 139}, {"content": "        public void setEndDate(LocalDate endDate) {", "lineIndex": 141}, {"content": "            if (endDate == null) {", "lineIndex": 142}, {"content": "                throw new IllegalArgumentException(\"End date cannot be null\");", "lineIndex": 143}, {"content": "            }", "lineIndex": 144}, {"content": "            if (this.startDate != null && !endDate.isAfter(this.startDate)) {", "lineIndex": 145}, {"content": "                throw new IllegalArgumentException(\"End date must be after start date\");", "lineIndex": 146}, {"content": "            }", "lineIndex": 147}, {"content": "            this.endDate = endDate;", "lineIndex": 148}, {"content": "        }", "lineIndex": 149}], "sqlLines": [{"content": "    end_date DATE NOT NULL CHECK (end_date > start_date),", "lineIndex": 33}], "type": "attribute", "color": "#3498DB", "tag": "A7", "level": "line"}, {"id": "mapping-line-020", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: status Key: Non-unique Data Type: Enum Type: Mandatory Format: N/A Values: \"Active\", \"Closed\", \"Default\", \"Restructured\" Default: \"Active\" Validation: List Check Error Message: \"Invalid loan status\" Description: Current status of the loan", "javaLines": [{"content": "        public enum Status {", "lineIndex": 21}, {"content": "            Active, Closed, Default, Restructured", "lineIndex": 22}, {"content": "        }", "lineIndex": 23}, {"content": "        private Status status = Status.Active;", "lineIndex": 40}, {"content": "        public Status getStatus() {", "lineIndex": 151}, {"content": "            return status;", "lineIndex": 152}, {"content": "        }", "lineIndex": 153}, {"content": "        public void setStatus(Status status) {", "lineIndex": 155}, {"content": "            if (status == null) {", "lineIndex": 156}, {"content": "                throw new IllegalArgumentException(\"Invalid loan status\");", "lineIndex": 157}, {"content": "            }", "lineIndex": 158}, {"content": "            this.status = status;", "lineIndex": 159}, {"content": "        }", "lineIndex": 160}], "sqlLines": [{"content": "CREATE TYPE loan_status AS ENUM ('ACTIVE', 'CLOSED', 'DEFAULT', 'RESTRUCTURED');", "lineIndex": 0}, {"content": "    status loan_status NOT NULL DEFAULT 'ACTIVE',", "lineIndex": 34}], "type": "attribute", "color": "#3498DB", "tag": "A8", "level": "line"}, {"id": "mapping-line-021", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: paymentFrequency Key: Non-unique Data Type: Enum Type: Mandatory Format: N/A Values: \"Monthly\", \"Biweekly\", \"Weekly\", \"Quarterly\", \"Annually\" Default: \"Monthly\" Validation: List Check Error Message: \"Invalid payment frequency\" Description: How often payments are made on the loan", "javaLines": [{"content": "        public enum PaymentFrequency {", "lineIndex": 25}, {"content": "            Monthly, Biweekly, Weekly, Quarterly, Annually", "lineIndex": 26}, {"content": "        }", "lineIndex": 27}, {"content": "        private PaymentFrequency paymentFrequency = PaymentFrequency.Monthly;", "lineIndex": 41}, {"content": "        public PaymentFrequency getPaymentFrequency() {", "lineIndex": 162}, {"content": "            return paymentFrequency;", "lineIndex": 163}, {"content": "        }", "lineIndex": 164}, {"content": "        public void setPaymentFrequency(PaymentFrequency paymentFrequency) {", "lineIndex": 166}, {"content": "            if (paymentFrequency == null) {", "lineIndex": 167}, {"content": "                throw new IllegalArgumentException(\"Invalid payment frequency\");", "lineIndex": 168}, {"content": "            }", "lineIndex": 169}, {"content": "            this.paymentFrequency = paymentFrequency;", "lineIndex": 170}, {"content": "        }", "lineIndex": 171}], "sqlLines": [{"content": "CREATE TYPE payment_frequency AS ENUM ('MONTHLY', 'BIWEEKLY', 'WEEKLY', 'QUARTERLY', 'ANNUALLY');", "lineIndex": 1}, {"content": "    payment_frequency payment_frequency NOT NULL DEFAULT 'MONTHLY',", "lineIndex": 35}], "type": "attribute", "color": "#3498DB", "tag": "A9", "level": "line"}, {"id": "mapping-line-022", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: totalPaymentsMade Key: Non-unique Data Type: Integer Type: Mandatory Format: \"###\" Values: 0-N Default: \"0\" Validation: Range Check Error Message: \"Total payments cannot be negative\" Description: Number of payments completed for this loan", "javaLines": [{"content": "        private Integer totalPaymentsMade = 0;", "lineIndex": 42}, {"content": "        public Integer getTotalPaymentsMade() {", "lineIndex": 173}, {"content": "            return totalPaymentsMade;", "lineIndex": 174}, {"content": "        }", "lineIndex": 175}, {"content": "        public void setTotalPaymentsMade(Integer totalPaymentsMade) {", "lineIndex": 177}, {"content": "            if (totalPaymentsMade == null || totalPaymentsMade < 0) {", "lineIndex": 178}, {"content": "                throw new IllegalArgumentException(\"Total payments cannot be negative\");", "lineIndex": 179}, {"content": "            }", "lineIndex": 180}, {"content": "            this.totalPaymentsMade = totalPaymentsMade;", "lineIndex": 181}, {"content": "        }", "lineIndex": 182}], "sqlLines": [{"content": "    total_payments_made INTEGER NOT NULL DEFAULT 0 CHECK (total_payments_made >= 0),", "lineIndex": 36}, {"content": "CREATE OR REPLACE FUNCTION update_loan_on_payment_completion()", "lineIndex": 86}, {"content": "        SET total_payments_made = total_payments_made + 1,", "lineIndex": 91}, {"content": "        SET total_payments_made = total_payments_made - 1,", "lineIndex": 96}], "type": "attribute", "color": "#3498DB", "tag": "A10", "level": "line"}, {"id": "mapping-line-023", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: remainingBalance Key: Non-unique Data Type: Decimal Type: Calculated Format: \"$#,###.00\" Values: 0.00-N Default: N/A Validation: Calculation Error Message: N/A Description: Current outstanding balance on the loan.", "javaLines": [{"content": "        private BigDecimal remainingBalance;", "lineIndex": 43}, {"content": "        public BigDecimal getRemainingBalance() {", "lineIndex": 184}, {"content": "            return remainingBalance;", "lineIndex": 185}, {"content": "        }", "lineIndex": 186}, {"content": "        protected void setRemainingBalance(BigDecimal remainingBalance) {", "lineIndex": 188}, {"content": "            this.remainingBalance = remainingBalance;", "lineIndex": 189}, {"content": "        }", "lineIndex": 190}], "sqlLines": [{"content": "CREATE OR REPLACE VIEW loan_balances AS", "lineIndex": 66}, {"content": "SELECT ", "lineIndex": 67}, {"content": "    l.loan_id,", "lineIndex": 68}, {"content": "    l.loan_amount,", "lineIndex": 69}, {"content": "    COALESCE(SUM(p.amount) FILTER (WHERE p.status = 'COMPLETED'), 0) as total_paid,", "lineIndex": 70}, {"content": "    (l.loan_amount - COALESCE(SUM(p.amount) FILTER (WHERE p.status = 'COMPLETED'), 0)) as remaining_balance", "lineIndex": 71}, {"content": "FROM ", "lineIndex": 72}, {"content": "    loans l", "lineIndex": 73}, {"content": "LEFT JOIN ", "lineIndex": 74}, {"content": "    payments p ON l.loan_id = p.loan_id", "lineIndex": 75}, {"content": "GROUP BY ", "lineIndex": 76}, {"content": "    l.loan_id, l.loan_amount;", "lineIndex": 77}], "type": "attribute", "color": "#2ECC71", "tag": "A11", "level": "line"}, {"id": "mapping-line-024", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: loanType Key: Non-unique Data Type: Enum Type: Mandatory Format: N/A Values: \"Personal\", \"Mortgage\", \"Auto\", \"Education\", \"Business\" Default: \"Personal\" Validation: List Check Error Message: \"Invalid loan type\" Description: Category of loan based on purpose", "javaLines": [{"content": "        public enum LoanType {", "lineIndex": 29}, {"content": "            Personal, Mortgage, Auto, Education, Business", "lineIndex": 30}, {"content": "        }", "lineIndex": 31}, {"content": "        private LoanType loanType = LoanType.Personal;", "lineIndex": 44}, {"content": "        public LoanType getLoanType() {", "lineIndex": 192}, {"content": "            return loanType;", "lineIndex": 193}, {"content": "        }", "lineIndex": 194}, {"content": "        public void setLoanType(LoanType loanType) {", "lineIndex": 196}, {"content": "            if (loanType == null) {", "lineIndex": 197}, {"content": "                throw new IllegalArgumentException(\"Invalid loan type\");", "lineIndex": 198}, {"content": "            }", "lineIndex": 199}, {"content": "            this.loanType = loanType;", "lineIndex": 200}, {"content": "        }", "lineIndex": 201}], "sqlLines": [{"content": "CREATE TYPE loan_type AS ENUM ('PER<PERSON><PERSON><PERSON>', 'MORTGA<PERSON>', 'AUTO', 'EDUCATION', 'BUSINESS');", "lineIndex": 2}, {"content": "    loan_type loan_type NOT NULL DEFAULT 'PERSONAL',", "lineIndex": 37}], "type": "attribute", "color": "#3498DB", "tag": "A12", "level": "line"}, {"id": "mapping-line-025", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: collateralId Key: Foreign Data Type: Integer Type: Optional Format: \"COL-######\" Values: N/A Default: N/A Validation: Foreign Key Check Error Message: \"Collateral ID must reference existing collateral\" Description: References the asset used to secure the loan", "javaLines": [{"content": "        private String collateralId;", "lineIndex": 45}, {"content": "        public String getCollateralId() {", "lineIndex": 203}, {"content": "            return collateralId;", "lineIndex": 204}, {"content": "        }", "lineIndex": 205}, {"content": "        public void setCollateralId(String collateralId) {", "lineIndex": 207}, {"content": "            this.collateralId = collateralId;", "lineIndex": 208}, {"content": "        }", "lineIndex": 209}], "sqlLines": [{"content": "    collateral_id VARCHAR(10) REFERENCES collaterals(collateral_id),", "lineIndex": 38}], "type": "attribute", "color": "#3498DB", "tag": "A13", "level": "line"}, {"id": "mapping-line-026", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: originationFee Key: Non-unique Data Type: Decimal Type: Optional Format: \"$#,###.00\" Values: 0.00-N Default: \"0.00\" Validation: Range Check Error Message: \"Origination fee cannot be negative\" Description: Fee charged for processing a new loan application", "javaLines": [{"content": "        private BigDecimal originationFee = BigDecimal.ZERO;", "lineIndex": 46}, {"content": "        public BigDecimal getOriginationFee() {", "lineIndex": 211}, {"content": "            return originationFee;", "lineIndex": 212}, {"content": "        }", "lineIndex": 213}, {"content": "        public void setOriginationFee(BigDecimal originationFee) {", "lineIndex": 215}, {"content": "            if (originationFee == null) {", "lineIndex": 216}, {"content": "                throw new IllegalArgumentException(\"Origination fee cannot be null\");", "lineIndex": 217}, {"content": "            }", "lineIndex": 218}, {"content": "            if (originationFee.compareTo(BigDecimal.ZERO) < 0) {", "lineIndex": 219}, {"content": "                throw new IllegalArgumentException(\"Origination fee cannot be negative\");", "lineIndex": 220}, {"content": "            }", "lineIndex": 221}, {"content": "            this.originationFee = originationFee;", "lineIndex": 222}, {"content": "        }", "lineIndex": 223}], "sqlLines": [{"content": "    origination_fee DECIMAL(10,2) DEFAULT 0.00 CHECK (origination_fee >= 0),", "lineIndex": 39}], "type": "attribute", "color": "#3498DB", "tag": "A14", "level": "line"}, {"id": "mapping-line-027", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: lateFeePercentage Key: Non-unique Data Type: Decimal Type: Optional Format: \"0.00%\" Values: 0.00-20.00 Default: \"5.00\" Validation: Range Check Error Message: \"Late fee percentage must be between 0% and 20%\" Description: Percentage charged on late payments", "javaLines": [{"content": "        private static final BigDecimal MIN_LATE_FEE = BigDecimal.ZERO;", "lineIndex": 16}, {"content": "        private static final BigDecimal MAX_LATE_FEE = new BigDecimal(\"20.00\");", "lineIndex": 17}, {"content": "        private BigDecimal lateFeePercentage = new BigDecimal(\"5.00\");", "lineIndex": 47}, {"content": "        public BigDecimal getLateFeePercentage() {", "lineIndex": 225}, {"content": "            return lateFeePercentage;", "lineIndex": 226}, {"content": "        }", "lineIndex": 227}, {"content": "        public void setLateFeePercentage(BigDecimal lateFeePercentage) {", "lineIndex": 229}, {"content": "            if (lateFeePercentage == null) {", "lineIndex": 230}, {"content": "                throw new IllegalArgumentException(\"Late fee percentage cannot be null\");", "lineIndex": 231}, {"content": "            }", "lineIndex": 232}, {"content": "            if (lateFeePercentage.compareTo(MIN_LATE_FEE) < 0 || lateFeePercentage.compareTo(MAX_LATE_FEE) > 0) {", "lineIndex": 233}, {"content": "                throw new IllegalArgumentException(\"Late fee percentage must be between 0% and 20%\");", "lineIndex": 234}, {"content": "            }", "lineIndex": 235}, {"content": "            this.lateFeePercentage = lateFeePercentage;", "lineIndex": 236}, {"content": "        }", "lineIndex": 237}], "sqlLines": [{"content": "    late_fee_percentage DECIMAL(5,2) DEFAULT 5.00 CHECK (late_fee_percentage BETWEEN 0 AND 20),", "lineIndex": 40}], "type": "attribute", "color": "#3498DB", "tag": "A15", "level": "line"}, {"id": "mapping-line-028", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: earlyPaymentPenalty Key: Non-unique Data Type: Decimal Type: Optional Format: \"0.00%\" Values: 0.00-10.00 Default: \"1.00\" Validation: Range Check Error Message: \"Early payment penalty must be between 0% and 10%\" Description: Penalty charged for early loan payoff", "javaLines": [{"content": "        private static final BigDecimal MIN_EARLY_PAYMENT_PENALTY = BigDecimal.ZERO;", "lineIndex": 18}, {"content": "        private static final BigDecimal MAX_EARLY_PAYMENT_PENALTY = new BigDecimal(\"10.00\");", "lineIndex": 19}, {"content": "        private BigDecimal earlyPaymentPenalty = new BigDecimal(\"1.00\");", "lineIndex": 48}, {"content": "        public BigDecimal getEarlyPaymentPenalty() {", "lineIndex": 239}, {"content": "            return earlyPaymentPenalty;", "lineIndex": 240}, {"content": "        }", "lineIndex": 241}, {"content": "        public void setEarlyPaymentPenalty(BigDecimal earlyPaymentPenalty) {", "lineIndex": 243}, {"content": "            if (earlyPaymentPenalty == null) {", "lineIndex": 244}, {"content": "                throw new IllegalArgumentException(\"Early payment penalty cannot be null\");", "lineIndex": 245}, {"content": "            }", "lineIndex": 246}, {"content": "            if (earlyPaymentPenalty.compareTo(MIN_EARLY_PAYMENT_PENALTY) < 0 || ", "lineIndex": 247}, {"content": "                earlyPaymentPenalty.compareTo(MAX_EARLY_PAYMENT_PENALTY) > 0) {", "lineIndex": 248}, {"content": "                throw new IllegalArgumentException(\"Early payment penalty must be between 0% and 10%\");", "lineIndex": 249}, {"content": "            }", "lineIndex": 250}, {"content": "            this.earlyPaymentPenalty = earlyPaymentPenalty;", "lineIndex": 251}, {"content": "        }", "lineIndex": 252}], "sqlLines": [{"content": "    early_payment_penalty DECIMAL(5,2) DEFAULT 1.00 CHECK (early_payment_penalty BETWEEN 0 AND 10),", "lineIndex": 41}], "type": "attribute", "color": "#3498DB", "tag": "A16", "level": "line"}, {"id": "mapping-line-029", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Relationship: Loan to Customer", "javaLines": [{"content": "        private Customer customer;", "lineIndex": 50}, {"content": "        public Customer getCustomer() {", "lineIndex": 254}, {"content": "            return customer;", "lineIndex": 255}, {"content": "        }", "lineIndex": 256}, {"content": "        public void setCustomer(Customer customer) {", "lineIndex": 258}, {"content": "            this.customer = customer;", "lineIndex": 259}, {"content": "            if (customer != null) {", "lineIndex": 260}, {"content": "                this.customerId = customer.getCustomerId();", "lineIndex": 261}, {"content": "            }", "lineIndex": 262}, {"content": "        }", "lineIndex": 263}], "sqlLines": [{"content": "    customer_id VARCHAR(10) NOT NULL REFERENCES customers(customer_id),", "lineIndex": 28}], "type": "relationship", "color": "#9B59B6", "tag": "R9", "level": "line"}, {"id": "mapping-line-030", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Relationship Properties: On Delete: Restrict (Prevent deletion of customer with active loans) On Update: Cascade (Update loan records when customer details change) Foreign Key Type: Non-Nullable", "javaLines": [{"content": "        public void setCustomer(Customer customer) {", "lineIndex": 258}, {"content": "            this.customer = customer;", "lineIndex": 259}, {"content": "            if (customer != null) {", "lineIndex": 260}, {"content": "                this.customerId = customer.getCustomerId();", "lineIndex": 261}, {"content": "            }", "lineIndex": 262}, {"content": "        }", "lineIndex": 263}], "sqlLines": [{"content": "    customer_id VARCHAR(10) NOT NULL REFERENCES customers(customer_id),", "lineIndex": 28}], "type": "relationship_property", "color": "#8E44AD", "tag": "RP1", "level": "line"}, {"id": "mapping-line-031", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Relationship: <PERSON><PERSON> to Collateral", "javaLines": [{"content": "        private Collateral collateral;", "lineIndex": 51}, {"content": "        public Collateral getCollateral() {", "lineIndex": 265}, {"content": "            return collateral;", "lineIndex": 266}, {"content": "        }", "lineIndex": 267}, {"content": "        public void setCollateral(Collateral collateral) {", "lineIndex": 269}, {"content": "            if (collateral != null && this.customer != null && ", "lineIndex": 270}, {"content": "                !collateral.getCustomerId().equals(this.customerId)) {", "lineIndex": 271}, {"content": "                throw new IllegalArgumentException(\"Collateral must belong to selected Customer\");", "lineIndex": 272}, {"content": "            }", "lineIndex": 273}, {"content": "            this.collateral = collateral;", "lineIndex": 275}, {"content": "            if (collateral != null) {", "lineIndex": 276}, {"content": "                this.collateralId = collateral.getCollateralId();", "lineIndex": 277}, {"content": "            }", "lineIndex": 278}, {"content": "        }", "lineIndex": 279}], "sqlLines": [{"content": "    collateral_id VARCHAR(10) REFERENCES collaterals(collateral_id),", "lineIndex": 38}], "type": "relationship", "color": "#9B59B6", "tag": "R10", "level": "line"}, {"id": "mapping-line-032", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Relationship Properties: On Delete: Set Null (Remove loan reference when collateral is deleted) On Update: Cascade (Update loan records when collateral details change) Foreign Key Type: Nullable", "javaLines": [{"content": "        public void setCollateral(Collateral collateral) {", "lineIndex": 269}, {"content": "            if (collateral != null && this.customer != null && ", "lineIndex": 270}, {"content": "                !collateral.getCustomerId().equals(this.customerId)) {", "lineIndex": 271}, {"content": "                throw new IllegalArgumentException(\"Collateral must belong to selected Customer\");", "lineIndex": 272}, {"content": "            }", "lineIndex": 273}, {"content": "            this.collateral = collateral;", "lineIndex": 275}, {"content": "            if (collateral != null) {", "lineIndex": 276}, {"content": "                this.collateralId = collateral.getCollateralId();", "lineIndex": 277}, {"content": "            }", "lineIndex": 278}, {"content": "        }", "lineIndex": 279}], "sqlLines": [{"content": "    collateral_id VARCHAR(10) REFERENCES collaterals(collateral_id),", "lineIndex": 38}], "type": "relationship_property", "color": "#8E44AD", "tag": "RP2", "level": "line"}, {"id": "customer-entity-main", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Customer has customerId^PK, firstName, lastName, email, phone, address, city, state, zipCode, country, dateOfBirth, ssn, creditScore, annualIncome, employmentStatus (Employed, Self-Employed, Unemployed, Retired), employerName, employmentLength, customerType (Individual, Business), status (Active, Inactive, Suspended).", "javaLines": [{"content": "    public static class Customer {", "lineIndex": 301}], "sqlLines": [{"content": "CREATE TABLE customers (", "lineIndex": 6}], "type": "other", "color": "#F39C12", "tag": "E2", "level": "line", "children": [{"id": "child-customer-customerId", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "customerId^PK", "javaLines": [{"content": "        private String customerId;", "lineIndex": 308}, {"content": "        public String getCustomerId() {", "lineIndex": 317}, {"content": "            return customerId;", "lineIndex": 318}, {"content": "        }", "lineIndex": 319}, {"content": "        public void setCustomerId(String customerId) {", "lineIndex": 321}, {"content": "            this.customerId = customerId;", "lineIndex": 328}, {"content": "        }", "lineIndex": 329}], "sqlLines": [{"content": "    customer_id VARCHAR(10) PRIMARY KEY CHECK (customer_id ~ '^CUS-[0-9]{6}$'),", "lineIndex": 7}], "type": "other", "color": "#3498DB", "tag": "", "level": "word", "parentId": "customer-entity-main"}, {"id": "child-customer-first<PERSON>ame", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "firstName", "javaLines": [{"content": "        private String firstName;", "lineIndex": 309}, {"content": "        public String getFirstName() {", "lineIndex": 331}, {"content": "            return firstName;", "lineIndex": 332}, {"content": "        }", "lineIndex": 333}, {"content": "        public void setFirstName(String firstName) {", "lineIndex": 335}, {"content": "            this.firstName = firstName;", "lineIndex": 342}, {"content": "        }", "lineIndex": 343}], "sqlLines": [{"content": "    first_name VARCHAR(50) NOT NULL,", "lineIndex": 8}], "type": "other", "color": "#3498DB", "tag": "", "level": "word", "parentId": "customer-entity-main"}, {"id": "child-customer-last<PERSON><PERSON>", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "lastName", "javaLines": [{"content": "        private String lastName;", "lineIndex": 310}, {"content": "        public String getLastName() {", "lineIndex": 345}, {"content": "            return lastName;", "lineIndex": 346}, {"content": "        }", "lineIndex": 347}, {"content": "        public void setLastName(String lastName) {", "lineIndex": 349}, {"content": "            this.lastName = lastName;", "lineIndex": 356}, {"content": "        }", "lineIndex": 357}], "sqlLines": [{"content": "    last_name VARCHAR(50) NOT NULL,", "lineIndex": 9}], "type": "other", "color": "#3498DB", "tag": "", "level": "word", "parentId": "customer-entity-main"}, {"id": "child-customer-email", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "email", "javaLines": [{"content": "        private String email;", "lineIndex": 311}, {"content": "        public String getEmail() {", "lineIndex": 359}, {"content": "            return email;", "lineIndex": 360}, {"content": "        }", "lineIndex": 361}, {"content": "        public void setEmail(String email) {", "lineIndex": 363}, {"content": "            this.email = email;", "lineIndex": 370}, {"content": "        }", "lineIndex": 371}], "sqlLines": [{"content": "    email VARCHAR(100) NOT NULL UNIQUE,", "lineIndex": 10}], "type": "other", "color": "#3498DB", "tag": "", "level": "word", "parentId": "customer-entity-main"}, {"id": "child-customer-phone", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "phone", "javaLines": [{"content": "        private String phone;", "lineIndex": 312}, {"content": "        public String getPhone() {", "lineIndex": 373}, {"content": "            return phone;", "lineIndex": 374}, {"content": "        }", "lineIndex": 375}, {"content": "        public void setPhone(String phone) {", "lineIndex": 377}, {"content": "            this.phone = phone;", "lineIndex": 384}, {"content": "        }", "lineIndex": 385}], "sqlLines": [{"content": "    phone VARCHAR(20) NOT NULL CHECK (phone ~ '^\\\\+?[0-9]{10,15}$'),", "lineIndex": 11}], "type": "other", "color": "#3498DB", "tag": "", "level": "word", "parentId": "customer-entity-main"}, {"id": "child-customer-address", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "address", "javaLines": [], "sqlLines": [], "type": "other", "color": "#E74C3C", "tag": "MISSING", "level": "word", "parentId": "customer-entity-main"}, {"id": "child-customer-city", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "city", "javaLines": [], "sqlLines": [], "type": "other", "color": "#E74C3C", "tag": "MISSING", "level": "word", "parentId": "customer-entity-main"}, {"id": "child-customer-state", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "state", "javaLines": [], "sqlLines": [], "type": "other", "color": "#E74C3C", "tag": "MISSING", "level": "word", "parentId": "customer-entity-main"}, {"id": "child-customer-zipCode", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "zipCode", "javaLines": [], "sqlLines": [], "type": "other", "color": "#E74C3C", "tag": "MISSING", "level": "word", "parentId": "customer-entity-main"}, {"id": "child-customer-country", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "country", "javaLines": [], "sqlLines": [], "type": "other", "color": "#E74C3C", "tag": "MISSING", "level": "word", "parentId": "customer-entity-main"}, {"id": "child-customer-date<PERSON><PERSON><PERSON><PERSON>h", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "dateOfBirth", "javaLines": [], "sqlLines": [], "type": "other", "color": "#E74C3C", "tag": "MISSING", "level": "word", "parentId": "customer-entity-main"}, {"id": "child-customer-ssn", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "ssn", "javaLines": [], "sqlLines": [], "type": "other", "color": "#E74C3C", "tag": "MISSING", "level": "word", "parentId": "customer-entity-main"}, {"id": "child-customer-creditScore", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "creditScore", "javaLines": [], "sqlLines": [], "type": "other", "color": "#E74C3C", "tag": "MISSING", "level": "word", "parentId": "customer-entity-main"}, {"id": "child-customer-annualIncome", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "annualIncome", "javaLines": [], "sqlLines": [], "type": "other", "color": "#E74C3C", "tag": "MISSING", "level": "word", "parentId": "customer-entity-main"}, {"id": "child-customer-employmentStatus", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "employmentStatus (Employed, Self-Employed, Unemployed, Retired)", "javaLines": [], "sqlLines": [], "type": "other", "color": "#E74C3C", "tag": "MISSING", "level": "word", "parentId": "customer-entity-main"}, {"id": "child-customer-employer<PERSON>ame", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "employerName", "javaLines": [], "sqlLines": [], "type": "other", "color": "#E74C3C", "tag": "MISSING", "level": "word", "parentId": "customer-entity-main"}, {"id": "child-customer-employmentLength", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "employmentLength", "javaLines": [], "sqlLines": [], "type": "other", "color": "#E74C3C", "tag": "MISSING", "level": "word", "parentId": "customer-entity-main"}, {"id": "child-customer-customerType", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "customerType (Individual, Business)", "javaLines": [], "sqlLines": [], "type": "other", "color": "#E74C3C", "tag": "MISSING", "level": "word", "parentId": "customer-entity-main"}, {"id": "child-customer-status", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "status (Active, Inactive, Suspended)", "javaLines": [], "sqlLines": [], "type": "other", "color": "#E74C3C", "tag": "MISSING", "level": "word", "parentId": "customer-entity-main"}]}, {"id": "mapping-line-035", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Customer has one-to-many relationship with <PERSON>an using Customer.customerId to Loan.customerId^FK", "javaLines": [], "sqlLines": [{"content": "    customer_id VARCHAR(10) NOT NULL REFERENCES customers(customer_id),", "lineIndex": 28}], "type": "relationship", "color": "#9B59B6", "tag": "R11", "level": "line"}, {"id": "mapping-line-036", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Customer has one-to-many relationship with Collateral using Customer.customerId to Collateral.customerId^FK", "javaLines": [], "sqlLines": [{"content": "    customer_id VARCHAR(10) NOT NULL REFERENCES customers(customer_id),", "lineIndex": 18}], "type": "relationship", "color": "#9B59B6", "tag": "R12", "level": "line"}, {"id": "mapping-line-038", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Customer.customerId must be unique", "javaLines": [{"content": "        private static final Pattern CUSTOMER_ID_PATTERN = Pattern.compile(\"^CUS-[0-9]{6}$\");", "lineIndex": 302}, {"content": "        public void setCustomerId(String customerId) {", "lineIndex": 321}, {"content": "            if (customerId == null || customerId.isEmpty()) {", "lineIndex": 322}, {"content": "                throw new IllegalArgumentException(\"Customer ID cannot be null or empty\");", "lineIndex": 323}, {"content": "            }", "lineIndex": 324}, {"content": "            if (!CUSTOMER_ID_PATTERN.matcher(customerId).matches()) {", "lineIndex": 325}, {"content": "                throw new IllegalArgumentException(\"Invalid Customer ID format\");", "lineIndex": 326}, {"content": "            }", "lineIndex": 327}, {"content": "            this.customerId = customerId;", "lineIndex": 328}, {"content": "        }", "lineIndex": 329}], "sqlLines": [{"content": "    customer_id VARCHAR(10) PRIMARY KEY CHECK (customer_id ~ '^CUS-[0-9]{6}$'),", "lineIndex": 7}], "type": "validation", "color": "#E74C3C", "tag": "V7", "level": "line"}, {"id": "mapping-line-039", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Customer.email must be a valid email format", "javaLines": [{"content": "        private static final Pattern EMAIL_PATTERN = Pattern.compile(", "lineIndex": 304}, {"content": "            \"^[a-zA-Z0-9_+&*-]+(?:\\\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\\\.)+[a-zA-Z]{2,7}$\");", "lineIndex": 305}, {"content": "        public void setEmail(String email) {", "lineIndex": 363}, {"content": "            if (email == null || email.trim().isEmpty()) {", "lineIndex": 364}, {"content": "                throw new IllegalArgumentException(\"Email cannot be null or empty\");", "lineIndex": 365}, {"content": "            }", "lineIndex": 366}, {"content": "            if (!EMAIL_PATTERN.matcher(email).matches()) {", "lineIndex": 367}, {"content": "                throw new IllegalArgumentException(\"Invalid email format\");", "lineIndex": 368}, {"content": "            }", "lineIndex": 369}, {"content": "            this.email = email;", "lineIndex": 370}, {"content": "        }", "lineIndex": 371}], "sqlLines": [{"content": "    email VARCHAR(100) NOT NULL UNIQUE,", "lineIndex": 10}], "type": "validation", "color": "#E74C3C", "tag": "V8", "level": "line"}, {"id": "mapping-line-040", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Customer.ssn must be a valid format and unique", "javaLines": [], "sqlLines": [], "type": "validation", "color": "#E74C3C", "tag": "V9", "level": "line"}, {"id": "mapping-line-041", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Customer.creditScore must be between 300 and 850", "javaLines": [], "sqlLines": [], "type": "validation", "color": "#E74C3C", "tag": "V10", "level": "line"}, {"id": "mapping-line-042", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Customer.annualIncome must be greater than 0", "javaLines": [], "sqlLines": [], "type": "validation", "color": "#E74C3C", "tag": "V11", "level": "line"}, {"id": "mapping-line-043", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Customer.dateOfBirth must indicate customer is at least 18 years old", "javaLines": [], "sqlLines": [], "type": "validation", "color": "#E74C3C", "tag": "V12", "level": "line"}, {"id": "mapping-line-044", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: customerId Key: Primary Data Type: Integer Type: Mandatory Format: \"CUS-######\" Values: N/A Default: N/A Validation: Auto-increment Error Message: \"Invalid Customer ID format\" Description: Unique identifier for the customer", "javaLines": [{"content": "        private static final Pattern CUSTOMER_ID_PATTERN = Pattern.compile(\"^CUS-[0-9]{6}$\");", "lineIndex": 302}, {"content": "        private String customerId;", "lineIndex": 308}, {"content": "        public String getCustomerId() {", "lineIndex": 317}, {"content": "            return customerId;", "lineIndex": 318}, {"content": "        }", "lineIndex": 319}, {"content": "        public void setCustomerId(String customerId) {", "lineIndex": 321}, {"content": "            if (customerId == null || customerId.isEmpty()) {", "lineIndex": 322}, {"content": "                throw new IllegalArgumentException(\"Customer ID cannot be null or empty\");", "lineIndex": 323}, {"content": "            }", "lineIndex": 324}, {"content": "            if (!CUSTOMER_ID_PATTERN.matcher(customerId).matches()) {", "lineIndex": 325}, {"content": "                throw new IllegalArgumentException(\"Invalid Customer ID format\");", "lineIndex": 326}, {"content": "            }", "lineIndex": 327}, {"content": "            this.customerId = customerId;", "lineIndex": 328}, {"content": "        }", "lineIndex": 329}], "sqlLines": [{"content": "    customer_id VARCHAR(10) PRIMARY KEY CHECK (customer_id ~ '^CUS-[0-9]{6}$'),", "lineIndex": 7}], "type": "attribute", "color": "#3498DB", "tag": "A17", "level": "line"}, {"id": "mapping-line-045", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: firstName Key: Non-unique Data Type: String Type: Mandatory Format: N/A Values: N/A Default: N/A Validation: Length Check Error Message: \"First name is required\" Description: Customer's first name", "javaLines": [{"content": "        private static final int MAX_NAME_LENGTH = 50;", "lineIndex": 303}, {"content": "        private String firstName;", "lineIndex": 309}, {"content": "        public String getFirstName() {", "lineIndex": 331}, {"content": "            return firstName;", "lineIndex": 332}, {"content": "        }", "lineIndex": 333}, {"content": "        public void setFirstName(String firstName) {", "lineIndex": 335}, {"content": "            if (firstName == null || firstName.trim().isEmpty()) {", "lineIndex": 336}, {"content": "                throw new IllegalArgumentException(\"First name cannot be null or empty\");", "lineIndex": 337}, {"content": "            }", "lineIndex": 338}, {"content": "            if (firstName.length() > MAX_NAME_LENGTH) {", "lineIndex": 339}, {"content": "                throw new IllegalArgumentException(\"First name cannot exceed 50 characters\");", "lineIndex": 340}, {"content": "            }", "lineIndex": 341}, {"content": "            this.firstName = firstName;", "lineIndex": 342}, {"content": "        }", "lineIndex": 343}], "sqlLines": [{"content": "    first_name VARCHAR(50) NOT NULL,", "lineIndex": 8}], "type": "attribute", "color": "#3498DB", "tag": "A18", "level": "line"}, {"id": "mapping-line-046", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: lastName Key: Non-unique Data Type: String Type: Mandatory Format: N/A Values: N/A Default: N/A Validation: Length Check Error Message: \"Last name is required\" Description: Customer's last name", "javaLines": [{"content": "        private String lastName;", "lineIndex": 310}, {"content": "        public String getLastName() {", "lineIndex": 345}, {"content": "            return lastName;", "lineIndex": 346}, {"content": "        }", "lineIndex": 347}, {"content": "        public void setLastName(String lastName) {", "lineIndex": 349}, {"content": "            if (lastName == null || lastName.trim().isEmpty()) {", "lineIndex": 350}, {"content": "                throw new IllegalArgumentException(\"Last name cannot be null or empty\");", "lineIndex": 351}, {"content": "            }", "lineIndex": 352}, {"content": "            if (lastName.length() > MAX_NAME_LENGTH) {", "lineIndex": 353}, {"content": "                throw new IllegalArgumentException(\"Last name cannot exceed 50 characters\");", "lineIndex": 354}, {"content": "            }", "lineIndex": 355}, {"content": "            this.lastName = lastName;", "lineIndex": 356}, {"content": "        }", "lineIndex": 357}], "sqlLines": [{"content": "    last_name VARCHAR(50) NOT NULL,", "lineIndex": 9}], "type": "attribute", "color": "#3498DB", "tag": "A19", "level": "line"}, {"id": "mapping-line-047", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: email Key: Non-unique Data Type: String Type: Mandatory Format: \"<EMAIL>\" Values: N/A Default: N/A Validation: Format Check Error Message: \"Invalid email format\" Description: Customer's email address", "javaLines": [{"content": "        private static final Pattern EMAIL_PATTERN = Pattern.compile(", "lineIndex": 304}, {"content": "            \"^[a-zA-Z0-9_+&*-]+(?:\\\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\\\.)+[a-zA-Z]{2,7}$\");", "lineIndex": 305}, {"content": "        private String email;", "lineIndex": 311}, {"content": "        public String getEmail() {", "lineIndex": 359}, {"content": "            return email;", "lineIndex": 360}, {"content": "        }", "lineIndex": 361}, {"content": "        public void setEmail(String email) {", "lineIndex": 363}, {"content": "            if (email == null || email.trim().isEmpty()) {", "lineIndex": 364}, {"content": "                throw new IllegalArgumentException(\"Email cannot be null or empty\");", "lineIndex": 365}, {"content": "            }", "lineIndex": 366}, {"content": "            if (!EMAIL_PATTERN.matcher(email).matches()) {", "lineIndex": 367}, {"content": "                throw new IllegalArgumentException(\"Invalid email format\");", "lineIndex": 368}, {"content": "            }", "lineIndex": 369}, {"content": "            this.email = email;", "lineIndex": 370}, {"content": "        }", "lineIndex": 371}], "sqlLines": [{"content": "    email VARCHAR(100) NOT NULL UNIQUE,", "lineIndex": 10}], "type": "attribute", "color": "#3498DB", "tag": "A20", "level": "line"}, {"id": "mapping-line-048", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: phone Key: Non-unique Data Type: String Type: Mandatory Format: \"+#-###-###-####\" Values: N/A Default: N/A Validation: Format Check Error Message: \"Invalid phone number format\" Description: Customer's contact phone number", "javaLines": [{"content": "        private static final Pattern PHONE_PATTERN = Pattern.compile(\"^\\\\+?[0-9]{10,15}$\");", "lineIndex": 306}, {"content": "        private String phone;", "lineIndex": 312}, {"content": "        public String getPhone() {", "lineIndex": 373}, {"content": "            return phone;", "lineIndex": 374}, {"content": "        }", "lineIndex": 375}, {"content": "        public void setPhone(String phone) {", "lineIndex": 377}, {"content": "            if (phone == null || phone.trim().isEmpty()) {", "lineIndex": 378}, {"content": "                throw new IllegalArgumentException(\"Phone cannot be null or empty\");", "lineIndex": 379}, {"content": "            }", "lineIndex": 380}, {"content": "            if (!PHONE_PATTERN.matcher(phone).matches()) {", "lineIndex": 381}, {"content": "                throw new IllegalArgumentException(\"Invalid phone number format\");", "lineIndex": 382}, {"content": "            }", "lineIndex": 383}, {"content": "            this.phone = phone;", "lineIndex": 384}, {"content": "        }", "lineIndex": 385}], "sqlLines": [{"content": "    phone VARCHAR(20) NOT NULL CHECK (phone ~ '^\\\\+?[0-9]{10,15}$'),", "lineIndex": 11}], "type": "attribute", "color": "#3498DB", "tag": "A21", "level": "line"}, {"id": "mapping-line-049", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: address Key: Non-unique Data Type: String Type: Mandatory Format: N/A Values: N/A Default: N/A Validation: Length Check Error Message: \"Address is required\" Description: Customer's street address", "javaLines": [], "sqlLines": [], "type": "attribute", "color": "#3498DB", "tag": "A22", "level": "line"}, {"id": "mapping-line-050", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: city Key: Non-unique Data Type: String Type: Mandatory Format: N/A Values: N/A Default: N/A Validation: Length Check Error Message: \"City is required\" Description: Customer's city of residence", "javaLines": [], "sqlLines": [], "type": "attribute", "color": "#3498DB", "tag": "A23", "level": "line"}, {"id": "mapping-line-051", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: state Key: Non-unique Data Type: String Type: Mandatory Format: N/A Values: N/A Default: N/A Validation: Length Check Error Message: \"State is required\" Description: Customer's state of residence", "javaLines": [], "sqlLines": [], "type": "attribute", "color": "#3498DB", "tag": "A24", "level": "line"}, {"id": "mapping-line-052", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: zipCode Key: Non-unique Data Type: String Type: Mandatory Format: \"#####\" or \"#####-####\" Values: N/A Default: N/A Validation: Format Check Error Message: \"Invalid zip code format\" Description: Customer's zip code", "javaLines": [], "sqlLines": [], "type": "attribute", "color": "#3498DB", "tag": "A25", "level": "line"}, {"id": "mapping-line-053", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: country Key: Non-unique Data Type: String Type: Mandatory Format: N/A Values: N/A Default: \"USA\" Validation: Length Check Error Message: \"Country is required\" Description: Customer's country of residence", "javaLines": [], "sqlLines": [], "type": "attribute", "color": "#3498DB", "tag": "A26", "level": "line"}, {"id": "mapping-line-054", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: dateOfBirth Key: Non-unique Data Type: Date Type: Mandatory Format: \"YYYY-MM-DD\" Values: N/A Default: N/A Validation: Age Check Error Message: \"Customer must be at least 18 years old\" Description: Customer's date of birth", "javaLines": [], "sqlLines": [], "type": "attribute", "color": "#3498DB", "tag": "A27", "level": "line"}, {"id": "mapping-line-055", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: ssn Key: Non-unique Data Type: String Type: Mandatory Format: \"###-##-####\" Values: N/A Default: N/A Validation: Format Check Error Message: \"Invalid SSN format\" Description: Customer's Social Security Number", "javaLines": [], "sqlLines": [], "type": "attribute", "color": "#3498DB", "tag": "A28", "level": "line"}, {"id": "mapping-line-056", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: creditScore Key: Non-unique Data Type: Integer Type: Mandatory Format: \"###\" Values: 300-850 Default: N/A Validation: Range Check Error Message: \"Credit score must be between 300 and 850\" Description: Customer's credit score", "javaLines": [], "sqlLines": [], "type": "attribute", "color": "#3498DB", "tag": "A29", "level": "line"}, {"id": "mapping-line-057", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: annualIncome Key: Non-unique Data Type: Decimal Type: Mandatory Format: \"$#,###.00\" Values: 0.00-N Default: N/A Validation: Range Check Error Message: \"Annual income must be greater than 0\" Description: Customer's annual income", "javaLines": [], "sqlLines": [], "type": "attribute", "color": "#3498DB", "tag": "A30", "level": "line"}, {"id": "mapping-line-058", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: employmentStatus Key: Non-unique Data Type: Enum Type: Mandatory Format: N/A Values: \"Employed\", \"Self-Employed\", \"Unemployed\", \"Retired\" Default: \"Employed\" Validation: List Check Error Message: \"Invalid employment status\" Description: Customer's current employment status", "javaLines": [], "sqlLines": [], "type": "attribute", "color": "#3498DB", "tag": "A31", "level": "line"}, {"id": "mapping-line-059", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: employerName Key: Non-unique Data Type: String Type: Optional Format: N/A Values: N/A Default: N/A Validation: Length Check Error Message: N/A Description: Name of customer's employer", "javaLines": [], "sqlLines": [], "type": "attribute", "color": "#3498DB", "tag": "A32", "level": "line"}, {"id": "mapping-line-060", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: employmentLength Key: Non-unique Data Type: Integer Type: Optional Format: \"## years\" Values: 0-N Default: \"0\" Validation: Range Check Error Message: \"Employment length cannot be negative\" Description: Length of employment in years", "javaLines": [], "sqlLines": [], "type": "attribute", "color": "#3498DB", "tag": "A33", "level": "line"}, {"id": "mapping-line-061", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: customerType Key: Non-unique Data Type: Enum Type: Mandatory Format: N/A Values: \"Individual\", \"Business\" Default: \"Individual\" Validation: List Check Error Message: \"Invalid customer type\" Description: Type of customer", "javaLines": [], "sqlLines": [], "type": "attribute", "color": "#3498DB", "tag": "A34", "level": "line"}, {"id": "mapping-line-062", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: status Key: Non-unique Data Type: Enum Type: Mandatory Format: N/A Values: \"Active\", \"Inactive\", \"Suspended\" Default: \"Active\" Validation: List Check Error Message: \"Invalid status\" Description: Current status of the customer account", "javaLines": [], "sqlLines": [], "type": "attribute", "color": "#3498DB", "tag": "A35", "level": "line"}, {"id": "mapping-line-063", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Relationship: Customer to <PERSON>an", "javaLines": [], "sqlLines": [{"content": "    customer_id VARCHAR(10) NOT NULL REFERENCES customers(customer_id),", "lineIndex": 28}], "type": "relationship", "color": "#9B59B6", "tag": "R13", "level": "line"}, {"id": "mapping-line-064", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Relationship Properties: On Delete: Restrict (Prevent deletion of customer with active loans) On Update: Cascade (Update loan records when customer details change) Foreign Key Type: Non-Nullable", "javaLines": [], "sqlLines": [{"content": "    customer_id VARCHAR(10) NOT NULL REFERENCES customers(customer_id),", "lineIndex": 28}], "type": "relationship_property", "color": "#8E44AD", "tag": "RP3", "level": "line"}, {"id": "collateral-entity-main", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Collateral has collateralId^PK, customerId^FK, loanId^FK, collateralType (RealEstate, Vehicle, Investment, Cash, Other), description, value, appraisalDate, appraisalValue, lienStatus (FirstLien, SecondLien, NoLien), condition (Excellent, Good, Fair, Poor), assetAddress, assetCity, assetState, assetZipCode, insuranceProvider, insurancePolicyNumber, insuranceExpirationDate, documentationComplete (true, false).", "javaLines": [{"content": "    public static class Collateral {", "lineIndex": 392}, {"content": "        public enum CollateralType {", "lineIndex": 395}, {"content": "            REAL_ESTATE, VEHICLE, INVESTMENT, SAVINGS, OTHER", "lineIndex": 396}, {"content": "        }", "lineIndex": 397}], "sqlLines": [{"content": "CREATE TYPE collateral_type AS ENUM ('REAL_ESTATE', 'VEHICLE', 'INVESTMENT', 'SAVINGS', 'OTHER');", "lineIndex": 3}, {"content": "CREATE TABLE collaterals (", "lineIndex": 16}], "type": "other", "color": "#F39C12", "tag": "E3", "level": "line", "children": [{"id": "child-collateral-collateralId", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "collateralId^PK", "javaLines": [{"content": "        private String collateralId;", "lineIndex": 399}, {"content": "        public String getCollateralId() {", "lineIndex": 408}, {"content": "            return collateralId;", "lineIndex": 409}, {"content": "        }", "lineIndex": 410}, {"content": "        public void setCollateralId(String collateralId) {", "lineIndex": 412}, {"content": "            this.collateralId = collateralId;", "lineIndex": 419}, {"content": "        }", "lineIndex": 420}], "sqlLines": [{"content": "    collateral_id VARCHAR(10) PRIMARY KEY CHECK (collateral_id ~ '^COL-[0-9]{6}$'),", "lineIndex": 17}], "type": "other", "color": "#3498DB", "tag": "", "level": "word", "parentId": "collateral-entity-main"}, {"id": "child-collateral-customerId", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "customerId^FK", "javaLines": [{"content": "        private String customerId;", "lineIndex": 400}, {"content": "        public String getCustomerId() {", "lineIndex": 422}, {"content": "            return customerId;", "lineIndex": 423}, {"content": "        }", "lineIndex": 424}, {"content": "        public void setCustomerId(String customerId) {", "lineIndex": 426}, {"content": "            this.customerId = customerId;", "lineIndex": 430}, {"content": "        }", "lineIndex": 431}], "sqlLines": [{"content": "    customer_id VARCHAR(10) NOT NULL REFERENCES customers(customer_id),", "lineIndex": 18}], "type": "other", "color": "#3498DB", "tag": "", "level": "word", "parentId": "collateral-entity-main"}, {"id": "child-collateral-loanId", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "loanId^FK", "javaLines": [], "sqlLines": [], "type": "other", "color": "#E74C3C", "tag": "MISSING", "level": "word", "parentId": "collateral-entity-main"}, {"id": "child-collateral-collateralType", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "collateralType (RealEstate, Vehicle, Investment, Cash, Other)", "javaLines": [{"content": "        public enum CollateralType {", "lineIndex": 395}, {"content": "            REAL_ESTATE, VEHICLE, INVESTMENT, SAVINGS, OTHER", "lineIndex": 396}, {"content": "        }", "lineIndex": 397}, {"content": "        private CollateralType type;", "lineIndex": 403}, {"content": "        public CollateralType getType() {", "lineIndex": 461}, {"content": "            return type;", "lineIndex": 462}, {"content": "        }", "lineIndex": 463}, {"content": "        public void setType(CollateralType type) {", "lineIndex": 465}, {"content": "            this.type = type;", "lineIndex": 469}, {"content": "        }", "lineIndex": 470}], "sqlLines": [{"content": "CREATE TYPE collateral_type AS ENUM ('REAL_ESTATE', 'VEHICLE', 'INVESTMENT', 'SAVINGS', 'OTHER');", "lineIndex": 3}, {"content": "    type collateral_type NOT NULL,", "lineIndex": 21}], "type": "other", "color": "#3498DB", "tag": "", "level": "word", "parentId": "collateral-entity-main"}, {"id": "child-collateral-description", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "description", "javaLines": [{"content": "        private String description;", "lineIndex": 401}, {"content": "        public String getDescription() {", "lineIndex": 433}, {"content": "            return description;", "lineIndex": 434}, {"content": "        }", "lineIndex": 435}, {"content": "        public void setDescription(String description) {", "lineIndex": 437}, {"content": "            this.description = description;", "lineIndex": 444}, {"content": "        }", "lineIndex": 445}], "sqlLines": [{"content": "    description VARCHAR(500) NOT NULL,", "lineIndex": 19}], "type": "other", "color": "#3498DB", "tag": "", "level": "word", "parentId": "collateral-entity-main"}, {"id": "child-collateral-value", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "value", "javaLines": [{"content": "        private BigDecimal value;", "lineIndex": 402}, {"content": "        public BigDecimal getValue() {", "lineIndex": 447}, {"content": "            return value;", "lineIndex": 448}, {"content": "        }", "lineIndex": 449}, {"content": "        public void setValue(BigDecimal value) {", "lineIndex": 451}, {"content": "            this.value = value;", "lineIndex": 458}, {"content": "        }", "lineIndex": 459}], "sqlLines": [{"content": "    value DECIMAL(12,2) NOT NULL CHECK (value > 0),", "lineIndex": 20}], "type": "other", "color": "#3498DB", "tag": "", "level": "word", "parentId": "collateral-entity-main"}, {"id": "child-collateral-appraisalDate", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "appraisalDate", "javaLines": [], "sqlLines": [], "type": "other", "color": "#E74C3C", "tag": "MISSING", "level": "word", "parentId": "collateral-entity-main"}, {"id": "child-collateral-appraisalValue", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "appraisalValue", "javaLines": [], "sqlLines": [], "type": "other", "color": "#E74C3C", "tag": "MISSING", "level": "word", "parentId": "collateral-entity-main"}, {"id": "child-collateral-lienStatus", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "lienStatus (FirstLien, SecondLien, NoLien)", "javaLines": [], "sqlLines": [], "type": "other", "color": "#E74C3C", "tag": "MISSING", "level": "word", "parentId": "collateral-entity-main"}, {"id": "child-collateral-condition", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "condition (Excellent, Good, Fair, Poor)", "javaLines": [], "sqlLines": [], "type": "other", "color": "#E74C3C", "tag": "MISSING", "level": "word", "parentId": "collateral-entity-main"}, {"id": "child-collateral-assetAddress", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "assetAddress", "javaLines": [], "sqlLines": [], "type": "other", "color": "#E74C3C", "tag": "MISSING", "level": "word", "parentId": "collateral-entity-main"}, {"id": "child-collateral-assetCity", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "assetCity", "javaLines": [], "sqlLines": [], "type": "other", "color": "#E74C3C", "tag": "MISSING", "level": "word", "parentId": "collateral-entity-main"}, {"id": "child-collateral-assetState", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "assetState", "javaLines": [], "sqlLines": [], "type": "other", "color": "#E74C3C", "tag": "MISSING", "level": "word", "parentId": "collateral-entity-main"}, {"id": "child-collateral-assetZipCode", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "assetZipCode", "javaLines": [], "sqlLines": [], "type": "other", "color": "#E74C3C", "tag": "MISSING", "level": "word", "parentId": "collateral-entity-main"}, {"id": "child-collateral-insuranceProvider", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "insuranceProvider", "javaLines": [], "sqlLines": [], "type": "other", "color": "#E74C3C", "tag": "MISSING", "level": "word", "parentId": "collateral-entity-main"}, {"id": "child-collateral-insurancePolicyNumber", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "insurancePolicyNumber", "javaLines": [], "sqlLines": [], "type": "other", "color": "#E74C3C", "tag": "MISSING", "level": "word", "parentId": "collateral-entity-main"}, {"id": "child-collateral-insuranceExpirationDate", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "insuranceExpirationDate", "javaLines": [], "sqlLines": [], "type": "other", "color": "#E74C3C", "tag": "MISSING", "level": "word", "parentId": "collateral-entity-main"}, {"id": "child-collateral-documentationComplete", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "documentationComplete (true, false)", "javaLines": [], "sqlLines": [], "type": "other", "color": "#E74C3C", "tag": "MISSING", "level": "word", "parentId": "collateral-entity-main"}]}, {"id": "mapping-line-067", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Collateral has many-to-one relationship with Customer using Collateral.customerId to Customer.customerId^PK", "javaLines": [], "sqlLines": [{"content": "    customer_id VARCHAR(10) NOT NULL REFERENCES customers(customer_id),", "lineIndex": 18}], "type": "relationship", "color": "#9B59B6", "tag": "R14", "level": "line"}, {"id": "mapping-line-068", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Collateral has many-to-one relationship with Loan using Collateral.loanId to Loan.loanId^PK", "javaLines": [], "sqlLines": [{"content": "    collateral_id VARCHAR(10) REFERENCES collaterals(collateral_id),", "lineIndex": 38}], "type": "relationship", "color": "#9B59B6", "tag": "R15", "level": "line"}, {"id": "mapping-line-070", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Collateral.collateralId must be unique", "javaLines": [{"content": "        private static final Pattern COLLATERAL_ID_PATTERN = Pattern.compile(\"^COL-[0-9]{6}$\");", "lineIndex": 393}, {"content": "        public void setCollateralId(String collateralId) {", "lineIndex": 412}, {"content": "            if (collateralId == null || collateralId.isEmpty()) {", "lineIndex": 413}, {"content": "                throw new IllegalArgumentException(\"Collateral ID cannot be null or empty\");", "lineIndex": 414}, {"content": "            }", "lineIndex": 415}, {"content": "            if (!COLLATERAL_ID_PATTERN.matcher(collateralId).matches()) {", "lineIndex": 416}, {"content": "                throw new IllegalArgumentException(\"Invalid Collateral ID format\");", "lineIndex": 417}, {"content": "            }", "lineIndex": 418}, {"content": "            this.collateralId = collateralId;", "lineIndex": 419}, {"content": "        }", "lineIndex": 420}], "sqlLines": [{"content": "    collateral_id VARCHAR(10) PRIMARY KEY CHECK (collateral_id ~ '^COL-[0-9]{6}$'),", "lineIndex": 17}], "type": "validation", "color": "#E74C3C", "tag": "V13", "level": "line"}, {"id": "mapping-line-071", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Collateral.customerId must exist in Customer table", "javaLines": [{"content": "        public void setCustomerId(String customerId) {", "lineIndex": 426}, {"content": "            if (customerId == null || customerId.isEmpty()) {", "lineIndex": 427}, {"content": "                throw new IllegalArgumentException(\"Customer ID cannot be null or empty\");", "lineIndex": 428}, {"content": "            }", "lineIndex": 429}, {"content": "            this.customerId = customerId;", "lineIndex": 430}, {"content": "        }", "lineIndex": 431}], "sqlLines": [{"content": "    customer_id VARCHAR(10) NOT NULL REFERENCES customers(customer_id),", "lineIndex": 18}], "type": "validation", "color": "#E74C3C", "tag": "V14", "level": "line"}, {"id": "mapping-line-072", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Collateral.loanId must exist in Loan table", "javaLines": [], "sqlLines": [{"content": "    collateral_id VARCHAR(10) REFERENCES collaterals(collateral_id),", "lineIndex": 38}], "type": "validation", "color": "#E74C3C", "tag": "V15", "level": "line"}, {"id": "mapping-line-073", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Collateral.value must be greater than 0", "javaLines": [{"content": "        public void setValue(BigDecimal value) {", "lineIndex": 451}, {"content": "            if (value == null) {", "lineIndex": 452}, {"content": "                throw new IllegalArgumentException(\"Value cannot be null\");", "lineIndex": 453}, {"content": "            }", "lineIndex": 454}, {"content": "            if (value.compareTo(BigDecimal.ZERO) <= 0) {", "lineIndex": 455}, {"content": "                throw new IllegalArgumentException(\"Collateral value must be positive\");", "lineIndex": 456}, {"content": "            }", "lineIndex": 457}, {"content": "            this.value = value;", "lineIndex": 458}, {"content": "        }", "lineIndex": 459}], "sqlLines": [{"content": "    value DECIMAL(12,2) NOT NULL CHECK (value > 0),", "lineIndex": 20}], "type": "validation", "color": "#E74C3C", "tag": "V16", "level": "line"}, {"id": "mapping-line-074", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Collateral.appraisalValue must be greater than 0", "javaLines": [], "sqlLines": [], "type": "validation", "color": "#E74C3C", "tag": "V17", "level": "line"}, {"id": "mapping-line-075", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Collateral.customerId must match the Loan.customerId of the associated loan", "javaLines": [{"content": "            if (collateral != null && this.customer != null && ", "lineIndex": 270}, {"content": "                !collateral.getCustomerId().equals(this.customerId)) {", "lineIndex": 271}, {"content": "                throw new IllegalArgumentException(\"Collateral must belong to selected Customer\");", "lineIndex": 272}, {"content": "            }", "lineIndex": 273}], "sqlLines": [{"content": "    CONSTRAINT loan_collateral_customer_fk CHECK (", "lineIndex": 45}, {"content": "        (collateral_id IS NULL) OR ", "lineIndex": 46}, {"content": "        (collateral_id IS NOT NULL AND ", "lineIndex": 47}, {"content": "         EXISTS (SELECT 1 FROM collaterals c ", "lineIndex": 48}, {"content": "                 WHERE c.collateral_id = loans.collateral_id ", "lineIndex": 49}, {"content": "                 AND c.customer_id = loans.customer_id))", "lineIndex": 50}, {"content": "    )", "lineIndex": 51}], "type": "validation", "color": "#E74C3C", "tag": "V18", "level": "line"}, {"id": "mapping-line-076", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: collateralId Key: Primary Data Type: Integer Type: Mandatory Format: \"COL-######\" Values: N/A Default: N/A Validation: Auto-increment Error Message: \"Invalid Collateral ID format\" Description: Unique identifier for the collateral", "javaLines": [{"content": "        private static final Pattern COLLATERAL_ID_PATTERN = Pattern.compile(\"^COL-[0-9]{6}$\");", "lineIndex": 393}, {"content": "        private String collateralId;", "lineIndex": 399}, {"content": "        public String getCollateralId() {", "lineIndex": 408}, {"content": "            return collateralId;", "lineIndex": 409}, {"content": "        }", "lineIndex": 410}, {"content": "        public void setCollateralId(String collateralId) {", "lineIndex": 412}, {"content": "            if (collateralId == null || collateralId.isEmpty()) {", "lineIndex": 413}, {"content": "                throw new IllegalArgumentException(\"Collateral ID cannot be null or empty\");", "lineIndex": 414}, {"content": "            }", "lineIndex": 415}, {"content": "            if (!COLLATERAL_ID_PATTERN.matcher(collateralId).matches()) {", "lineIndex": 416}, {"content": "                throw new IllegalArgumentException(\"Invalid Collateral ID format\");", "lineIndex": 417}, {"content": "            }", "lineIndex": 418}, {"content": "            this.collateralId = collateralId;", "lineIndex": 419}, {"content": "        }", "lineIndex": 420}], "sqlLines": [{"content": "    collateral_id VARCHAR(10) PRIMARY KEY CHECK (collateral_id ~ '^COL-[0-9]{6}$'),", "lineIndex": 17}], "type": "attribute", "color": "#3498DB", "tag": "A36", "level": "line"}, {"id": "mapping-line-077", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: customerId Key: Foreign Data Type: Integer Type: Mandatory Format: \"CUS-######\" Values: N/A Default: N/A Validation: Foreign Key Check Error Message: \"Customer ID must reference an existing customer\" Description: References the customer who owns the collateral", "javaLines": [{"content": "        private String customerId;", "lineIndex": 400}, {"content": "        public String getCustomerId() {", "lineIndex": 422}, {"content": "            return customerId;", "lineIndex": 423}, {"content": "        }", "lineIndex": 424}, {"content": "        public void setCustomerId(String customerId) {", "lineIndex": 426}, {"content": "            if (customerId == null || customerId.isEmpty()) {", "lineIndex": 427}, {"content": "                throw new IllegalArgumentException(\"Customer ID cannot be null or empty\");", "lineIndex": 428}, {"content": "            }", "lineIndex": 429}, {"content": "            this.customerId = customerId;", "lineIndex": 430}, {"content": "        }", "lineIndex": 431}], "sqlLines": [{"content": "    customer_id VARCHAR(10) NOT NULL REFERENCES customers(customer_id),", "lineIndex": 18}], "type": "attribute", "color": "#3498DB", "tag": "A37", "level": "line"}, {"id": "mapping-line-078", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: loanId Key: Foreign Data Type: Integer Type: Optional Format: \"LN-######\" Values: N/A Default: N/A Validation: Foreign Key Check Error Message: \"Loan ID must reference an existing loan\" Description: References the loan secured by this collateral", "javaLines": [], "sqlLines": [], "type": "attribute", "color": "#3498DB", "tag": "A38", "level": "line"}, {"id": "mapping-line-079", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: collateralType Key: Non-unique Data Type: Enum Type: Mandatory Format: N/A Values: \"RealEstate\", \"Vehicle\", \"Investment\", \"Cash\", \"Other\" Default: \"RealEstate\" Validation: List Check Error Message: \"Invalid collateral type\" Description: Type of asset used as collateral", "javaLines": [{"content": "        public enum CollateralType {", "lineIndex": 395}, {"content": "            REAL_ESTATE, VEHICLE, INVESTMENT, SAVINGS, OTHER", "lineIndex": 396}, {"content": "        }", "lineIndex": 397}, {"content": "        private CollateralType type;", "lineIndex": 403}, {"content": "        public CollateralType getType() {", "lineIndex": 461}, {"content": "            return type;", "lineIndex": 462}, {"content": "        }", "lineIndex": 463}, {"content": "        public void setType(CollateralType type) {", "lineIndex": 465}, {"content": "            if (type == null) {", "lineIndex": 466}, {"content": "                throw new IllegalArgumentException(\"Collateral type cannot be null\");", "lineIndex": 467}, {"content": "            }", "lineIndex": 468}, {"content": "            this.type = type;", "lineIndex": 469}, {"content": "        }", "lineIndex": 470}], "sqlLines": [{"content": "CREATE TYPE collateral_type AS ENUM ('REAL_ESTATE', 'VEHICLE', 'INVESTMENT', 'SAVINGS', 'OTHER');", "lineIndex": 3}, {"content": "    type collateral_type NOT NULL,", "lineIndex": 21}], "type": "attribute", "color": "#3498DB", "tag": "A39", "level": "line"}, {"id": "mapping-line-080", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: description Key: Non-unique Data Type: String Type: Mandatory Format: N/A Values: N/A Default: N/A Validation: Length Check Error Message: \"Description is required\" Description: Detailed description of the collateral", "javaLines": [{"content": "        private String description;", "lineIndex": 401}, {"content": "        public String getDescription() {", "lineIndex": 433}, {"content": "            return description;", "lineIndex": 434}, {"content": "        }", "lineIndex": 435}, {"content": "        public void setDescription(String description) {", "lineIndex": 437}, {"content": "            if (description == null || description.trim().isEmpty()) {", "lineIndex": 438}, {"content": "                throw new IllegalArgumentException(\"Description cannot be null or empty\");", "lineIndex": 439}, {"content": "            }", "lineIndex": 440}, {"content": "            if (description.length() > 500) {", "lineIndex": 441}, {"content": "                throw new IllegalArgumentException(\"Description cannot exceed 500 characters\");", "lineIndex": 442}, {"content": "            }", "lineIndex": 443}, {"content": "            this.description = description;", "lineIndex": 444}, {"content": "        }", "lineIndex": 445}], "sqlLines": [{"content": "    description VARCHAR(500) NOT NULL,", "lineIndex": 19}], "type": "attribute", "color": "#3498DB", "tag": "A40", "level": "line"}, {"id": "mapping-line-081", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: value Key: Non-unique Data Type: Decimal Type: Mandatory Format: \"$#,###.00\" Values: 0.00-N Default: N/A Validation: Range Check Error Message: \"Value must be greater than 0\" Description: Current market value of the collateral", "javaLines": [{"content": "        private BigDecimal value;", "lineIndex": 402}, {"content": "        public BigDecimal getValue() {", "lineIndex": 447}, {"content": "            return value;", "lineIndex": 448}, {"content": "        }", "lineIndex": 449}, {"content": "        public void setValue(BigDecimal value) {", "lineIndex": 451}, {"content": "            if (value == null) {", "lineIndex": 452}, {"content": "                throw new IllegalArgumentException(\"Value cannot be null\");", "lineIndex": 453}, {"content": "            }", "lineIndex": 454}, {"content": "            if (value.compareTo(BigDecimal.ZERO) <= 0) {", "lineIndex": 455}, {"content": "                throw new IllegalArgumentException(\"Collateral value must be positive\");", "lineIndex": 456}, {"content": "            }", "lineIndex": 457}, {"content": "            this.value = value;", "lineIndex": 458}, {"content": "        }", "lineIndex": 459}], "sqlLines": [{"content": "    value DECIMAL(12,2) NOT NULL CHECK (value > 0),", "lineIndex": 20}], "type": "attribute", "color": "#3498DB", "tag": "A41", "level": "line"}, {"id": "mapping-line-082", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: appraisalDate Key: Non-unique Data Type: Date Type: Mandatory Format: \"YYYY-MM-DD\" Values: N/A Default: \"CURRENT_DATE\" Validation: Date Check Error Message: \"Appraisal date is required\" Description: Date when the collateral was appraised", "javaLines": [], "sqlLines": [], "type": "attribute", "color": "#3498DB", "tag": "A42", "level": "line"}, {"id": "mapping-line-083", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: appraisalValue Key: Non-unique Data Type: Decimal Type: Mandatory Format: \"$#,###.00\" Values: 0.00-N Default: N/A Validation: Range Check Error Message: \"Appraisal value must be greater than 0\" Description: Value determined during appraisal", "javaLines": [], "sqlLines": [], "type": "attribute", "color": "#3498DB", "tag": "A43", "level": "line"}, {"id": "mapping-line-084", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: lienStatus Key: Non-unique Data Type: Enum Type: Mandatory Format: N/A Values: \"FirstLien\", \"SecondLien\", \"NoLien\" Default: \"FirstLien\" Validation: List Check Error Message: \"Invalid lien status\" Description: Status of liens against this collateral", "javaLines": [], "sqlLines": [], "type": "attribute", "color": "#3498DB", "tag": "A44", "level": "line"}, {"id": "mapping-line-085", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: condition Key: Non-unique Data Type: Enum Type: Mandatory Format: N/A Values: \"Excellent\", \"Good\", \"Fair\", \"Poor\" Default: \"Good\" Validation: List Check Error Message: \"Invalid condition\" Description: Physical condition of the collateral", "javaLines": [], "sqlLines": [], "type": "attribute", "color": "#3498DB", "tag": "A45", "level": "line"}, {"id": "mapping-line-086", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: assetAddress Key: Non-unique Data Type: String Type: Optional Format: N/A Values: N/A Default: N/A Validation: Length Check Error Message: N/A Description: Address of the physical asset", "javaLines": [], "sqlLines": [], "type": "attribute", "color": "#3498DB", "tag": "A46", "level": "line"}, {"id": "mapping-line-087", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: assetCity Key: Non-unique Data Type: String Type: Optional Format: N/A Values: N/A Default: N/A Validation: Length Check Error Message: N/A Description: City where the physical asset is located", "javaLines": [], "sqlLines": [], "type": "attribute", "color": "#3498DB", "tag": "A47", "level": "line"}, {"id": "mapping-line-088", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: assetState Key: Non-unique Data Type: String Type: Optional Format: N/A Values: N/A Default: N/A Validation: Length Check Error Message: N/A Description: State where the physical asset is located", "javaLines": [], "sqlLines": [], "type": "attribute", "color": "#3498DB", "tag": "A48", "level": "line"}, {"id": "mapping-line-089", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: assetZipCode Key: Non-unique Data Type: String Type: Optional Format: \"#####\" or \"#####-####\" Values: N/A Default: N/A Validation: Format Check Error Message: \"Invalid zip code format\" Description: Zip code where the physical asset is located", "javaLines": [], "sqlLines": [], "type": "attribute", "color": "#3498DB", "tag": "A49", "level": "line"}, {"id": "mapping-line-090", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: insuranceProvider Key: Non-unique Data Type: String Type: Optional Format: N/A Values: N/A Default: N/A Validation: Length Check Error Message: N/A Description: Insurance company covering the collateral", "javaLines": [], "sqlLines": [], "type": "attribute", "color": "#3498DB", "tag": "A50", "level": "line"}, {"id": "mapping-line-091", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: insurancePolicyNumber Key: Non-unique Data Type: String Type: Optional Format: N/A Values: N/A Default: N/A Validation: Length Check Error Message: N/A Description: Insurance policy number for the collateral", "javaLines": [], "sqlLines": [], "type": "attribute", "color": "#3498DB", "tag": "A51", "level": "line"}, {"id": "mapping-line-092", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: insuranceExpirationDate Key: Non-unique Data Type: Date Type: Optional Format: \"YYYY-MM-DD\" Values: N/A Default: N/A Validation: Date Check Error Message: \"Expiration date must be in the future\" Description: Date when the insurance policy expires", "javaLines": [], "sqlLines": [], "type": "attribute", "color": "#3498DB", "tag": "A52", "level": "line"}, {"id": "mapping-line-093", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: documentationComplete Key: Non-unique Data Type: Boolean Type: Mandatory Format: N/A Values: \"true\", \"false\" Default: \"false\" Validation: Boolean Check Error Message: \"Invalid documentation status\" Description: Indicates if all required documentation is complete", "javaLines": [], "sqlLines": [], "type": "attribute", "color": "#3498DB", "tag": "A53", "level": "line"}, {"id": "mapping-line-094", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Relationship: Collateral to Customer", "javaLines": [], "sqlLines": [{"content": "    customer_id VARCHAR(10) NOT NULL REFERENCES customers(customer_id),", "lineIndex": 18}], "type": "relationship", "color": "#9B59B6", "tag": "R16", "level": "line"}, {"id": "mapping-line-095", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Relationship Properties: On Delete: Restrict (Prevent deletion of customer with active collateral) On Update: Cascade (Update collateral records when customer details change) Foreign Key Type: Non-Nullable", "javaLines": [], "sqlLines": [{"content": "    customer_id VARCHAR(10) NOT NULL REFERENCES customers(customer_id),", "lineIndex": 18}], "type": "relationship_property", "color": "#8E44AD", "tag": "RP4", "level": "line"}, {"id": "mapping-line-096", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Relationship: Collateral to <PERSON><PERSON>", "javaLines": [], "sqlLines": [{"content": "    collateral_id VARCHAR(10) REFERENCES collaterals(collateral_id),", "lineIndex": 38}], "type": "relationship", "color": "#9B59B6", "tag": "R17", "level": "line"}, {"id": "mapping-line-097", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Relationship Properties: On Delete: Set Null (Remove loan reference when loan is deleted) On Update: Cascade (Update collateral records when loan details change) Foreign Key Type: Nullable", "javaLines": [], "sqlLines": [{"content": "    collateral_id VARCHAR(10) REFERENCES collaterals(collateral_id),", "lineIndex": 38}], "type": "relationship_property", "color": "#8E44AD", "tag": "RP5", "level": "line"}, {"id": "payment-entity-main", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Payment has paymentId^PK, loanId^FK, amount, paymentDate, dueDate, status (Pending, Completed, Failed, Cancelled), paymentMethod (CreditCard, DebitCard, BankTransfer, Cash, Check, AutoDebit), referenceNumber, lateFee, paymentType (Regular, ExtraPayment, LatePayment, InterestOnly, PrincipalOnly), notes.", "javaLines": [{"content": "    public static class Payment {", "lineIndex": 473}, {"content": "        public enum PaymentStatus {", "lineIndex": 489}, {"content": "            Pending, Completed, Failed, Cancelled", "lineIndex": 490}, {"content": "        }", "lineIndex": 491}, {"content": "        public enum PaymentMethod {", "lineIndex": 493}, {"content": "            CreditCard, DebitCard, BankTransfer, Cash, Check, AutoDebit", "lineIndex": 494}, {"content": "        }", "lineIndex": 495}, {"content": "        public enum PaymentType {", "lineIndex": 497}, {"content": "            Regular, ExtraPayment, LatePayment, InterestOnly, PrincipalOnly", "lineIndex": 498}, {"content": "        }", "lineIndex": 499}], "sqlLines": [{"content": "CREATE TYPE payment_status AS ENUM ('PENDING', 'COMPLETED', 'FAILED', 'REFUNDED');", "lineIndex": 4}, {"content": "CREATE TABLE payments (", "lineIndex": 54}], "type": "other", "color": "#F39C12", "tag": "E4", "level": "line", "children": [{"id": "child-payment-paymentId", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "paymentId^PK", "javaLines": [{"content": "        private Long paymentId;", "lineIndex": 476}, {"content": "        public Long getPaymentId() {", "lineIndex": 530}, {"content": "            return paymentId;", "lineIndex": 531}, {"content": "        }", "lineIndex": 532}, {"content": "        public void setPaymentId(Long paymentId) {", "lineIndex": 534}, {"content": "            this.paymentId = paymentId;", "lineIndex": 535}, {"content": "        }", "lineIndex": 536}], "sqlLines": [{"content": "    payment_id SERIAL PRIMARY KEY,", "lineIndex": 55}], "type": "other", "color": "#3498DB", "tag": "", "level": "word", "parentId": "payment-entity-main"}, {"id": "child-payment-loanId", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "loanId^FK", "javaLines": [{"content": "        private String loanId;", "lineIndex": 477}, {"content": "        public String getLoanId() {", "lineIndex": 538}, {"content": "            return loanId;", "lineIndex": 539}, {"content": "        }", "lineIndex": 540}, {"content": "        public void setLoanId(String loanId) {", "lineIndex": 542}, {"content": "            this.loanId = loanId;", "lineIndex": 546}, {"content": "        }", "lineIndex": 547}], "sqlLines": [{"content": "    loan_id VARCHAR(12) NOT NULL REFERENCES loans(loan_id),", "lineIndex": 56}], "type": "other", "color": "#3498DB", "tag": "", "level": "word", "parentId": "payment-entity-main"}, {"id": "child-payment-amount", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "amount", "javaLines": [{"content": "        private BigDecimal amount;", "lineIndex": 478}, {"content": "        public BigDecimal getAmount() {", "lineIndex": 549}, {"content": "            return amount;", "lineIndex": 550}, {"content": "        }", "lineIndex": 551}, {"content": "        public void setAmount(BigDecimal amount) {", "lineIndex": 553}, {"content": "            this.amount = amount;", "lineIndex": 557}, {"content": "        }", "lineIndex": 558}], "sqlLines": [{"content": "    amount DECIMAL(12,2) NOT NULL CHECK (amount > 0),", "lineIndex": 57}], "type": "other", "color": "#3498DB", "tag": "", "level": "word", "parentId": "payment-entity-main"}, {"id": "child-payment-paymentDate", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "paymentDate", "javaLines": [{"content": "        private LocalDate paymentDate;", "lineIndex": 479}, {"content": "        public LocalDate getPaymentDate() {", "lineIndex": 560}, {"content": "            return paymentDate;", "lineIndex": 561}, {"content": "        }", "lineIndex": 562}, {"content": "        public void setPaymentDate(LocalDate paymentDate) {", "lineIndex": 564}, {"content": "            this.paymentDate = paymentDate;", "lineIndex": 568}, {"content": "        }", "lineIndex": 569}], "sqlLines": [{"content": "    payment_date TIMESTAMP NOT NULL,", "lineIndex": 58}], "type": "other", "color": "#3498DB", "tag": "", "level": "word", "parentId": "payment-entity-main"}, {"id": "child-payment-dueDate", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "dueDate", "javaLines": [{"content": "        private LocalDate dueDate;", "lineIndex": 480}, {"content": "        public LocalDate getDueDate() {", "lineIndex": 571}, {"content": "            return dueDate;", "lineIndex": 572}, {"content": "        }", "lineIndex": 573}, {"content": "        public void setDueDate(LocalDate dueDate) {", "lineIndex": 575}, {"content": "            this.dueDate = dueDate;", "lineIndex": 579}, {"content": "        }", "lineIndex": 580}], "sqlLines": [], "type": "other", "color": "#3498DB", "tag": "", "level": "word", "parentId": "payment-entity-main"}, {"id": "child-payment-status", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "status (Pending, Completed, Failed, Cancelled)", "javaLines": [{"content": "        public enum PaymentStatus {", "lineIndex": 489}, {"content": "            Pending, Completed, Failed, Cancelled", "lineIndex": 490}, {"content": "        }", "lineIndex": 491}, {"content": "        private PaymentStatus status;", "lineIndex": 481}, {"content": "        public PaymentStatus getStatus() {", "lineIndex": 582}, {"content": "            return status;", "lineIndex": 583}, {"content": "        }", "lineIndex": 584}, {"content": "        public void setStatus(PaymentStatus status) {", "lineIndex": 586}, {"content": "            this.status = status;", "lineIndex": 590}, {"content": "        }", "lineIndex": 591}], "sqlLines": [{"content": "CREATE TYPE payment_status AS ENUM ('PENDING', 'COMPLETED', 'FAILED', 'REFUNDED');", "lineIndex": 4}, {"content": "    status payment_status NOT NULL DEFAULT 'PENDING',", "lineIndex": 59}], "type": "other", "color": "#3498DB", "tag": "", "level": "word", "parentId": "payment-entity-main"}, {"id": "child-payment-paymentMethod", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "paymentMethod (CreditCard, DebitCard, BankTransfer, Cash, Check, AutoDebit)", "javaLines": [{"content": "        public enum PaymentMethod {", "lineIndex": 493}, {"content": "            CreditCard, DebitCard, BankTransfer, Cash, Check, AutoDebit", "lineIndex": 494}, {"content": "        }", "lineIndex": 495}, {"content": "        private PaymentMethod paymentMethod;", "lineIndex": 482}, {"content": "        public PaymentMethod getPaymentMethod() {", "lineIndex": 593}, {"content": "            return paymentMethod;", "lineIndex": 594}, {"content": "        }", "lineIndex": 595}, {"content": "        public void setPaymentMethod(PaymentMethod paymentMethod) {", "lineIndex": 597}, {"content": "            this.paymentMethod = paymentMethod;", "lineIndex": 601}, {"content": "        }", "lineIndex": 602}], "sqlLines": [{"content": "    payment_method VARCHAR(50) NOT NULL,", "lineIndex": 60}], "type": "other", "color": "#3498DB", "tag": "", "level": "word", "parentId": "payment-entity-main"}, {"id": "child-payment-referenceNumber", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "referenceNumber", "javaLines": [{"content": "        private String referenceNumber;", "lineIndex": 483}, {"content": "        public String getReferenceNumber() {", "lineIndex": 604}, {"content": "            return referenceNumber;", "lineIndex": 605}, {"content": "        }", "lineIndex": 606}, {"content": "        public void setReferenceNumber(String referenceNumber) {", "lineIndex": 608}, {"content": "            this.referenceNumber = referenceNumber;", "lineIndex": 609}, {"content": "        }", "lineIndex": 610}], "sqlLines": [{"content": "    reference_number VARCHAR(100),", "lineIndex": 61}], "type": "other", "color": "#3498DB", "tag": "", "level": "word", "parentId": "payment-entity-main"}, {"id": "child-payment-lateFee", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "lateFee", "javaLines": [{"content": "        private BigDecimal lateFee;", "lineIndex": 484}, {"content": "        public BigDecimal getLateFee() {", "lineIndex": 612}, {"content": "            return lateFee;", "lineIndex": 613}, {"content": "        }", "lineIndex": 614}, {"content": "        public void setLateFee(BigDecimal lateFee) {", "lineIndex": 616}, {"content": "            this.lateFee = lateFee;", "lineIndex": 617}, {"content": "        }", "lineIndex": 618}], "sqlLines": [], "type": "other", "color": "#3498DB", "tag": "", "level": "word", "parentId": "payment-entity-main"}, {"id": "child-payment-paymentType", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "paymentType (Regular, ExtraPayment, LatePayment, InterestOnly, PrincipalOnly)", "javaLines": [{"content": "        public enum PaymentType {", "lineIndex": 497}, {"content": "            Regular, ExtraPayment, LatePayment, InterestOnly, PrincipalOnly", "lineIndex": 498}, {"content": "        }", "lineIndex": 499}, {"content": "        private PaymentType paymentType;", "lineIndex": 485}, {"content": "        public PaymentType getPaymentType() {", "lineIndex": 620}, {"content": "            return paymentType;", "lineIndex": 621}, {"content": "        }", "lineIndex": 622}, {"content": "        public void setPaymentType(PaymentType paymentType) {", "lineIndex": 624}, {"content": "            this.paymentType = paymentType;", "lineIndex": 628}, {"content": "        }", "lineIndex": 629}], "sqlLines": [], "type": "other", "color": "#3498DB", "tag": "", "level": "word", "parentId": "payment-entity-main"}, {"id": "child-payment-notes", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "notes", "javaLines": [{"content": "        private String notes;", "lineIndex": 486}, {"content": "        public String getNotes() {", "lineIndex": 631}, {"content": "            return notes;", "lineIndex": 632}, {"content": "        }", "lineIndex": 633}, {"content": "        public void setNotes(String notes) {", "lineIndex": 635}, {"content": "            this.notes = notes;", "lineIndex": 636}, {"content": "        }", "lineIndex": 637}], "sqlLines": [], "type": "other", "color": "#3498DB", "tag": "", "level": "word", "parentId": "payment-entity-main"}]}, {"id": "mapping-line-100", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Payment has many-to-one relationship with Loan using Payment.loanId to Loan.loanId^PK", "javaLines": [{"content": "        private Loan loan;", "lineIndex": 487}, {"content": "        public Loan getLoan() {", "lineIndex": 639}, {"content": "            return loan;", "lineIndex": 640}, {"content": "        }", "lineIndex": 641}, {"content": "        public void setLoan(Loan loan) {", "lineIndex": 643}, {"content": "            this.loan = loan;", "lineIndex": 644}, {"content": "            if (loan != null) {", "lineIndex": 645}, {"content": "                this.loanId = loan.getLoanId();", "lineIndex": 646}, {"content": "            }", "lineIndex": 647}, {"content": "        }", "lineIndex": 648}], "sqlLines": [{"content": "    loan_id VARCHAR(12) NOT NULL REFERENCES loans(loan_id),", "lineIndex": 56}], "type": "relationship", "color": "#9B59B6", "tag": "R18", "level": "line"}, {"id": "mapping-line-102", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Payment.paymentId must be unique", "javaLines": [{"content": "        private Long paymentId;", "lineIndex": 476}, {"content": "        public Long getPaymentId() {", "lineIndex": 530}, {"content": "            return paymentId;", "lineIndex": 531}, {"content": "        }", "lineIndex": 532}, {"content": "        public void setPaymentId(Long paymentId) {", "lineIndex": 534}, {"content": "            this.paymentId = paymentId;", "lineIndex": 535}, {"content": "        }", "lineIndex": 536}], "sqlLines": [{"content": "    payment_id SERIAL PRIMARY KEY,", "lineIndex": 55}], "type": "validation", "color": "#E74C3C", "tag": "V19", "level": "line"}, {"id": "mapping-line-103", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Payment.loanId must exist in Loan table", "javaLines": [{"content": "        public void setLoanId(String loanId) {", "lineIndex": 542}, {"content": "            if (loanId == null || loanId.isEmpty()) {", "lineIndex": 543}, {"content": "                throw new IllegalArgumentException(\"Loan ID is required\");", "lineIndex": 544}, {"content": "            }", "lineIndex": 545}, {"content": "            this.loanId = loanId;", "lineIndex": 546}, {"content": "        }", "lineIndex": 547}], "sqlLines": [{"content": "    loan_id VARCHAR(12) NOT NULL REFERENCES loans(loan_id),", "lineIndex": 56}], "type": "validation", "color": "#E74C3C", "tag": "V20", "level": "line"}, {"id": "mapping-line-104", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Payment.amount must be greater than 0", "javaLines": [{"content": "        private static final BigDecimal MIN_AMOUNT = new BigDecimal(\"0.01\");", "lineIndex": 474}, {"content": "        public void setAmount(BigDecimal amount) {", "lineIndex": 553}, {"content": "            if (amount == null || amount.compareTo(MIN_AMOUNT) < 0) {", "lineIndex": 554}, {"content": "                throw new IllegalArgumentException(\"Payment amount must be greater than 0\");", "lineIndex": 555}, {"content": "            }", "lineIndex": 556}, {"content": "            this.amount = amount;", "lineIndex": 557}, {"content": "        }", "lineIndex": 558}], "sqlLines": [{"content": "    amount DECIMAL(12,2) NOT NULL CHECK (amount > 0),", "lineIndex": 57}], "type": "validation", "color": "#E74C3C", "tag": "V21", "level": "line"}, {"id": "mapping-line-105", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Payment.dueDate must be a valid date", "javaLines": [{"content": "        public void setDueDate(LocalDate dueDate) {", "lineIndex": 575}, {"content": "            if (dueDate == null) {", "lineIndex": 576}, {"content": "                throw new IllegalArgumentException(\"Due date is required\");", "lineIndex": 577}, {"content": "            }", "lineIndex": 578}, {"content": "            this.dueDate = dueDate;", "lineIndex": 579}, {"content": "        }", "lineIndex": 580}], "sqlLines": [], "type": "validation", "color": "#E74C3C", "tag": "V22", "level": "line"}, {"id": "mapping-line-106", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Payment.paymentDate must be a valid date", "javaLines": [{"content": "        public void setPaymentDate(LocalDate paymentDate) {", "lineIndex": 564}, {"content": "            if (paymentDate == null) {", "lineIndex": 565}, {"content": "                throw new IllegalArgumentException(\"Payment date is required\");", "lineIndex": 566}, {"content": "            }", "lineIndex": 567}, {"content": "            this.paymentDate = paymentDate;", "lineIndex": 568}, {"content": "        }", "lineIndex": 569}], "sqlLines": [{"content": "    payment_date TIMESTAMP NOT NULL,", "lineIndex": 58}], "type": "validation", "color": "#E74C3C", "tag": "V23", "level": "line"}, {"id": "mapping-line-107", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Payment.status must be one of \"Pending\", \"Completed\", \"Failed\", \"Cancelled\"", "javaLines": [{"content": "        public enum PaymentStatus {", "lineIndex": 489}, {"content": "            Pending, Completed, Failed, Cancelled", "lineIndex": 490}, {"content": "        }", "lineIndex": 491}, {"content": "        public void setStatus(PaymentStatus status) {", "lineIndex": 586}, {"content": "            if (status == null) {", "lineIndex": 587}, {"content": "                throw new IllegalArgumentException(\"Invalid payment status\");", "lineIndex": 588}, {"content": "            }", "lineIndex": 589}, {"content": "            this.status = status;", "lineIndex": 590}, {"content": "        }", "lineIndex": 591}], "sqlLines": [{"content": "CREATE TYPE payment_status AS ENUM ('PENDING', 'COMPLETED', 'FAILED', 'REFUNDED');", "lineIndex": 4}, {"content": "    status payment_status NOT NULL DEFAULT 'PENDING',", "lineIndex": 59}], "type": "validation", "color": "#E74C3C", "tag": "V24", "level": "line"}, {"id": "mapping-line-108", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: paymentId Key: Primary Data Type: Integer Type: Mandatory Format: \"PMT-######\" Values: N/A Default: N/A Validation: Auto-increment Error Message: \"Invalid Payment ID format\" Description: Unique identifier for the payment", "javaLines": [{"content": "        private Long paymentId;", "lineIndex": 476}, {"content": "        public Long getPaymentId() {", "lineIndex": 530}, {"content": "            return paymentId;", "lineIndex": 531}, {"content": "        }", "lineIndex": 532}, {"content": "        public void setPaymentId(Long paymentId) {", "lineIndex": 534}, {"content": "            this.paymentId = paymentId;", "lineIndex": 535}, {"content": "        }", "lineIndex": 536}], "sqlLines": [{"content": "    payment_id SERIAL PRIMARY KEY,", "lineIndex": 55}], "type": "attribute", "color": "#3498DB", "tag": "A54", "level": "line"}, {"id": "mapping-line-109", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: loanId Key: Foreign Data Type: Integer Type: Mandatory Format: \"LN-######\" Values: N/A Default: N/A Validation: Foreign Key Check Error Message: \"Loan ID must reference an existing loan\" Description: References the loan this payment applies to", "javaLines": [{"content": "        private String loanId;", "lineIndex": 477}, {"content": "        public String getLoanId() {", "lineIndex": 538}, {"content": "            return loanId;", "lineIndex": 539}, {"content": "        }", "lineIndex": 540}, {"content": "        public void setLoanId(String loanId) {", "lineIndex": 542}, {"content": "            if (loanId == null || loanId.isEmpty()) {", "lineIndex": 543}, {"content": "                throw new IllegalArgumentException(\"Loan ID is required\");", "lineIndex": 544}, {"content": "            }", "lineIndex": 545}, {"content": "            this.loanId = loanId;", "lineIndex": 546}, {"content": "        }", "lineIndex": 547}], "sqlLines": [{"content": "    loan_id VARCHAR(12) NOT NULL REFERENCES loans(loan_id),", "lineIndex": 56}], "type": "attribute", "color": "#3498DB", "tag": "A55", "level": "line"}, {"id": "mapping-line-110", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: amount Key: Non-unique Data Type: Decimal Type: Mandatory Format: \"$#,###.00\" Values: 0.01-N Default: N/A Validation: Range Check Error Message: \"Payment amount must be greater than 0\" Description: Amount of the payment", "javaLines": [{"content": "        private static final BigDecimal MIN_AMOUNT = new BigDecimal(\"0.01\");", "lineIndex": 474}, {"content": "        private BigDecimal amount;", "lineIndex": 478}, {"content": "        public BigDecimal getAmount() {", "lineIndex": 549}, {"content": "            return amount;", "lineIndex": 550}, {"content": "        }", "lineIndex": 551}, {"content": "        public void setAmount(BigDecimal amount) {", "lineIndex": 553}, {"content": "            if (amount == null || amount.compareTo(MIN_AMOUNT) < 0) {", "lineIndex": 554}, {"content": "                throw new IllegalArgumentException(\"Payment amount must be greater than 0\");", "lineIndex": 555}, {"content": "            }", "lineIndex": 556}, {"content": "            this.amount = amount;", "lineIndex": 557}, {"content": "        }", "lineIndex": 558}], "sqlLines": [{"content": "    amount DECIMAL(12,2) NOT NULL CHECK (amount > 0),", "lineIndex": 57}], "type": "attribute", "color": "#3498DB", "tag": "A56", "level": "line"}, {"id": "mapping-line-111", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: paymentDate Key: Non-unique Data Type: Date Type: Mandatory Format: \"YYYY-MM-DD\" Values: N/A Default: \"CURRENT_DATE\" Validation: Date Check Error Message: \"Payment date is required\" Description: Date when the payment was made", "javaLines": [{"content": "        private LocalDate paymentDate;", "lineIndex": 479}, {"content": "        public LocalDate getPaymentDate() {", "lineIndex": 560}, {"content": "            return paymentDate;", "lineIndex": 561}, {"content": "        }", "lineIndex": 562}, {"content": "        public void setPaymentDate(LocalDate paymentDate) {", "lineIndex": 564}, {"content": "            if (paymentDate == null) {", "lineIndex": 565}, {"content": "                throw new IllegalArgumentException(\"Payment date is required\");", "lineIndex": 566}, {"content": "            }", "lineIndex": 567}, {"content": "            this.paymentDate = paymentDate;", "lineIndex": 568}, {"content": "        }", "lineIndex": 569}], "sqlLines": [{"content": "    payment_date TIMESTAMP NOT NULL,", "lineIndex": 58}], "type": "attribute", "color": "#3498DB", "tag": "A57", "level": "line"}, {"id": "mapping-line-112", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: dueDate Key: Non-unique Data Type: Date Type: Mandatory Format: \"YYYY-MM-DD\" Values: N/A Default: N/A Validation: Date Check Error Message: \"Due date is required\" Description: Date when the payment is due", "javaLines": [{"content": "        private LocalDate dueDate;", "lineIndex": 480}, {"content": "        public LocalDate getDueDate() {", "lineIndex": 571}, {"content": "            return dueDate;", "lineIndex": 572}, {"content": "        }", "lineIndex": 573}, {"content": "        public void setDueDate(LocalDate dueDate) {", "lineIndex": 575}, {"content": "            if (dueDate == null) {", "lineIndex": 576}, {"content": "                throw new IllegalArgumentException(\"Due date is required\");", "lineIndex": 577}, {"content": "            }", "lineIndex": 578}, {"content": "            this.dueDate = dueDate;", "lineIndex": 579}, {"content": "        }", "lineIndex": 580}], "sqlLines": [], "type": "attribute", "color": "#3498DB", "tag": "A58", "level": "line"}, {"id": "mapping-line-113", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: status Key: Non-unique Data Type: Enum Type: Mandatory Format: N/A Values: \"Pending\", \"Completed\", \"Failed\", \"Cancelled\" Default: \"Pending\" Validation: List Check Error Message: \"Invalid payment status\" Description: Current status of the payment", "javaLines": [{"content": "        public enum PaymentStatus {", "lineIndex": 489}, {"content": "            Pending, Completed, Failed, Cancelled", "lineIndex": 490}, {"content": "        }", "lineIndex": 491}, {"content": "        private PaymentStatus status;", "lineIndex": 481}, {"content": "        public PaymentStatus getStatus() {", "lineIndex": 582}, {"content": "            return status;", "lineIndex": 583}, {"content": "        }", "lineIndex": 584}, {"content": "        public void setStatus(PaymentStatus status) {", "lineIndex": 586}, {"content": "            if (status == null) {", "lineIndex": 587}, {"content": "                throw new IllegalArgumentException(\"Invalid payment status\");", "lineIndex": 588}, {"content": "            }", "lineIndex": 589}, {"content": "            this.status = status;", "lineIndex": 590}, {"content": "        }", "lineIndex": 591}], "sqlLines": [{"content": "CREATE TYPE payment_status AS ENUM ('PENDING', 'COMPLETED', 'FAILED', 'REFUNDED');", "lineIndex": 4}, {"content": "    status payment_status NOT NULL DEFAULT 'PENDING',", "lineIndex": 59}], "type": "attribute", "color": "#3498DB", "tag": "A59", "level": "line"}, {"id": "mapping-line-114", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: paymentMethod Key: Non-unique Data Type: Enum Type: Mandatory Format: N/A Values: \"CreditCard\", \"DebitCard\", \"BankTransfer\", \"Cash\", \"Check\", \"AutoDebit\" Default: \"AutoDebit\" Validation: List Check Error Message: \"Invalid payment method\" Description: Method used for payment", "javaLines": [{"content": "        public enum PaymentMethod {", "lineIndex": 493}, {"content": "            CreditCard, DebitCard, BankTransfer, Cash, Check, AutoDebit", "lineIndex": 494}, {"content": "        }", "lineIndex": 495}, {"content": "        private PaymentMethod paymentMethod;", "lineIndex": 482}, {"content": "        public PaymentMethod getPaymentMethod() {", "lineIndex": 593}, {"content": "            return paymentMethod;", "lineIndex": 594}, {"content": "        }", "lineIndex": 595}, {"content": "        public void setPaymentMethod(PaymentMethod paymentMethod) {", "lineIndex": 597}, {"content": "            if (paymentMethod == null) {", "lineIndex": 598}, {"content": "                throw new IllegalArgumentException(\"Payment method cannot be null\");", "lineIndex": 599}, {"content": "            }", "lineIndex": 600}, {"content": "            this.paymentMethod = paymentMethod;", "lineIndex": 601}, {"content": "        }", "lineIndex": 602}], "sqlLines": [{"content": "    payment_method VARCHAR(50) NOT NULL,", "lineIndex": 60}], "type": "attribute", "color": "#3498DB", "tag": "A60", "level": "line"}, {"id": "mapping-line-115", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: referenceNumber Key: Non-unique Data Type: String Type: Optional Format: N/A Values: N/A Default: N/A Validation: Length Check Error Message: N/A Description: External reference number for the payment", "javaLines": [{"content": "        private String referenceNumber;", "lineIndex": 483}, {"content": "        public String getReferenceNumber() {", "lineIndex": 604}, {"content": "            return referenceNumber;", "lineIndex": 605}, {"content": "        }", "lineIndex": 606}, {"content": "        public void setReferenceNumber(String referenceNumber) {", "lineIndex": 608}, {"content": "            this.referenceNumber = referenceNumber;", "lineIndex": 609}, {"content": "        }", "lineIndex": 610}], "sqlLines": [{"content": "    reference_number VARCHAR(100),", "lineIndex": 61}], "type": "attribute", "color": "#3498DB", "tag": "A61", "level": "line"}, {"id": "mapping-line-116", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: lateFee Key: Non-unique Data Type: Decimal Type: Optional Format: \"$#,###.00\" Values: 0.00-N Default: \"0.00\" Validation: Range Check Error Message: \"Late fee cannot be negative\" Description: Additional fee applied for late payments", "javaLines": [{"content": "        private BigDecimal lateFee;", "lineIndex": 484}, {"content": "        public BigDecimal getLateFee() {", "lineIndex": 612}, {"content": "            return lateFee;", "lineIndex": 613}, {"content": "        }", "lineIndex": 614}, {"content": "        public void setLateFee(BigDecimal lateFee) {", "lineIndex": 616}, {"content": "            this.lateFee = lateFee;", "lineIndex": 617}, {"content": "        }", "lineIndex": 618}], "sqlLines": [], "type": "attribute", "color": "#3498DB", "tag": "A62", "level": "line"}, {"id": "mapping-line-117", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: paymentType Key: Non-unique Data Type: Enum Type: Mandatory Format: N/A Values: \"Regular\", \"ExtraPayment\", \"LatePayment\", \"InterestOnly\", \"PrincipalOnly\" Default: \"Regular\" Validation: List Check Error Message: \"Invalid payment type\" Description: Type of payment being made", "javaLines": [{"content": "        public enum PaymentType {", "lineIndex": 497}, {"content": "            Regular, ExtraPayment, LatePayment, InterestOnly, PrincipalOnly", "lineIndex": 498}, {"content": "        }", "lineIndex": 499}, {"content": "        private PaymentType paymentType;", "lineIndex": 485}, {"content": "        public PaymentType getPaymentType() {", "lineIndex": 620}, {"content": "            return paymentType;", "lineIndex": 621}, {"content": "        }", "lineIndex": 622}, {"content": "        public void setPaymentType(PaymentType paymentType) {", "lineIndex": 624}, {"content": "            if (paymentType == null) {", "lineIndex": 625}, {"content": "                throw new IllegalArgumentException(\"Invalid payment type\");", "lineIndex": 626}, {"content": "            }", "lineIndex": 627}, {"content": "            this.paymentType = paymentType;", "lineIndex": 628}, {"content": "        }", "lineIndex": 629}], "sqlLines": [], "type": "attribute", "color": "#3498DB", "tag": "A63", "level": "line"}, {"id": "mapping-line-118", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Attribute Additional Properties: Attribute name: notes Key: Non-unique Data Type: String Type: Optional Format: N/A Values: N/A Default: N/A Validation: Length Check Error Message: N/A Description: Additional notes about the payment", "javaLines": [{"content": "        private String notes;", "lineIndex": 486}, {"content": "        public String getNotes() {", "lineIndex": 631}, {"content": "            return notes;", "lineIndex": 632}, {"content": "        }", "lineIndex": 633}, {"content": "        public void setNotes(String notes) {", "lineIndex": 635}, {"content": "            this.notes = notes;", "lineIndex": 636}, {"content": "        }", "lineIndex": 637}], "sqlLines": [], "type": "attribute", "color": "#3498DB", "tag": "A64", "level": "line"}, {"id": "mapping-line-119", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Relationship: Payment to <PERSON>an", "javaLines": [{"content": "        private Loan loan;", "lineIndex": 487}, {"content": "        public Loan getLoan() {", "lineIndex": 639}, {"content": "            return loan;", "lineIndex": 640}, {"content": "        }", "lineIndex": 641}, {"content": "        public void setLoan(Loan loan) {", "lineIndex": 643}, {"content": "            this.loan = loan;", "lineIndex": 644}, {"content": "            if (loan != null) {", "lineIndex": 645}, {"content": "                this.loanId = loan.getLoanId();", "lineIndex": 646}, {"content": "            }", "lineIndex": 647}, {"content": "        }", "lineIndex": 648}], "sqlLines": [{"content": "    loan_id VARCHAR(12) NOT NULL REFERENCES loans(loan_id),", "lineIndex": 56}], "type": "relationship", "color": "#9B59B6", "tag": "R19", "level": "line"}, {"id": "mapping-line-120", "solutionId": "e50b7db0-cac7-4a66-9b5c-42fb1fd94185", "nlpLine": "Relationship Properties: On Delete: Cascade (Remove payments when loan is deleted) On Update: Cascade (Update payment records when loan details change) Foreign Key Type: Non-Nullable", "javaLines": [], "sqlLines": [{"content": "    loan_id VARCHAR(12) NOT NULL REFERENCES loans(loan_id),", "lineIndex": 56}], "type": "relationship_property", "color": "#8E44AD", "tag": "RP6", "level": "line"}], "javaKeywords": {}}